/* Main CSS file - imports all other stylesheets */
@import url('variables.css');
@import url('base.css');
@import url('components.css');
@import url('layout.css');

/* Page-specific styles */

/* Knowledge Base Page */
.knowledge-base-tabs {
  display: flex;
  border-bottom: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-lg);
}

.tab-button {
  background: none;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all var(--transition-fast);
  font-size: var(--font-size-base);
  color: var(--gray-600);
}

.tab-button:hover {
  color: var(--primary-color);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Search and Filter Section */
.search-filters {
  background-color: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.filter-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

/* Enhanced Filter Layout with Grouping */
.filter-section {
  margin-bottom: var(--spacing-lg);
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-section-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  margin-bottom: var(--spacing-sm);
  padding-left: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-groups {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.filter-group {
  background-color: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  position: relative;
}

.filter-group-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-800);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--gray-300);
  display: flex;
  align-items: center;
}

.filter-group-title::before {
  content: '';
  width: 3px;
  height: 16px;
  background-color: var(--primary-color);
  margin-right: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

.platform-group .filter-group-title::before {
  background-color: var(--blue-500);
}

.vendor-group .filter-group-title::before {
  background-color: var(--green-500);
}

.filter-group-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--spacing-md);
}

/* Single field sections */
.filter-single {
  margin-bottom: var(--spacing-md);
}

.filter-single .form-group {
  margin-bottom: 0;
}

/* Data Table */
.data-table-container {
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background-color: var(--gray-50);
  padding: var(--spacing-md);
  text-align: left;
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  border-bottom: 1px solid var(--gray-200);
}

.data-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-100);
  vertical-align: middle;
}

.data-table tbody tr:hover {
  background-color: var(--gray-50);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-xs);
}

.action-buttons .btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background-color: var(--white);
  border-top: 1px solid var(--gray-200);
}

.pagination-info {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin-right: auto;
}

.pagination-controls {
  display: flex;
  gap: var(--spacing-xs);
}

.pagination-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--gray-300);
  background-color: var(--white);
  color: var(--gray-700);
  text-decoration: none;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
}

.pagination-btn:hover {
  background-color: var(--gray-100);
  text-decoration: none;
}

.pagination-btn.active {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* File Upload Component */
.file-upload-area {
  border: 2px dashed var(--gray-300);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  transition: border-color var(--transition-fast);
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: var(--primary-color);
}

.file-upload-area.dragover {
  border-color: var(--primary-color);
  background-color: rgba(25, 118, 210, 0.05);
}

.file-upload-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--spacing-md);
  color: var(--gray-400);
}

.file-upload-text {
  font-size: var(--font-size-lg);
  color: var(--gray-700);
  margin-bottom: var(--spacing-sm);
}

.file-upload-hint {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

/* Document Display Cards */
.document-cards {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.document-card {
  background-color: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-fast);
  width: 100%;
}

.document-card:hover {
  box-shadow: var(--shadow-md);
}

.document-card-header {
  padding: var(--spacing-md);
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.document-type {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
  background-color: rgba(25, 118, 210, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

.document-card-body {
  padding: var(--spacing-md);
}

.document-content {
  max-height: 300px;
  overflow-y: auto;
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  background-color: var(--gray-50);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  white-space: pre-wrap;
  word-break: break-word;
  border: 1px solid var(--gray-200);
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--gray-300) var(--gray-100);
}

/* Webkit scrollbar styling */
.document-content::-webkit-scrollbar {
  width: 8px;
}

.document-content::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--border-radius-sm);
}

.document-content::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--border-radius-sm);
  transition: background-color var(--transition-fast);
}

.document-content::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Responsive adjustments for document content */
@media (max-width: 768px) {
  .document-content {
    max-height: 200px;
    font-size: var(--font-size-xs);
    padding: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .document-content {
    max-height: 150px;
  }
}

.document-actions {
  padding: var(--spacing-md);
  border-top: 1px solid var(--gray-200);
  text-align: right;
}

/* JSON Tree Selector */
.json-tree-selector {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal);
  display: none;
}

.json-tree-selector.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.json-tree-panel {
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.json-tree-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.json-tree-search {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
}

.json-tree-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
}

.json-tree {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
}

.json-node {
  margin-left: var(--spacing-md);
  padding: var(--spacing-xs) 0;
}

.json-node-toggle {
  background: none;
  border: none;
  padding: 0;
  margin-right: var(--spacing-xs);
  cursor: pointer;
  color: var(--gray-500);
}

.json-node-checkbox {
  margin-right: var(--spacing-xs);
}

.json-node-key {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.json-node-type {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
  margin-left: var(--spacing-xs);
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-300);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: var(--spacing-3xl);
  color: var(--gray-500);
}

.empty-state-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-lg);
  color: var(--gray-300);
}

.empty-state-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  margin-bottom: var(--spacing-sm);
}

.empty-state-description {
  font-size: var(--font-size-base);
  color: var(--gray-500);
  margin-bottom: var(--spacing-lg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-row {
    grid-template-columns: 1fr;
  }

  .filter-actions {
    justify-content: stretch;
  }

  .filter-actions .btn {
    flex: 1;
  }

  .document-cards {
    /* 垂直布局在移动端保持不变 */
  }

  .action-buttons {
    flex-direction: column;
  }

  .pagination {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .pagination-info {
    margin-right: 0;
    text-align: center;
  }

  /* Enhanced filter responsive design */
  .filter-groups {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .filter-group-content {
    grid-template-columns: 1fr;
  }

  .filter-group {
    padding: var(--spacing-sm);
  }

  .filter-group-title {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .search-filters {
    padding: var(--spacing-md);
  }

  .filter-section-title {
    font-size: var(--font-size-xs);
  }

  .filter-group {
    padding: var(--spacing-xs);
  }
}
