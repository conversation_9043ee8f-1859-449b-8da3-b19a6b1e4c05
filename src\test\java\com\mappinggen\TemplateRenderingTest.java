package com.mappinggen;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 测试模板渲染是否正常工作
 */
@SpringBootTest
@AutoConfigureWebMvc
public class TemplateRenderingTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Test
    public void testHomePageRenders() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        mockMvc.perform(get("/"))
                .andExpect(status().isOk())
                .andExpect(view().name("layout/base"))
                .andExpect(model().attributeExists("pageTitle"))
                .andExpect(model().attributeExists("currentPath"));
    }

    @Test
    public void testScenarioPageRenders() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        mockMvc.perform(get("/scenario/create"))
                .andExpect(status().isOk())
                .andExpect(model().attributeExists("currentPath"))
                .andExpect(model().attribute("currentPath", "/scenario/create"));
    }
}
