package com.mappinggen.dto;

import org.springframework.web.multipart.MultipartFile;

public class ScenarioStep2DTO {
    
    private String airlineInterfaceName;
    private String airlineRequestExample;
    private String airlineResponseExample;
    private String airlineRequestDoc;
    private String airlineResponseDoc;
    
    // File uploads
    private MultipartFile requestExampleFile;
    private MultipartFile responseExampleFile;
    private MultipartFile requestDocFile;
    private MultipartFile responseDocFile;
    
    // Constructors
    public ScenarioStep2DTO() {}
    
    // Getters and Setters
    public String getAirlineInterfaceName() { return airlineInterfaceName; }
    public void setAirlineInterfaceName(String airlineInterfaceName) { this.airlineInterfaceName = airlineInterfaceName; }
    
    public String getAirlineRequestExample() { return airlineRequestExample; }
    public void setAirlineRequestExample(String airlineRequestExample) { this.airlineRequestExample = airlineRequestExample; }
    
    public String getAirlineResponseExample() { return airlineResponseExample; }
    public void setAirlineResponseExample(String airlineResponseExample) { this.airlineResponseExample = airlineResponseExample; }
    
    public String getAirlineRequestDoc() { return airlineRequestDoc; }
    public void setAirlineRequestDoc(String airlineRequestDoc) { this.airlineRequestDoc = airlineRequestDoc; }
    
    public String getAirlineResponseDoc() { return airlineResponseDoc; }
    public void setAirlineResponseDoc(String airlineResponseDoc) { this.airlineResponseDoc = airlineResponseDoc; }
    
    public MultipartFile getRequestExampleFile() { return requestExampleFile; }
    public void setRequestExampleFile(MultipartFile requestExampleFile) { this.requestExampleFile = requestExampleFile; }
    
    public MultipartFile getResponseExampleFile() { return responseExampleFile; }
    public void setResponseExampleFile(MultipartFile responseExampleFile) { this.responseExampleFile = responseExampleFile; }
    
    public MultipartFile getRequestDocFile() { return requestDocFile; }
    public void setRequestDocFile(MultipartFile requestDocFile) { this.requestDocFile = requestDocFile; }
    
    public MultipartFile getResponseDocFile() { return responseDocFile; }
    public void setResponseDocFile(MultipartFile responseDocFile) { this.responseDocFile = responseDocFile; }
}
