package com.mappinggen.controller.api;

import com.mappinggen.dto.ApiResponse;
import com.mappinggen.model.FieldMapping;
import com.mappinggen.model.FieldMappingRule;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;

/**
 * REST API controller for Field Mapping operations
 */
@RestController
@RequestMapping("/old/api/field-mapping")
public class FieldMappingApiController {

    // Mock data storage
    private static final Map<Long, FieldMapping> fieldMappingStorage = new HashMap<>();
    private static Long nextId = 1L;

    static {
        initializeMockData();
    }

    private static void initializeMockData() {
        // Flight Search - 东方航空
        FieldMapping mapping1 = new FieldMapping(1L, "航班搜索");
        mapping1.setId(nextId++);
        mapping1.setPlatformInterface("FlightSearch");
        mapping1.setPlatformVersion("v1.0");
        mapping1.setVendor("东方航空");
        mapping1.setVendorInterface("FlightSearch");
        mapping1.setVendorVersion("v1.0");
        mapping1.setGeneralLogic("东方航空航班搜索接口映射：将IFS标准字段映射到东航特定字段格式，包含机场代码转换、时间格式标准化、价格单位转换等");

        List<FieldMappingRule> rules1 = Arrays.asList(
            new FieldMappingRule("departureAirport", "response.flightList[i].depAirport", "IATA机场代码转换"),
            new FieldMappingRule("arrivalAirport", "response.flightList[i].arrAirport", "IATA机场代码转换"),
            new FieldMappingRule("flightNumber", "response.flightList[i].flightNo", "航班号格式化"),
            new FieldMappingRule("departureTime", "response.flightList[i].depTime", "时间格式转换 yyyy-MM-dd HH:mm"),
            new FieldMappingRule("arrivalTime", "response.flightList[i].arrTime", "时间格式转换 yyyy-MM-dd HH:mm"),
            new FieldMappingRule("price", "response.flightList[i].fareInfo.totalPrice", "价格转换为分，乘以100"),
            new FieldMappingRule("currency", "response.flightList[i].fareInfo.currency", "货币代码标准化"),
            new FieldMappingRule("aircraftType", "response.flightList[i].aircraft", "机型代码转换"),
            new FieldMappingRule("duration", "response.flightList[i].flightTime", "飞行时长分钟转换"),
            new FieldMappingRule("availableSeats", "response.flightList[i].seatCount", "可用座位数")
        );

        // Set detailed mapping rules
        rules1.get(0).setMappingRule("departureAirport = convertAirportCode(response.flightList[i].depAirport)");
        rules1.get(1).setMappingRule("arrivalAirport = convertAirportCode(response.flightList[i].arrAirport)");
        rules1.get(2).setMappingRule("flightNumber = formatFlightNumber(response.flightList[i].flightNo)");
        rules1.get(3).setMappingRule("departureTime = formatDateTime(response.flightList[i].depTime, 'yyyy-MM-dd HH:mm')");
        rules1.get(4).setMappingRule("arrivalTime = formatDateTime(response.flightList[i].arrTime, 'yyyy-MM-dd HH:mm')");
        rules1.get(5).setMappingRule("price = Math.round(response.flightList[i].fareInfo.totalPrice * 100)");

        mapping1.setMappingRules(rules1);
        mapping1.setCreateTime(LocalDateTime.now().minusDays(12));
        fieldMappingStorage.put(mapping1.getId(), mapping1);

        // Flight Search - 南方航空
        FieldMapping mapping2 = new FieldMapping(1L, "航班搜索");
        mapping2.setId(nextId++);
        mapping2.setPlatformInterface("FlightSearch");
        mapping2.setPlatformVersion("v1.0");
        mapping2.setVendor("南方航空");
        mapping2.setVendorInterface("FlightSearch");
        mapping2.setVendorVersion("v1.0");
        mapping2.setGeneralLogic("南方航空航班搜索接口映射：处理南航特有的响应格式，包含舱位等级映射、联程航班处理等");

        List<FieldMappingRule> rules2 = Arrays.asList(
            new FieldMappingRule("departureAirport", "response.data.flights[i].origin", "起飞机场代码"),
            new FieldMappingRule("arrivalAirport", "response.data.flights[i].destination", "到达机场代码"),
            new FieldMappingRule("flightNumber", "response.data.flights[i].flight_number", "航班号"),
            new FieldMappingRule("departureTime", "response.data.flights[i].departure_time", "起飞时间"),
            new FieldMappingRule("arrivalTime", "response.data.flights[i].arrival_time", "到达时间"),
            new FieldMappingRule("price", "response.data.flights[i].pricing.base_fare", "基础票价"),
            new FieldMappingRule("cabinClass", "response.data.flights[i].cabin_class", "舱位等级映射"),
            new FieldMappingRule("stops", "response.data.flights[i].stops", "经停次数"),
            new FieldMappingRule("operatingCarrier", "response.data.flights[i].operating_carrier", "实际承运人")
        );

        rules2.get(0).setMappingRule("departureAirport = response.data.flights[i].origin");
        rules2.get(1).setMappingRule("arrivalAirport = response.data.flights[i].destination");
        rules2.get(2).setMappingRule("flightNumber = response.data.flights[i].flight_number");
        rules2.get(6).setMappingRule("cabinClass = mapCabinClass(response.data.flights[i].cabin_class)");

        mapping2.setMappingRules(rules2);
        mapping2.setCreateTime(LocalDateTime.now().minusDays(11));
        fieldMappingStorage.put(mapping2.getId(), mapping2);

        // Order Management - 东方航空
        FieldMapping mapping3 = new FieldMapping(5L, "订单管理");
        mapping3.setId(nextId++);
        mapping3.setPlatformInterface("OrderManagement");
        mapping3.setPlatformVersion("v2.0");
        mapping3.setVendor("东方航空");
        mapping3.setVendorInterface("OrderProcess");
        mapping3.setVendorVersion("v1.5");
        mapping3.setGeneralLogic("东方航空订单管理接口映射：处理订单创建、修改、取消等操作的字段映射");

        List<FieldMappingRule> rules3 = Arrays.asList(
            new FieldMappingRule("orderId", "response.orderInfo.orderId", "订单ID"),
            new FieldMappingRule("orderStatus", "response.orderInfo.status", "订单状态映射"),
            new FieldMappingRule("passengerInfo", "response.orderInfo.passengers", "乘客信息数组"),
            new FieldMappingRule("totalAmount", "response.orderInfo.payment.totalAmount", "订单总金额"),
            new FieldMappingRule("createTime", "response.orderInfo.createTime", "订单创建时间"),
            new FieldMappingRule("paymentStatus", "response.orderInfo.payment.status", "支付状态"),
            new FieldMappingRule("ticketNumbers", "response.orderInfo.tickets", "票号列表")
        );

        rules3.get(1).setMappingRule("orderStatus = mapOrderStatus(response.orderInfo.status)");
        rules3.get(3).setMappingRule("totalAmount = response.orderInfo.payment.totalAmount * 100");

        mapping3.setMappingRules(rules3);
        mapping3.setCreateTime(LocalDateTime.now().minusDays(6));
        fieldMappingStorage.put(mapping3.getId(), mapping3);
    }

    // Dropdown endpoints removed - now using unified /api/init-data endpoint

    @GetMapping("/json-sample")
    public ApiResponse<Map<String, Object>> getJsonSample() {
        Map<String, Object> sampleData = new HashMap<>();
        
        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> flights = new ArrayList<>();
        
        Map<String, Object> flight1 = new HashMap<>();
        flight1.put("flightNumber", "MU123");
        
        Map<String, Object> departure = new HashMap<>();
        departure.put("airport", "PEK");
        departure.put("time", "2024-01-01T10:00:00");
        departure.put("terminal", "T3");
        flight1.put("departure", departure);
        
        Map<String, Object> arrival = new HashMap<>();
        arrival.put("airport", "SHA");
        arrival.put("time", "2024-01-01T12:00:00");
        arrival.put("terminal", "T2");
        flight1.put("arrival", arrival);
        
        Map<String, Object> price = new HashMap<>();
        price.put("amount", 1200);
        price.put("currency", "CNY");
        price.put("tax", 120);
        flight1.put("price", price);
        
        Map<String, Object> aircraft = new HashMap<>();
        aircraft.put("type", "A320");
        aircraft.put("capacity", 180);
        flight1.put("aircraft", aircraft);
        
        flights.add(flight1);
        
        response.put("flights", flights);
        response.put("total", 1);
        response.put("hasMore", false);
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("requestId", "req-123456");
        metadata.put("timestamp", "2024-01-01T09:00:00");
        metadata.put("version", "v1.0");
        response.put("metadata", metadata);
        
        sampleData.put("response", response);
        
        return ApiResponse.success(sampleData);
    }

    @GetMapping
    public ApiResponse<FieldMapping> getFieldMapping(
            @RequestParam(required = false) String businessDomain,
            @RequestParam(required = false) String platformInterface,
            @RequestParam(required = false) String platformVersion,
            @RequestParam(required = false) String vendor,
            @RequestParam(required = false) String vendorInterface,
            @RequestParam(required = false) String vendorVersion) {

        // Find existing field mapping or create a new one
        FieldMapping mapping = fieldMappingStorage.values().stream()
                .filter(fm -> Objects.equals(businessDomain, fm.getBusinessDomain()))
                .filter(fm -> Objects.equals(platformInterface, fm.getPlatformInterface()))
                .filter(fm -> Objects.equals(vendor, fm.getVendor()))
                .findFirst()
                .orElse(createNewFieldMapping(businessDomain, platformInterface, platformVersion, vendor, vendorInterface, vendorVersion));

        return ApiResponse.success(mapping);
    }

    @GetMapping("/association/{associationId}")
    public ApiResponse<FieldMapping> getFieldMappingByAssociation(@PathVariable Long associationId) {
        // Mock: find field mapping by association ID
        FieldMapping mapping = fieldMappingStorage.values().stream()
                .filter(fm -> Objects.equals(associationId, fm.getMappingAssociationId()))
                .findFirst()
                .orElse(fieldMappingStorage.get(1L)); // Return first mapping as fallback

        return ApiResponse.success(mapping);
    }

    @PostMapping("/{id}/generate-mapping")
    public ApiResponse<Map<String, Object>> generateMapping(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        FieldMapping mapping = fieldMappingStorage.get(id);
        if (mapping == null) {
            return ApiResponse.error("字段映射不存在");
        }

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> mappingRules = (List<Map<String, Object>>) request.get("mappingRules");
        
        // Generate mapping rules based on airline response results
        for (Map<String, Object> rule : mappingRules) {
            String ifsField = (String) rule.get("ifsField");
            String airlineResponse = (String) rule.get("airlineResponseResult");
            String supplementaryLogic = (String) rule.get("supplementaryLogic");
            
            if (airlineResponse != null && !airlineResponse.trim().isEmpty()) {
                String mappingRule = generateMappingRule(ifsField, airlineResponse, supplementaryLogic);
                rule.put("mappingRule", mappingRule);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("mappingRules", mappingRules);
        
        return ApiResponse.success("映射规则生成成功", result);
    }

    @PostMapping("/{id}/generate-code-prompt")
    public ApiResponse<Map<String, Object>> generateCodePrompt(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        FieldMapping mapping = fieldMappingStorage.get(id);
        if (mapping == null) {
            return ApiResponse.error("字段映射不存在");
        }

        // Mock: generate code prompt ID
        Long codePromptId = System.currentTimeMillis(); // Use timestamp as mock ID
        
        Map<String, Object> result = new HashMap<>();
        result.put("id", codePromptId);
        result.put("message", "CodePrompt 生成成功");
        
        return ApiResponse.success("CodePrompt 生成成功", result);
    }

    @PutMapping("/{id}")
    public ApiResponse<Void> updateFieldMapping(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        FieldMapping mapping = fieldMappingStorage.get(id);
        if (mapping == null) {
            return ApiResponse.error("字段映射不存在");
        }

        String generalLogic = (String) request.get("generalLogic");
        if (generalLogic != null) {
            mapping.setGeneralLogic(generalLogic);
        }

        mapping.setUpdateTime(LocalDateTime.now());
        
        return ApiResponse.success("暂存成功");
    }

    private FieldMapping createNewFieldMapping(String businessDomain, String platformInterface, 
                                             String platformVersion, String vendor, 
                                             String vendorInterface, String vendorVersion) {
        FieldMapping mapping = new FieldMapping(null, businessDomain);
        mapping.setId(nextId++);
        mapping.setPlatformInterface(platformInterface);
        mapping.setPlatformVersion(platformVersion);
        mapping.setVendor(vendor);
        mapping.setVendorInterface(vendorInterface);
        mapping.setVendorVersion(vendorVersion);

        // Create default IFS fields based on business domain
        List<FieldMappingRule> defaultRules = createDefaultMappingRules(businessDomain);
        mapping.setMappingRules(defaultRules);

        fieldMappingStorage.put(mapping.getId(), mapping);
        return mapping;
    }

    private List<FieldMappingRule> createDefaultMappingRules(String businessDomain) {
        List<FieldMappingRule> rules = new ArrayList<>();
        
        if ("航班搜索".equals(businessDomain)) {
            rules.add(new FieldMappingRule("departureAirport"));
            rules.add(new FieldMappingRule("arrivalAirport"));
            rules.add(new FieldMappingRule("flightNumber"));
            rules.add(new FieldMappingRule("departureTime"));
            rules.add(new FieldMappingRule("arrivalTime"));
            rules.add(new FieldMappingRule("price"));
            rules.add(new FieldMappingRule("currency"));
            rules.add(new FieldMappingRule("aircraftType"));
        } else if ("订单管理".equals(businessDomain)) {
            rules.add(new FieldMappingRule("orderId"));
            rules.add(new FieldMappingRule("orderStatus"));
            rules.add(new FieldMappingRule("totalAmount"));
            rules.add(new FieldMappingRule("createTime"));
            rules.add(new FieldMappingRule("customerId"));
        } else {
            // Default fields
            rules.add(new FieldMappingRule("id"));
            rules.add(new FieldMappingRule("name"));
            rules.add(new FieldMappingRule("status"));
            rules.add(new FieldMappingRule("createTime"));
        }
        
        return rules;
    }

    private String generateMappingRule(String ifsField, String airlineResponse, String supplementaryLogic) {
        StringBuilder rule = new StringBuilder();
        rule.append(ifsField).append(" = ").append(airlineResponse);
        
        if (supplementaryLogic != null && !supplementaryLogic.trim().isEmpty()) {
            rule.append("\n// ").append(supplementaryLogic);
        }
        
        // Add some mock transformation logic based on field type
        if (ifsField.toLowerCase().contains("time")) {
            rule.append("\n// 时间格式转换: ISO 8601 -> yyyy-MM-dd HH:mm:ss");
        } else if (ifsField.toLowerCase().contains("price") || ifsField.toLowerCase().contains("amount")) {
            rule.append("\n// 价格转换: 元 -> 分 (乘以100)");
        } else if (ifsField.toLowerCase().contains("airport")) {
            rule.append("\n// 机场代码标准化: IATA 三字码");
        }
        
        return rule.toString();
    }
}
