package com.mappinggen.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Home controller for main navigation and page routing
 * Note: All redirects use relative paths to work correctly with the context path
 */
@Controller
public class HomeController {

    @GetMapping("/")
    public String home() {
        // Use relative redirect to avoid context path issues
        return "redirect:knowledge-base";
    }

    @GetMapping("/knowledge-base")
    public String knowledgeBase(@RequestParam(required = false) String type, Model model) {
        // Add any necessary model attributes for the knowledge base page
        model.addAttribute("activeTab", type != null ? type : "platform");
        // Add current page identifier for navigation highlighting
        model.addAttribute("currentPage", "knowledge-base");

        // Add empty collections for dropdown components (will be populated by JavaScript)
        model.addAttribute("businessDomains", java.util.Collections.emptyList());
        model.addAttribute("interfaces", java.util.Collections.emptyList());
        model.addAttribute("versions", java.util.Collections.emptyList());
        model.addAttribute("vendors", java.util.Collections.emptyList());

        return "knowledge-base";
    }

    @GetMapping("/knowledge-base/documents/{id}")
    public String viewKnowledgeBaseDocuments(@PathVariable Long id, @RequestParam String type, Model model) {
        // This would typically load the specific knowledge base documents
        // For now, redirect to the main knowledge base page
        return "redirect:../knowledge-base?type=" + type;
    }

    @GetMapping("/mapping-association")
    public String mappingAssociation(Model model) {
        model.addAttribute("currentPage", "mapping-association");

        // Add empty collections for dropdown components (will be populated by JavaScript)
        model.addAttribute("businessDomains", java.util.Collections.emptyList());
        model.addAttribute("platformInterfaces", java.util.Collections.emptyList());
        model.addAttribute("platformVersions", java.util.Collections.emptyList());
        model.addAttribute("vendors", java.util.Collections.emptyList());
        model.addAttribute("vendorInterfaces", java.util.Collections.emptyList());
        model.addAttribute("vendorVersions", java.util.Collections.emptyList());

        return "mapping-association";
    }

    @GetMapping("/field-mapping")
    public String fieldMapping(Model model) {
        model.addAttribute("currentPage", "field-mapping");

        // Add empty collections for dropdown components (will be populated by JavaScript)
        model.addAttribute("businessDomains", java.util.Collections.emptyList());
        model.addAttribute("platformInterfaces", java.util.Collections.emptyList());
        model.addAttribute("platformVersions", java.util.Collections.emptyList());
        model.addAttribute("vendors", java.util.Collections.emptyList());
        model.addAttribute("vendorInterfaces", java.util.Collections.emptyList());
        model.addAttribute("vendorVersions", java.util.Collections.emptyList());

        return "field-mapping";
    }

    @GetMapping("/code-prompt")
    public String codePrompt(Model model) {
        model.addAttribute("currentPage", "code-prompt");

        // Add empty collections for dropdown components (will be populated by JavaScript)
        model.addAttribute("businessDomains", java.util.Collections.emptyList());
        model.addAttribute("platformInterfaces", java.util.Collections.emptyList());
        model.addAttribute("platformVersions", java.util.Collections.emptyList());
        model.addAttribute("vendors", java.util.Collections.emptyList());
        model.addAttribute("vendorInterfaces", java.util.Collections.emptyList());
        model.addAttribute("vendorVersions", java.util.Collections.emptyList());
        model.addAttribute("promptVersions", java.util.Collections.emptyList());

        return "code-prompt";
    }
}
