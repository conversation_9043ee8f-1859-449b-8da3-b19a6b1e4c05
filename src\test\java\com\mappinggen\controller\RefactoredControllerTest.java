package com.mappinggen.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 测试重构后的Controller接口
 */
@SpringBootTest
@AutoConfigureWebMvc
public class RefactoredControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Test
    public void testScenarioInitAPI() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        mockMvc.perform(get("/scenario/api/init"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.businessDomains").isArray())
                .andExpect(jsonPath("$.data.airlines").isArray())
                .andExpect(jsonPath("$.data.interfaces").isArray());
    }

    @Test
    public void testScenarioValidateStep1() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        String requestBody = "{\"businessDomain\":\"ifs\",\"airline\":\"CA\",\"interfaceType\":\"AV\",\"interfaceVersion\":\"V2.0\"}";
        
        mockMvc.perform(post("/scenario/api/validate-step1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.exists").value(true));
    }

    @Test
    public void testScenarioStep2Data() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        String requestBody = "{\"businessDomain\":\"ifs\",\"airline\":\"CZ\",\"interfaceType\":\"AV\",\"interfaceVersion\":\"V1.0\"}";
        
        mockMvc.perform(post("/scenario/api/step2-data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.sampleXml").exists())
                .andExpect(jsonPath("$.data.xmlStructure").isArray())
                .andExpect(jsonPath("$.data.fieldMappings").isArray());
    }

    @Test
    public void testMappingInitAPI() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        mockMvc.perform(get("/mapping/api/init"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.businessDomains").isArray())
                .andExpect(jsonPath("$.data.airlines").isArray());
    }

    @Test
    public void testMappingQuery() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        mockMvc.perform(get("/mapping/api/query")
                .param("businessDomain", "ifs")
                .param("airline", "CA")
                .param("interfaceType", "AV")
                .param("interfaceVersion", "V2.0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.mappings").isArray())
                .andExpect(jsonPath("$.data.total").exists())
                .andExpect(jsonPath("$.data.xmlTree").exists());
    }

    @Test
    public void testMappingGenerate() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        String requestBody = "{\"fieldMappings\":[{\"source\":\"messageId\",\"target\":\"header.msgId\"}]}";
        
        mockMvc.perform(post("/mapping/api/generate")
                .param("mappingId", "1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.mappingRules").exists())
                .andExpect(jsonPath("$.data.mappingId").value(1));
    }

    @Test
    public void testCodePromptInitAPI() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        mockMvc.perform(get("/codeprompt/api/init"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.airlines").isArray())
                .andExpect(jsonPath("$.data.interfaces").isArray());
    }

    @Test
    public void testCodePromptQuery() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        mockMvc.perform(get("/codeprompt/api/query")
                .param("airline", "CA")
                .param("interfaceType", "AV")
                .param("interfaceVersion", "V2.0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.codePrompts").isArray())
                .andExpect(jsonPath("$.data.currentCodePrompt").exists());
    }

    @Test
    public void testCodePromptSave() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        String content = "# Test CodePrompt Content\nThis is a test content for request mapping.";
        
        mockMvc.perform(put("/codeprompt/api/save")
                .param("codePromptId", "1")
                .param("areaName", "request_mapping")
                .contentType(MediaType.TEXT_PLAIN)
                .content(content))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.codePromptId").value(1))
                .andExpect(jsonPath("$.data.areaName").value("request_mapping"));
    }

    @Test
    public void testCodePromptSaveAll() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        String requestBody = "{\"request_mapping\":\"Request mapping content\",\"response_mapping\":\"Response mapping content\"}";
        
        mockMvc.perform(put("/codeprompt/api/save-all")
                .param("codePromptId", "1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalAreas").value(2));
    }
}
