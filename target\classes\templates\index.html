<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('首页')}"></head>
<body>
    <div class="app-layout">
        <div th:replace="~{fragments/layout :: sidebar}"></div>
        <div class="app-main">
            <div th:replace="~{fragments/layout :: header}"></div>
            <main class="app-content">
                <!-- Page Header -->
                <div class="page-header">
                    <h1 class="page-title">欢迎使用映射生成器</h1>
                    <p class="page-subtitle">企业级映射管理平台，提供完整的接口映射解决方案</p>
                </div>
                
                <!-- Feature Cards -->
                <div class="row">
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <svg class="icon mb-3" style="width: 48px; height: 48px; color: var(--primary-color);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                <h3 class="card-title">知识库管理</h3>
                                <p class="card-subtitle">管理平台和供应商的接口知识库</p>
                                <a href="/mapping-gen/knowledge-base" class="btn btn-primary">进入知识库</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <svg class="icon mb-3" style="width: 48px; height: 48px; color: var(--success-color);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                </svg>
                                <h3 class="card-title">建立关联</h3>
                                <p class="card-subtitle">创建平台端和供应商端的映射关联</p>
                                <a href="/mapping-gen/mapping-association" class="btn btn-success">建立关联</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <svg class="icon mb-3" style="width: 48px; height: 48px; color: var(--warning-color);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                <h3 class="card-title">字段映射</h3>
                                <p class="card-subtitle">管理详细的字段映射规则</p>
                                <a href="/mapping-gen/field-mapping" class="btn btn-warning">字段映射</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-3 mb-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <svg class="icon mb-3" style="width: 48px; height: 48px; color: var(--info-color);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                </svg>
                                <h3 class="card-title">代码提示</h3>
                                <p class="card-subtitle">管理和编辑代码提示内容</p>
                                <a href="/mapping-gen/code-prompt" class="btn btn-info">代码提示</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="row mt-5">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">系统概览</h3>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="stat-item">
                                            <h4 class="stat-number text-primary">12</h4>
                                            <p class="stat-label">知识库总数</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-item">
                                            <h4 class="stat-number text-success">8</h4>
                                            <p class="stat-label">映射关联</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-item">
                                            <h4 class="stat-number text-warning">24</h4>
                                            <p class="stat-label">字段映射</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-item">
                                            <h4 class="stat-number text-info">6</h4>
                                            <p class="stat-label">代码提示</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: scripts}"></div>
    
    <style>
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -15px;
        }
        
        .col-md-3, .col-md-6, .col-md-12 {
            padding: 0 15px;
        }
        
        .col-md-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }
        
        .col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
        
        .col-md-12 {
            flex: 0 0 100%;
            max-width: 100%;
        }
        
        .mb-3 {
            margin-bottom: 1rem;
        }
        
        .mb-4 {
            margin-bottom: 1.5rem;
        }
        
        .mt-5 {
            margin-top: 3rem;
        }
        
        .stat-item {
            padding: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: var(--gray-600);
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .col-md-3, .col-md-6 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
    </style>
</body>
</html>
