/**
 * CodePrompt Management JavaScript functionality
 */

// Global variables
let currentCodePromptId = null;
let currentPromptFiles = [];
let isEditMode = false;
let originalContent = {};

// Tree data for cascading dropdowns
let platformTreeData = null;
let supplierTreeData = null;

// Store interface IDs for API calls
let platformInterfaceIdMap = new Map(); // key: "businessDomain-interfaceName-version", value: interfaceId
let supplierInterfaceIdMap = new Map(); // key: "businessDomain-supplierName-interfaceName-version", value: interfaceId

// Store available prompt versions
let availablePromptVersions = [];
let currentPlatformInterfaceId = null;
let currentSupplierInterfaceId = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCodePrompt();
});

async function initializeCodePrompt() {
    try {
        // Load initial data
        await loadDropdownData();

        // Check for URL parameters
        const urlParams = new URLSearchParams(window.location.search);

        if (urlParams.has('promptVersionId')) {
            // Direct Prompt Version ID - load specific prompt version
            await loadPromptVersionById(urlParams.get('promptVersionId'));
        } else if (urlParams.has('codePromptId')) {
            // Direct CodePrompt ID - load specific CodePrompt (legacy support)
            await loadCodePromptById(urlParams.get('codePromptId'));
        } else if (urlParams.has('businessDomain')) {
            // Parameters from field mapping - auto-populate and search
            await handleFieldMappingParameters(urlParams);
        } else {
            // No parameters - show default example
            await showDefaultExample();
        }
    } catch (error) {
        console.error('Error initializing CodePrompt:', error);
        MappingGen.Utils.showAlert('页面初始化失败：' + error.message, 'error');
    }
}

async function handleFieldMappingParameters(urlParams) {
    // Auto-populate form from URL parameters
    populateFormFromParams(urlParams);

    // Wait for dropdown data to be fully loaded
    setTimeout(async () => {
        // If we have promptVersionId, load that specific prompt version
        if (urlParams.has('promptVersionId')) {
            try {
                await loadPromptVersionById(urlParams.get('promptVersionId'));
                return;
            } catch (error) {
                console.warn('Failed to load prompt version, falling back to search:', error);
            }
        }

        // Check if we have all required parameters for automatic search
        const requiredFields = ['businessDomain', 'platformInterface', 'platformVersion', 'vendor', 'vendorInterface', 'vendorVersion'];
        const hasAllFields = requiredFields.every(field => urlParams.has(field) && urlParams.get(field).trim() !== '');
        
        if (hasAllFields) {
            // Trigger automatic search after a short delay to ensure dropdowns are populated
            console.log('Auto-searching CodePrompt with URL parameters');
            setTimeout(() => searchCodePrompt(), 1000);
        } else {
            console.log('Not all required parameters present for auto-search');
            showNoSelectionState();
        }
    }, 1000); // Wait for dropdown initialization
}

async function showDefaultExample() {
    try {
        // Simply show the no selection state to guide users to make selections
        showNoSelectionState();
        
        // Show a helpful message to guide users
        MappingGen.Utils.showAlert('请选择业务域、接口和版本信息来查看对应的CodePrompt', 'info');
        
        console.log('Showing default state, waiting for user selections');
    } catch (error) {
        console.warn('Error showing default state:', error);
        showNoSelectionState();
    }
}

async function loadDropdownData() {
    try {
        console.log('Loading tree data for CodePrompt cascading dropdowns...');
        
        // Load platform interface tree data
        const platformResponse = await MappingGen.API.get('/api/platform-interface/tree');
        if (platformResponse.success && platformResponse.data) {
            platformTreeData = platformResponse.data;
            buildPlatformInterfaceIdMap(platformTreeData);
            console.log('Platform tree data loaded for CodePrompt:', platformTreeData);
        } else {
            throw new Error('获取平台接口树形数据失败：' + (platformResponse.message || '未知错误'));
        }
        
        // Load supplier interface tree data
        const supplierResponse = await MappingGen.API.get('/api/supplier-interface/tree');
        if (supplierResponse.success && supplierResponse.data) {
            supplierTreeData = supplierResponse.data;
            buildSupplierInterfaceIdMap(supplierTreeData);
            console.log('Supplier tree data loaded for CodePrompt:', supplierTreeData);
        } else {
            throw new Error('获取供应商接口树形数据失败：' + (supplierResponse.message || '未知错误'));
        }
        
        // Initialize cascading dropdowns
        initializeCascadingDropdowns();
        
        console.log('CodePrompt dropdowns initialized successfully');
    } catch (error) {
        console.error('Error loading dropdown data:', error);
        MappingGen.Utils.showAlert('加载下拉数据失败：' + error.message, 'error');
        throw error; // Re-throw to handle in initialization
    }
}

// populateSelect function removed - now using MappingGen.InitData.populateSelect

function populateFormFromParams(urlParams) {
    const fields = ['businessDomain', 'platformInterface', 'platformVersion', 'vendor', 'vendorInterface', 'vendorVersion', 'promptVersion'];
    fields.forEach(field => {
        if (urlParams.has(field)) {
            const element = document.getElementById(field);
            if (element) {
                element.value = urlParams.get(field);
            }
        }
    });
    
    // Auto-search if required fields are present
    if (urlParams.has('businessDomain') && urlParams.has('platformInterface') && urlParams.has('vendor')) {
        setTimeout(() => searchCodePrompt(), 500);
    }
}

async function loadPromptVersionById(promptVersionId) {
    try {
        console.log('Loading prompt version by ID:', promptVersionId);
        
        // Load prompt version data using new API
        const response = await MappingGen.API.get(`/api/prompt-version/id/${promptVersionId}`);
        
        if (response.success) {
            const data = response.data;
            
            // Store current prompt version ID for saving
            currentCodePromptId = data.id;
            
            // Populate form fields with version info
            document.getElementById('promptVersion').value = data.version || '';
            
            // Create prompt files data in the expected format
            const promptFiles = [
                {
                    id: 1,
                    fileType: '入参Bean生成Prompt',
                    fileName: 'reqBeanPrompt.txt',
                    content: data.reqBeanPrompt || ''
                },
                {
                    id: 2,
                    fileType: '返参Bean生成Prompt',
                    fileName: 'respBeanPrompt.txt',
                    content: data.respBeanPrompt || ''
                },
                {
                    id: 3,
                    fileType: '代码生成Prompt',
                    fileName: 'codeGenPrompt.txt',
                    content: data.codeGenPrompt || ''
                }
            ];
            
            // Load data using existing format
            loadCodePromptData({
                id: data.id,
                promptFiles: promptFiles
            });
            
            MappingGen.Utils.showAlert('Prompt版本加载成功', 'success');
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error loading prompt version by ID:', error);
        MappingGen.Utils.showAlert('加载 Prompt版本 失败：' + error.message, 'error');
        showNoDataState();
    }
}

async function loadCodePromptById(codePromptId) {
    try {
        console.warn('loadCodePromptById is deprecated, use loadPromptVersionById instead');
        
        // This function is kept for backward compatibility but should not be used
        // Redirect to no selection state and show message
        showNoSelectionState();
        MappingGen.Utils.showAlert('请使用新的Prompt版本查询功能，选择完整的接口信息后查询', 'info');
    } catch (error) {
        console.error('Error in legacy loadCodePromptById:', error);
        showNoDataState();
    }
}

async function searchCodePrompt() {
    // const form = document.getElementById('codePromptSearchForm');
    // const formData = MappingGen.Utils.getFormData(form);
    //
    // // Validate that all required fields are selected
    // if (!formData.businessDomain || !formData.platformInterface || !formData.platformVersion ||
    //     !formData.vendor || !formData.vendorInterface || !formData.vendorVersion) {
    //     MappingGen.Utils.showAlert('请选择完整的接口信息（业务域、平台接口、版本、供应商、供应商接口、版本）', 'warning');
    //     return;
    // }
    //
    // try {
    //     console.log('Triggering search with form data:', formData);
    //
    //     // Trigger the cascade loading of prompt versions
    //     await checkAndLoadPromptVersions();
    //
    //     // If there are available prompt versions, automatically load the latest one
    //     if (availablePromptVersions && availablePromptVersions.length > 0) {
    //         const latestVersion = availablePromptVersions[0]; // Versions are sorted, first is latest
    //         console.log('Auto-loading latest prompt version:', latestVersion);
    //
    //         // Set the prompt version dropdown
    //         const promptVersionSelect = document.getElementById('promptVersion');
    //         if (promptVersionSelect) {
    //             promptVersionSelect.value = latestVersion;
    //         }
    //
    //         // Load the prompt content
    //         await loadPromptByVersion(latestVersion);
    //
    //         MappingGen.Utils.showAlert(`已加载最新版本 ${latestVersion} 的Prompt`, 'success');
    //     } else {
    //         // No prompt versions available
    //         showNoDataState();
    //         // 版本信息已经在checkAndLoadPromptVersions中处理，这里不再重复显示alert
    //         console.log('No prompt versions available for the selected interface combination');
    //     }
    // } catch (error) {
    //     console.error('Error searching CodePrompt:', error);
    //     MappingGen.Utils.showAlert('查询 CodePrompt 失败：' + error.message, 'error');
    //     showNoSelectionState();
    // }

    const form = document.getElementById('codePromptSearchForm');
    const formData = MappingGen.Utils.getFormData(form);

    // ✅ 增加Prompt版本的校验
    const promptVersion = document.getElementById('promptVersion').value;
    if (!promptVersion) {
        MappingGen.Utils.showAlert('请选择提示词版本', 'warning');
        return;
    }

    // ✅ 使用统一的接口 + 版本号加载Prompt内容
    try {
        await loadPromptByVersion(promptVersion);
        MappingGen.Utils.showAlert(`Prompt版本 ${promptVersion} 加载成功`, 'success');
    } catch (error) {
        console.error('加载Prompt失败:', error);
        MappingGen.Utils.showAlert('加载Prompt失败：' + error.message, 'error');
    }
}

function loadCodePromptData(data) {
    currentCodePromptId = data.id;
    currentPromptFiles = data.promptFiles || [];
    
    // Show CodePrompt content
    document.getElementById('noSelectionState').style.display = 'none';
    document.getElementById('noDataState').style.display = 'none';
    document.getElementById('codePromptContent').style.display = 'block';
    
    // Display prompt content cards
    displayPromptCards(currentPromptFiles);
    
    // Reset edit mode
    isEditMode = false;
    updateEditModeUI();
}

function showNoSelectionState() {
    document.getElementById('codePromptContent').style.display = 'none';
    document.getElementById('noDataState').style.display = 'none';
    document.getElementById('noSelectionState').style.display = 'block';
}

function showNoDataState() {
    document.getElementById('codePromptContent').style.display = 'none';
    document.getElementById('noSelectionState').style.display = 'none';
    document.getElementById('noDataState').style.display = 'block';
}

function displayPromptCards(promptFiles) {
    const cardsContainer = document.getElementById('promptContentCards');
    cardsContainer.innerHTML = '';
    
    if (!promptFiles || promptFiles.length === 0) {
        cardsContainer.innerHTML = '<p class="text-center text-secondary">暂无 CodePrompt 内容</p>';
        return;
    }
    
    promptFiles.forEach((file, index) => {
        const card = createPromptCard(file, index);
        cardsContainer.appendChild(card);
    });
}

function createPromptCard(file, index) {
    const card = document.createElement('div');
    card.className = 'document-card';
    card.setAttribute('data-file-id', file.id);
    card.setAttribute('data-file-index', index);
    
    card.innerHTML = `
        <div class="document-card-header">
            <span class="document-type">${file.fileType}</span>
            <span class="text-sm text-secondary">${file.fileName}</span>
        </div>
        <div class="document-card-body">
            <div class="document-content" id="content_${index}">
                ${file.content || '暂无内容'}
            </div>
            <div class="document-edit-area" id="editArea_${index}" style="display: none;">
                <textarea class="form-control textarea code-editor"
                          id="editor_${index}"
                          rows="15"
                          placeholder="请输入代码内容...">${file.content || ''}</textarea>
            </div>
        </div>
    `;
    
    return card;
}

function toggleEditMode() {
    isEditMode = !isEditMode;
    
    if (isEditMode) {
        // Store original content
        originalContent = {};
        currentPromptFiles.forEach((file, index) => {
            originalContent[index] = file.content;
        });
        
        // Show edit areas
        currentPromptFiles.forEach((file, index) => {
            document.getElementById(`content_${index}`).style.display = 'none';
            document.getElementById(`editArea_${index}`).style.display = 'block';
        });
    } else {
        // Hide edit areas
        currentPromptFiles.forEach((file, index) => {
            document.getElementById(`content_${index}`).style.display = 'block';
            document.getElementById(`editArea_${index}`).style.display = 'none';
        });
    }
    
    updateEditModeUI();
}

function updateEditModeUI() {
    const editButton = document.getElementById('editButton');
    const saveButton = document.getElementById('saveButton');
    const cancelButton = document.getElementById('cancelButton');
    
    if (isEditMode) {
        editButton.style.display = 'none';
        saveButton.style.display = 'inline-flex';
        cancelButton.style.display = 'inline-flex';
    } else {
        editButton.style.display = 'inline-flex';
        saveButton.style.display = 'none';
        cancelButton.style.display = 'none';
    }
}

function cancelEdit() {
    // Restore original content
    currentPromptFiles.forEach((file, index) => {
        const editor = document.getElementById(`editor_${index}`);
        if (editor && originalContent[index] !== undefined) {
            editor.value = originalContent[index];
        }
    });

    // Exit edit mode
    isEditMode = false;

    // Hide edit areas and show content areas
    currentPromptFiles.forEach((file, index) => {
        const contentElement = document.getElementById(`content_${index}`);
        const editAreaElement = document.getElementById(`editArea_${index}`);
        if (contentElement && editAreaElement) {
            contentElement.style.display = 'block';
            editAreaElement.style.display = 'none';
        }
    });

    // Update UI buttons
    updateEditModeUI();
}

async function saveCodePrompt() {
    if (!currentCodePromptId) {
        MappingGen.Utils.showAlert('无效的 Prompt ID', 'error');
        return;
    }
    
    try {
        // Collect updated content from the three prompt text areas
        const reqBeanPromptEditor = document.getElementById('editor_0');
        const respBeanPromptEditor = document.getElementById('editor_1');
        const codeGenPromptEditor = document.getElementById('editor_2');
        
        if (!reqBeanPromptEditor || !respBeanPromptEditor || !codeGenPromptEditor) {
            throw new Error('编辑器未找到');
        }
        
        // Prepare update request data
        const updateData = {
            reqBeanPrompt: reqBeanPromptEditor.value,
            respBeanPrompt: respBeanPromptEditor.value,
            codeGenPrompt: codeGenPromptEditor.value,
            operator: '用户'
        };
        
        // Call new prompt version update API
        const response = await MappingGen.API.put(`/api/prompt-version/${currentCodePromptId}`, updateData);
        
        if (response.success) {
            MappingGen.Utils.showAlert('Prompt 保存成功', 'success');
            
            // Update current data
            if (currentPromptFiles[0]) currentPromptFiles[0].content = updateData.reqBeanPrompt;
            if (currentPromptFiles[1]) currentPromptFiles[1].content = updateData.respBeanPrompt;
            if (currentPromptFiles[2]) currentPromptFiles[2].content = updateData.codeGenPrompt;
            
            // Update display
            displayPromptCards(currentPromptFiles);
            
            // Exit edit mode
            isEditMode = false;
            updateEditModeUI();
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error saving Prompt:', error);
        MappingGen.Utils.showAlert('保存失败：' + error.message, 'error');
    }
}

// ===== 级联下拉框相关函数 =====

/**
 * 构建平台接口ID映射
 */
function buildPlatformInterfaceIdMap(treeData) {
    platformInterfaceIdMap.clear();
    
    if (!treeData || !Array.isArray(treeData)) return;
    
    treeData.forEach(domain => {
        if (domain.businessDomain && domain.interfaces) {
            domain.interfaces.forEach(interfaceNode => {
                if (interfaceNode.interfaceName && interfaceNode.versions) {
                    interfaceNode.versions.forEach(versionNode => {
                        const key = `${domain.businessDomain}-${interfaceNode.interfaceName}-${versionNode.version}`;
                        platformInterfaceIdMap.set(key, versionNode.id);
                    });
                }
            });
        }
    });
    
    console.log('Platform interface ID map built for CodePrompt:', platformInterfaceIdMap);
}

/**
 * 构建供应商接口ID映射
 */
function buildSupplierInterfaceIdMap(treeData) {
    supplierInterfaceIdMap.clear();
    
    if (!treeData || !Array.isArray(treeData)) return;
    
    treeData.forEach(domain => {
        if (domain.businessDomain && domain.suppliers) {
            domain.suppliers.forEach(supplierNode => {
                if (supplierNode.supplierName && supplierNode.interfaces) {
                    supplierNode.interfaces.forEach(interfaceNode => {
                        if (interfaceNode.interfaceName && interfaceNode.versions) {
                            interfaceNode.versions.forEach(versionNode => {
                                const key = `${domain.businessDomain}-${supplierNode.supplierName}-${interfaceNode.interfaceName}-${versionNode.version}`;
                                supplierInterfaceIdMap.set(key, versionNode.id);
                            });
                        }
                    });
                }
            });
        }
    });
    
    console.log('Supplier interface ID map built for CodePrompt:', supplierInterfaceIdMap);
}

/**
 * 初始化级联下拉框
 */
function initializeCascadingDropdowns() {
    console.log('Initializing cascading dropdowns for CodePrompt...');
    
    // 获取表单元素
    const businessDomainSelect = document.getElementById('businessDomain');
    const platformInterfaceSelect = document.getElementById('platformInterface');
    const platformVersionSelect = document.getElementById('platformVersion');
    const vendorSelect = document.getElementById('vendor');
    const vendorInterfaceSelect = document.getElementById('vendorInterface');
    const vendorVersionSelect = document.getElementById('vendorVersion');
    const promptVersionSelect = document.getElementById('promptVersion');
    
    if (!businessDomainSelect || !platformInterfaceSelect || !platformVersionSelect ||
        !vendorSelect || !vendorInterfaceSelect || !vendorVersionSelect || !promptVersionSelect) {
        console.warn('Some form elements not found for cascading dropdowns in CodePrompt');
        return;
    }
    
    // 填充业务域选项
    populateBusinessDomains(businessDomainSelect);
    
    // 初始时隐藏版本信息提示
    displayPromptVersionsInfo([], false);
    
    // 业务域变更事件
    businessDomainSelect.addEventListener('change', function() {
        const selectedDomain = this.value;
        console.log('Business domain changed in CodePrompt:', selectedDomain);
        
        updatePlatformInterfaces(selectedDomain, platformInterfaceSelect, platformVersionSelect);
        updateSuppliers(selectedDomain, vendorSelect, vendorInterfaceSelect, vendorVersionSelect);
        clearPromptVersions(promptVersionSelect);
    });
    
    // 平台接口变更事件
    platformInterfaceSelect.addEventListener('change', function() {
        const selectedDomain = businessDomainSelect.value;
        const selectedInterface = this.value;
        console.log('Platform interface changed in CodePrompt:', selectedInterface);
        
        updatePlatformVersions(selectedDomain, selectedInterface, platformVersionSelect);
        clearPromptVersions(promptVersionSelect);
    });
    
    // 供应商变更事件
    vendorSelect.addEventListener('change', function() {
        const selectedDomain = businessDomainSelect.value;
        const selectedVendor = this.value;
        console.log('Vendor changed in CodePrompt:', selectedVendor);
        
        updateVendorInterfaces(selectedDomain, selectedVendor, vendorInterfaceSelect, vendorVersionSelect);
        clearPromptVersions(promptVersionSelect);
    });
    
    // 供应商接口变更事件
    vendorInterfaceSelect.addEventListener('change', function() {
        const selectedDomain = businessDomainSelect.value;
        const selectedVendor = vendorSelect.value;
        const selectedInterface = this.value;
        console.log('Vendor interface changed in CodePrompt:', selectedInterface);
        
        updateVendorVersions(selectedDomain, selectedVendor, selectedInterface, vendorVersionSelect);
        clearPromptVersions(promptVersionSelect);
    });
    
    // 平台版本变更事件 - 查询提示词版本
    platformVersionSelect.addEventListener('change', function() {
        checkAndLoadPromptVersions();
    });
    
    // 供应商版本变更事件 - 查询提示词版本
    vendorVersionSelect.addEventListener('change', function() {
        checkAndLoadPromptVersions();
    });
    
    // 提示词版本变更事件 - 加载具体内容
    // promptVersionSelect.addEventListener('change', function() {
    //     if (this.value) {
    //         loadPromptByVersion(this.value);
    //     }
    // });
}

/**
 * 填充业务域下拉框（平台和供应商业务域的并集）
 */
function populateBusinessDomains(selectElement) {
    if (!selectElement) return;
    
    const businessDomains = new Set();
    
    // 收集平台业务域
    if (platformTreeData && Array.isArray(platformTreeData)) {
        platformTreeData.forEach(domain => {
            if (domain.businessDomain) {
                businessDomains.add(domain.businessDomain);
            }
        });
    }
    
    // 收集供应商业务域
    if (supplierTreeData && Array.isArray(supplierTreeData)) {
        supplierTreeData.forEach(domain => {
            if (domain.businessDomain) {
                businessDomains.add(domain.businessDomain);
            }
        });
    }
    
    // 清空并填充选项
    clearSelect(selectElement, '请选择业务领域');
    Array.from(businessDomains).sort().forEach(domain => {
        const option = document.createElement('option');
        option.value = domain;
        option.textContent = domain;
        selectElement.appendChild(option);
    });
    
    console.log('Business domains populated for CodePrompt:', Array.from(businessDomains));
}

/**
 * 更新平台接口下拉框
 */
function updatePlatformInterfaces(businessDomain, interfaceSelect, versionSelect) {
    clearSelect(interfaceSelect, '请选择平台端接口');
    clearSelect(versionSelect, '请选择平台端版本');
    
    if (!businessDomain || !platformTreeData) return;
    
    const domainData = platformTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.interfaces) {
        domainData.interfaces.forEach(interfaceNode => {
            const option = document.createElement('option');
            option.value = interfaceNode.interfaceName;
            option.textContent = interfaceNode.interfaceName;
            interfaceSelect.appendChild(option);
        });
    }
}

/**
 * 更新平台版本下拉框
 */
function updatePlatformVersions(businessDomain, interfaceName, versionSelect) {
    clearSelect(versionSelect, '请选择平台端版本');
    
    if (!businessDomain || !interfaceName || !platformTreeData) return;
    
    const domainData = platformTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.interfaces) {
        const interfaceData = domainData.interfaces.find(i => i.interfaceName === interfaceName);
        if (interfaceData && interfaceData.versions) {
            interfaceData.versions.forEach(versionNode => {
                const option = document.createElement('option');
                option.value = versionNode.version;
                option.textContent = versionNode.version;
                versionSelect.appendChild(option);
            });
        }
    }
}

/**
 * 更新供应商下拉框
 */
function updateSuppliers(businessDomain, supplierSelect, interfaceSelect, versionSelect) {
    clearSelect(supplierSelect, '请选择供应商');
    clearSelect(interfaceSelect, '请选择供应商端接口');
    clearSelect(versionSelect, '请选择供应商端版本');
    
    if (!businessDomain || !supplierTreeData) return;
    
    const domainData = supplierTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.suppliers) {
        domainData.suppliers.forEach(supplierNode => {
            const option = document.createElement('option');
            option.value = supplierNode.supplierName;
            option.textContent = supplierNode.supplierName;
            supplierSelect.appendChild(option);
        });
    }
}

/**
 * 更新供应商接口下拉框
 */
function updateVendorInterfaces(businessDomain, supplierName, interfaceSelect, versionSelect) {
    clearSelect(interfaceSelect, '请选择供应商端接口');
    clearSelect(versionSelect, '请选择供应商端版本');
    
    if (!businessDomain || !supplierName || !supplierTreeData) return;
    
    const domainData = supplierTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.suppliers) {
        const supplierData = domainData.suppliers.find(s => s.supplierName === supplierName);
        if (supplierData && supplierData.interfaces) {
            supplierData.interfaces.forEach(interfaceNode => {
                const option = document.createElement('option');
                option.value = interfaceNode.interfaceName;
                option.textContent = interfaceNode.interfaceName;
                interfaceSelect.appendChild(option);
            });
        }
    }
}

/**
 * 更新供应商版本下拉框
 */
function updateVendorVersions(businessDomain, supplierName, interfaceName, versionSelect) {
    clearSelect(versionSelect, '请选择供应商端版本');
    
    if (!businessDomain || !supplierName || !interfaceName || !supplierTreeData) return;
    
    const domainData = supplierTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.suppliers) {
        const supplierData = domainData.suppliers.find(s => s.supplierName === supplierName);
        if (supplierData && supplierData.interfaces) {
            const interfaceData = supplierData.interfaces.find(i => i.interfaceName === interfaceName);
            if (interfaceData && interfaceData.versions) {
                interfaceData.versions.forEach(versionNode => {
                    const option = document.createElement('option');
                    option.value = versionNode.version;
                    option.textContent = versionNode.version;
                    versionSelect.appendChild(option);
                });
            }
        }
    }
}

/**
 * 清空下拉框并添加占位符选项
 */
function clearSelect(selectElement, placeholder) {
    if (!selectElement) return;
    
    selectElement.innerHTML = '';
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = placeholder;
    defaultOption.disabled = true;
    defaultOption.selected = true;
    selectElement.appendChild(defaultOption);
}

/**
 * 清空提示词版本下拉框
 */
function clearPromptVersions(promptVersionSelect) {
    clearSelect(promptVersionSelect, '请选择提示词版本');
    availablePromptVersions = [];
    
    // 隐藏版本信息提示
    displayPromptVersionsInfo([], false);
}

/**
 * 获取平台接口ID
 */
function getPlatformInterfaceId(businessDomain, interfaceName, version) {
    const key = `${businessDomain}-${interfaceName}-${version}`;
    return platformInterfaceIdMap.get(key);
}

/**
 * 获取供应商接口ID
 */
function getSupplierInterfaceId(businessDomain, supplierName, interfaceName, version) {
    const key = `${businessDomain}-${supplierName}-${interfaceName}-${version}`;
    return supplierInterfaceIdMap.get(key);
}

/**
 * 检查并加载提示词版本列表
 */
async function checkAndLoadPromptVersions() {
    const businessDomainSelect = document.getElementById('businessDomain');
    const platformInterfaceSelect = document.getElementById('platformInterface');
    const platformVersionSelect = document.getElementById('platformVersion');
    const vendorSelect = document.getElementById('vendor');
    const vendorInterfaceSelect = document.getElementById('vendorInterface');
    const vendorVersionSelect = document.getElementById('vendorVersion');
    const promptVersionSelect = document.getElementById('promptVersion');
    
    // 检查是否所有必要字段都已选择
    if (!businessDomainSelect.value || !platformInterfaceSelect.value || 
        !platformVersionSelect.value || !vendorSelect.value || 
        !vendorInterfaceSelect.value || !vendorVersionSelect.value) {
        console.log('Not all interface parameters selected yet, skipping prompt version loading');
        return;
    }
    
    try {
        // 获取接口ID
        const platformInterfaceId = getPlatformInterfaceId(
            businessDomainSelect.value, 
            platformInterfaceSelect.value, 
            platformVersionSelect.value
        );
        
        const supplierInterfaceId = getSupplierInterfaceId(
            businessDomainSelect.value, 
            vendorSelect.value, 
            vendorInterfaceSelect.value, 
            vendorVersionSelect.value
        );
        
        if (!platformInterfaceId || !supplierInterfaceId) {
            console.warn('Unable to find interface IDs for prompt version loading');
            clearPromptVersions(promptVersionSelect);
            return;
        }
        
        console.log('Loading prompt versions for interfaces:', { platformInterfaceId, supplierInterfaceId });
        
        // 存储当前接口ID供后续使用
        currentPlatformInterfaceId = platformInterfaceId;
        currentSupplierInterfaceId = supplierInterfaceId;
        
        // 查询可用的提示词版本
        const response = await MappingGen.API.get('/api/prompt-version/versionList', {
            platformInterfaceId: platformInterfaceId,
            supplierInterfaceId: supplierInterfaceId
        });
        
        if (response.success && response.data) {
            availablePromptVersions = response.data;
            populatePromptVersions(promptVersionSelect, availablePromptVersions);
            console.log('Available prompt versions loaded for CodePrompt:', availablePromptVersions);
            
            // 显示版本加载状态信息
            if (availablePromptVersions.length > 0) {
                displayPromptVersionsInfo(availablePromptVersions, false);
            } else {
                displayPromptVersionsInfo([], true);
            }
        } else {
            console.warn('No prompt versions found or API error:', response.message);
            availablePromptVersions = [];
            clearPromptVersions(promptVersionSelect);
            displayPromptVersionsInfo([], true, response.message);
        }
    } catch (error) {
        console.error('Error loading prompt versions:', error);
        availablePromptVersions = [];
        clearPromptVersions(promptVersionSelect);
        displayPromptVersionsInfo([], true, error.message);
    }
}

/**
 * 显示提示词版本信息和状态
 */
function displayPromptVersionsInfo(versions, isEmpty = false, errorMessage = null) {
    // 查找或创建版本信息显示容器
    let versionInfoContainer = document.getElementById('promptVersionInfoCodePrompt');
    
    // 如果容器不存在，创建一个
    if (!versionInfoContainer) {
        versionInfoContainer = document.createElement('div');
        versionInfoContainer.id = 'promptVersionInfoCodePrompt';
        versionInfoContainer.className = 'prompt-version-info mt-3';
        
        // 找到合适的位置插入（在提示词版本下拉框附近）
        const promptVersionSelect = document.getElementById('promptVersion');
        if (promptVersionSelect && promptVersionSelect.parentElement) {
            promptVersionSelect.parentElement.appendChild(versionInfoContainer);
        } else {
            // 备选位置：查询按钮附近
            const searchButton = document.querySelector('#codePromptSearchForm button[type="submit"]');
            if (searchButton && searchButton.parentElement) {
                searchButton.parentElement.appendChild(versionInfoContainer);
            }
        }
    }
    
    if (isEmpty) {
        // 显示空状态或错误信息
        let alertType = 'warning';
        let messageContent = '';
        
        if (errorMessage) {
            alertType = 'danger';
            messageContent = `
                <strong>查询失败：</strong> ${errorMessage}<br>
                <small class="text-muted">请检查接口选择是否正确，或联系管理员</small>
            `;
        } else {
            messageContent = `
                <strong>暂无提示词版本：</strong><br>
                <small class="text-muted">
                    当前选择的平台接口与供应商接口组合暂无可用的提示词版本。<br>
                    您可以：<br>
                    • 选择其他接口组合<br>
                    • 联系管理员添加相应的提示词版本<br>
                    • 或等待系统自动生成
                </small>
            `;
        }
        
        versionInfoContainer.innerHTML = `
            <div class="alert alert-${alertType}">
                <i class="fas fa-exclamation-triangle"></i>
                ${messageContent}
            </div>
        `;
        versionInfoContainer.style.display = 'block';
    } else if (versions && versions.length > 0) {
        // 显示可用版本信息
        versionInfoContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>可用提示词版本：</strong>
                ${versions.map(version => `<span class="badge badge-primary mr-1">${version}</span>`).join('')}
                <br>
                <small class="text-muted">
                    共找到 ${versions.length} 个版本，版本按时间降序排列
                </small>
            </div>
        `;
        versionInfoContainer.style.display = 'block';
    } else {
        // 隐藏容器
        versionInfoContainer.style.display = 'none';
    }
}

/**
 * 填充提示词版本下拉框
 */
function populatePromptVersions(promptVersionSelect, versions) {
    clearSelect(promptVersionSelect, '请选择提示词版本');
    
    if (versions && versions.length > 0) {
        versions.forEach(version => {
            const option = document.createElement('option');
            option.value = version;
            option.textContent = version;
            promptVersionSelect.appendChild(option);
        });
        console.log('Prompt versions populated:', versions);
    } else {
        // 当没有版本时，显示特殊的占位符选项
        const noVersionOption = document.createElement('option');
        noVersionOption.value = '';
        noVersionOption.textContent = '暂无可用版本';
        noVersionOption.disabled = true;
        noVersionOption.selected = true;
        promptVersionSelect.appendChild(noVersionOption);
    }
}

/**
 * 根据版本号加载具体的Prompt内容
 */
async function loadPromptByVersion(version) {
    if (!currentPlatformInterfaceId || !currentSupplierInterfaceId) {
        MappingGen.Utils.showAlert('接口ID信息缺失，请重新选择接口', 'error');
        return;
    }
    
    try {
        console.log('Loading prompt content for version:', version);
        
        // 调用新的根据版本号查询Prompt API
        const response = await MappingGen.API.get(`/api/prompt-version/${version}`, {
            platformInterfaceId: currentPlatformInterfaceId,
            supplierInterfaceId: currentSupplierInterfaceId
        });
        
        if (response.success && response.data) {
            const data = response.data;
            
            // 存储当前prompt版本ID用于保存
            currentCodePromptId = data.id;
            
            // 创建prompt files数据格式
            const promptFiles = [
                {
                    id: 1,
                    fileType: '入参Bean生成Prompt',
                    fileName: 'reqBeanPrompt.txt',
                    content: data.reqBeanPrompt || ''
                },
                {
                    id: 2,
                    fileType: '返参Bean生成Prompt',
                    fileName: 'respBeanPrompt.txt',
                    content: data.respBeanPrompt || ''
                },
                {
                    id: 3,
                    fileType: '代码生成Prompt',
                    fileName: 'codeGenPrompt.txt',
                    content: data.codeGenPrompt || ''
                }
            ];
            
            // 加载数据到界面
            loadCodePromptData({
                id: data.id,
                promptFiles: promptFiles
            });
            
            MappingGen.Utils.showAlert(`Prompt版本 ${version} 加载成功`, 'success');
        } else {
            throw new Error(response.message || '加载Prompt版本失败');
        }
    } catch (error) {
        console.error('Error loading prompt by version:', error);
        MappingGen.Utils.showAlert('加载Prompt版本失败：' + error.message, 'error');
    }
}
