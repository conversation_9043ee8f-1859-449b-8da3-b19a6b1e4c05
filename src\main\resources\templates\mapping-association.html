<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('建立关联')}"></head>
<body>
    <div class="app-layout">
        <div th:replace="~{fragments/layout :: sidebar}"></div>
        <div class="app-main">
            <div th:replace="~{fragments/layout :: header}"></div>
            <main class="app-content">

                <!-- Page Header -->
                <div class="page-header">
                    <h1 class="page-title">建立关联</h1>
                    <p class="page-subtitle">管理平台端和供应商端接口的映射关联关系</p>
                </div>
                
                <!-- Search and Filter Section -->
                <div class="search-filters">
                    <form id="associationSearchForm" onsubmit="return false;">
                        <!-- Business Domain Section -->
                        <div class="filter-section">
<!--                            <div class="filter-section-title">业务领域</div>-->
                            <div class="filter-single">
                                <div th:replace="~{fragments/components :: form-select('businessDomain', 'businessDomain', '业务领域', ${businessDomains}, '', '请选择业务领域', false)}"></div>
                            </div>
                        </div>

                        <!-- Platform and Vendor Groups -->
                        <div class="filter-groups">
                            <!-- Platform Group -->
                            <div class="filter-group platform-group">
                                <div class="filter-group-title">平台端信息</div>
                                <div class="filter-group-content">
                                    <div th:replace="~{fragments/components :: form-select('platformInterface', 'platformInterface', '平台端接口', ${platformInterfaces}, '', '请选择平台端接口', false)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('platformVersion', 'platformVersion', '平台端版本', ${platformVersions}, '', '请选择平台端版本', false)}"></div>
                                </div>
                            </div>

                            <!-- Vendor Group -->
                            <div class="filter-group vendor-group">
                                <div class="filter-group-title">供应商端信息</div>
                                <div class="filter-group-content">
                                    <div th:replace="~{fragments/components :: form-select('vendor', 'vendor', '供应商', ${vendors}, '', '请选择供应商', false)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('vendorInterface', 'vendorInterface', '供应商接口', ${vendorInterfaces}, '', '请选择供应商端接口', false)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('vendorVersion', 'vendorVersion', '供应商版本', ${vendorVersions}, '', '请选择供应商端版本', false)}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-actions">
                            <button type="button" class="btn btn-primary" onclick="searchAssociations()">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                查询
                            </button>
                            <button type="button" class="btn btn-success" data-action="show-modal" data-target="addMappingModal">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                添加映射
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Data Table -->
                <div class="data-table-container">
                    <table class="data-table" id="associationTable">
                        <thead>
                            <tr>
                                <th>平台端</th>
                                <th>供应商端</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="associationTableBody">
                            <!-- Data will be loaded via JavaScript -->
                        </tbody>
                    </table>
                    
                    <!-- Empty state -->
                    <div id="associationEmptyState" class="empty-state" style="display: none;">
                        <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                        <h3 class="empty-state-title">暂无映射关联数据</h3>
                        <p class="empty-state-description">当前没有符合条件的映射关联记录</p>
                        <button class="btn btn-primary" data-action="show-modal" data-target="addMappingModal">
                            添加第一个映射关联
                        </button>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="pagination" id="associationPagination" style="display: none;">
                        <div class="pagination-info" id="associationPaginationInfo"></div>
                        <div class="pagination-controls" id="associationPaginationControls"></div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Add Mapping Modal -->
    <div id="addMappingModal" class="modal modal-lg">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">添加映射关联</h3>
                    <button type="button" class="modal-close" data-action="hide-modal" data-target="addMappingModal">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addMappingForm">
                        <!-- Business Domain Section -->
                        <div class="filter-section">
<!--                            <div class="filter-section-title">业务领域</div>-->
                            <div class="filter-single">
                                <div th:replace="~{fragments/components :: form-select('addBusinessDomain', 'businessDomain', '业务领域', ${businessDomains}, '', '请选择业务领域', true)}"></div>
                            </div>
                        </div>

                        <!-- Platform and Vendor Groups -->
                        <div class="filter-groups">
                            <!-- Platform Group -->
                            <div class="filter-group platform-group">
                                <div class="filter-group-title">平台端信息</div>
                                <div class="filter-group-content">
                                    <div th:replace="~{fragments/components :: form-select('addPlatformInterface', 'platformInterface', '平台端接口', ${platformInterfaces}, '', '请选择平台端接口', true)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('addPlatformVersion', 'platformVersion', '平台端版本', ${platformVersions}, '', '请选择平台端版本', true)}"></div>
                                </div>
                            </div>

                            <!-- Vendor Group -->
                            <div class="filter-group vendor-group">
                                <div class="filter-group-title">供应商端信息</div>
                                <div class="filter-group-content">
                                    <div th:replace="~{fragments/components :: form-select('addVendor', 'vendor', '供应商', ${vendors}, '', '请选择供应商', true)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('addVendorInterface', 'vendorInterface', '供应商接口', ${vendorInterfaces}, '', '请选择供应商端接口', true)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('addVendorVersion', 'vendorVersion', '供应商版本', ${vendorVersions}, '', '请选择供应商端版本', true)}"></div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-action="hide-modal" data-target="addMappingModal">
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" onclick="createMapping()">
                        确认创建
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Confirm Create Modal -->
    <div id="confirmCreateModal" class="modal modal-sm">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">确认创建</h3>
                    <button type="button" class="modal-close" data-action="hide-modal" data-target="confirmCreateModal">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                    <p>是否直接导航至字段映射管理页面？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="confirmCreateMapping(false)">
                        否
                    </button>
                    <button type="button" class="btn btn-primary" onclick="confirmCreateMapping(true)">
                        是
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Confirm Delete Modal -->
    <div id="confirmDeleteModal" class="modal modal-sm">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">确认删除</h3>
                    <button type="button" class="modal-close" data-action="hide-modal" data-target="confirmDeleteModal">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                    <p>确定要删除这个映射关联吗？此操作不可撤销。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-action="hide-modal" data-target="confirmDeleteModal">
                        取消
                    </button>
                    <button type="button" class="btn btn-error" onclick="confirmDeleteMapping()">
                        确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: scripts}"></div>
    <script th:src="@{/js/mapping-association.js}"></script>
</body>
</html>
