<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('知识库')}"></head>
<body>
    <div class="app-layout">
        <div th:replace="~{fragments/layout :: sidebar}"></div>
        <div class="app-main">
            <div th:replace="~{fragments/layout :: header}"></div>
            <main class="app-content">

                <!-- Page Header - Platform Knowledge Base -->
                <div th:if="${activeTab == null or activeTab == 'platform'}" class="page-header">
                    <h1 class="page-title">平台知识库</h1>
                    <p class="page-subtitle">管理平台接口知识库，包括文档、示例和提示信息</p>
                </div>

                <!-- Page Header - Vendor Knowledge Base -->
                <div th:if="${activeTab == 'vendor'}" class="page-header">
                    <h1 class="page-title">供应商知识库</h1>
                    <p class="page-subtitle">管理供应商接口知识库，包括文档、示例和提示信息</p>
                </div>
                
                <!-- Content based on URL parameter -->
                <div th:if="${activeTab == null or activeTab == 'platform'}">
                    <div th:replace="~{knowledge-base/platform :: content}"></div>
                </div>

                <div th:if="${activeTab == 'vendor'}">
                    <div th:replace="~{knowledge-base/vendor :: content}"></div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Modals -->
    <div th:replace="~{knowledge-base/platform :: modals}"></div>
    <div th:replace="~{knowledge-base/vendor :: modals}"></div>
    
    <div th:replace="~{fragments/layout :: scripts}"></div>
    <script th:src="@{/js/knowledge-base.js}"></script>
</body>
</html>
