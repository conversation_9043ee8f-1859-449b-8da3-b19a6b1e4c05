/* Component Styles */

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: var(--button-height);
  padding: var(--button-padding-y) var(--button-padding-x);
  font-size: var(--font-size-sm);
  font-weight: var(--button-font-weight);
  line-height: 1;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--button-border-radius);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn:focus {
  outline: none;
  box-shadow: var(--shadow-focus);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
  pointer-events: none;
}

.btn:hover::before {
  transform: translateX(100%);
}

/* Enhanced Button variants */
.btn-primary {
  color: var(--white);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
  border-color: var(--primary-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-xs);
}

.btn-secondary {
  color: var(--white);
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
  border-color: var(--secondary-color);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--secondary-dark) 0%, var(--secondary-color) 100%);
  border-color: var(--secondary-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-success {
  color: var(--white);
  background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
  border-color: var(--success-color);
  box-shadow: var(--shadow-sm);
}

.btn-success:hover {
  background: linear-gradient(135deg, var(--success-dark) 0%, var(--success-color) 100%);
  border-color: var(--success-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-warning {
  color: var(--white);
  background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
  border-color: var(--warning-color);
  box-shadow: var(--shadow-sm);
}

.btn-warning:hover {
  background: linear-gradient(135deg, var(--warning-dark) 0%, var(--warning-color) 100%);
  border-color: var(--warning-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-error {
  color: var(--white);
  background: linear-gradient(135deg, var(--error-color) 0%, var(--error-dark) 100%);
  border-color: var(--error-color);
  box-shadow: var(--shadow-sm);
}

.btn-error:hover {
  background: linear-gradient(135deg, var(--error-dark) 0%, var(--error-color) 100%);
  border-color: var(--error-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-outline {
  color: var(--primary-color);
  background-color: transparent;
  border-color: var(--primary-color);
}

.btn-outline:hover {
  color: var(--white);
  background-color: var(--primary-color);
}

.btn-ghost {
  color: var(--primary-color);
  background-color: transparent;
  border-color: transparent;
}

.btn-ghost:hover {
  background-color: rgba(25, 118, 210, 0.1);
}

/* Button sizes */
.btn-sm {
  height: 32px;
  padding: 4px 12px;
  font-size: var(--font-size-xs);
}

.btn-lg {
  height: 48px;
  padding: 12px 24px;
  font-size: var(--font-size-lg);
}

.btn-icon {
  width: var(--button-height);
  padding: 0;
}

.btn-icon.btn-sm {
  width: 32px;
}

.btn-icon.btn-lg {
  width: 48px;
}

/* Button with icon */
.btn .icon {
  width: 16px;
  height: 16px;
  margin-right: var(--spacing-xs);
}

.btn .icon:last-child {
  margin-right: 0;
  margin-left: var(--spacing-xs);
}

.btn-icon .icon {
  margin: 0;
}

/* Loading state */
.btn.loading {
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Form Controls */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
}

.form-label.required::after {
  content: ' *';
  color: var(--error-color);
}

.form-control {
  display: block;
  width: 100%;
  height: var(--input-height);
  padding: var(--input-padding-y) var(--input-padding-x);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--gray-900);
  background-color: var(--white);
  background-image: none;
  border: var(--input-border-width) solid var(--input-border-color);
  border-radius: var(--input-border-radius);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-xs);
  position: relative;
}

.form-control:hover {
  border-color: var(--gray-400);
  box-shadow: var(--shadow-sm);
}

.form-control:focus {
  outline: none;
  border-color: var(--input-focus-border-color);
  box-shadow: var(--input-focus-shadow), var(--shadow-sm);
  transform: translateY(-1px);
}

.form-control:disabled {
  background-color: var(--gray-100);
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.form-control.is-invalid {
  border-color: var(--error-color);
  box-shadow: var(--shadow-focus-error);
}

.form-control.is-invalid:focus {
  border-color: var(--error-color);
  box-shadow: var(--shadow-focus-error), var(--shadow-sm);
}

.form-control.is-valid {
  border-color: var(--success-color);
  box-shadow: var(--shadow-focus-success);
}

/* Textarea */
.form-control.textarea {
  height: auto;
  min-height: 80px;
  resize: vertical;
}

/* Select */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 32px;
  appearance: none;
}

/* File input */
.form-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: var(--input-height);
  margin-bottom: 0;
}

.form-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: var(--input-height);
  margin: 0;
  opacity: 0;
  cursor: pointer;
}

.form-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: var(--input-height);
  padding: var(--input-padding-y) var(--input-padding-x);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--gray-700);
  background-color: var(--white);
  border: var(--input-border-width) solid var(--input-border-color);
  border-radius: var(--input-border-radius);
  cursor: pointer;
}

.form-file-input:focus ~ .form-file-label {
  border-color: var(--input-focus-border-color);
  box-shadow: var(--input-focus-shadow);
}

/* Form validation */
.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--error-color);
}

.valid-feedback {
  display: block;
  width: 100%;
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--success-color);
}

/* Cards */
.card {
  background-color: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

.card-title {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
}

.card-subtitle {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.card-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Button hint text - Enterprise optimized */
.btn-hint {
  /* Typography - Enhanced readability */
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  color: var(--gray-700);

  /* Layout - Professional spacing */
  margin-left: var(--spacing-md);
  padding: var(--spacing-xs) var(--spacing-sm);

  /* Visual design - Subtle enterprise styling */
  background: linear-gradient(135deg, var(--info-bg) 0%, rgba(240, 249, 255, 0.6) 100%);
  border: 1px solid var(--info-border);
  border-radius: var(--border-radius-md);

  /* Interactive effects */
  transition: all var(--transition-fast);
  position: relative;

  /* Text behavior */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 280px;

  /* Accessibility */
  user-select: none;
  cursor: default;
}

/* Subtle hover effect for enhanced interactivity */
.btn-hint:hover {
  background: linear-gradient(135deg, var(--info-bg) 0%, rgba(240, 249, 255, 0.8) 100%);
  border-color: var(--info-light);
  color: var(--gray-800);
  transform: translateY(-1px);
  box-shadow: var(--shadow-xs);
}

/* Icon enhancement for better visual hierarchy */
.btn-hint::before {
  content: "💡";
  margin-right: var(--spacing-xs);
  font-size: 0.875em;
  opacity: 0.8;
}

/* Responsive behavior */
@media (max-width: 768px) {
  .btn-hint {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs);
    margin-left: var(--spacing-sm);
    max-width: 200px;
  }

  .btn-hint::before {
    display: none;
  }
}

/* Enhanced Tables */
.table {
  width: 100%;
  margin-bottom: var(--spacing-lg);
  background-color: var(--white);
  border-collapse: collapse;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--table-border-color);
}

.table th,
.table td {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: left;
  border-bottom: 1px solid var(--table-border-color);
  vertical-align: middle;
  transition: all var(--transition-fast);
}

.table th {
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  background: linear-gradient(135deg, var(--table-header-bg) 0%, #f8fafc 100%);
  border-bottom: 2px solid var(--gray-300);
  font-size: var(--font-size-sm);
  letter-spacing: 0.025em;
  text-transform: uppercase;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table tbody tr {
  transition: all var(--transition-fast);
}

.table tbody tr:hover {
  background-color: var(--table-row-hover-bg);
  transform: scale(1.001);
  box-shadow: inset 0 0 0 1px var(--primary-200);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

.table-bordered {
  border: 1px solid var(--gray-200);
}

.table-bordered th,
.table-bordered td {
  border: 1px solid var(--gray-200);
}

/* Enhanced Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-modal);
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
  animation: var(--animation-fade-in);
  pointer-events: auto;
}

.modal.show {
  display: block;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-modal-backdrop);
  width: 100vw;
  height: 100vh;
  background: var(--modal-backdrop-bg);
  backdrop-filter: blur(2px);
  animation: var(--animation-fade-in);
  pointer-events: auto;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: var(--spacing-lg);
  pointer-events: none;
  display: flex;
  align-items: center;
  min-height: calc(100vh - 2rem);
  z-index: calc(var(--z-modal-backdrop) + 1);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: var(--white);
  background-clip: padding-box;
  border: none;
  border-radius: var(--modal-border-radius);
  box-shadow: var(--modal-shadow);
  outline: 0;
  animation: var(--animation-scale-in);
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
}

.modal-close {
  padding: 0;
  margin: 0;
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--gray-500);
  cursor: pointer;
  transition: color var(--transition-fast);
  outline: none;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-md);
}

.modal-close:hover {
  color: var(--gray-700);
  background-color: var(--gray-100);
}

.modal-close:focus {
  outline: none;
  background-color: var(--gray-100);
  box-shadow: 0 0 0 2px var(--gray-300);
}

.modal-close:active {
  background-color: var(--gray-200);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: var(--spacing-lg);
  pointer-events: auto;
}

.modal-body input,
.modal-body select,
.modal-body textarea,
.modal-body button {
  pointer-events: auto;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
}

/* Modal sizes */
.modal-sm .modal-dialog {
  max-width: 400px;
}

.modal-lg .modal-dialog {
  max-width: 800px;
}

.modal-xl .modal-dialog {
  max-width: 1200px;
}

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: var(--spacing-2xl) auto;
  }
}

/* Alerts */
.alert {
  position: relative;
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
}

.alert-success {
  color: var(--success-dark);
  background-color: rgba(76, 175, 80, 0.1);
  border-color: var(--success-light);
}

.alert-warning {
  color: var(--warning-dark);
  background-color: rgba(255, 152, 0, 0.1);
  border-color: var(--warning-light);
}

.alert-error {
  color: var(--error-dark);
  background-color: rgba(244, 67, 54, 0.1);
  border-color: var(--error-light);
}

.alert-info {
  color: var(--info-dark);
  background-color: rgba(33, 150, 243, 0.1);
  border-color: var(--info-light);
}

.alert-dismissible {
  padding-right: 3rem;
}

.alert-close {
  position: absolute;
  top: 0;
  right: 0;
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  cursor: pointer;
  opacity: 0.7;
  transition: opacity var(--transition-fast);
}

.alert-close:hover {
  opacity: 1;
}

/* Enhanced Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Enhanced Card Styles */
.card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  transition: all var(--transition-fast);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--card-shadow-hover);
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  border-bottom: 1px solid var(--card-border);
  padding: var(--spacing-lg);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  background: var(--gray-50);
  border-top: 1px solid var(--card-border);
  padding: var(--spacing-md) var(--spacing-lg);
}

/* Accessibility Enhancements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: var(--white);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--border-radius-md);
  z-index: 1000;
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* Focus indicators */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn:focus,
.form-control:focus,
.nav-link:focus,
.modal-close:focus {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
  }

  .form-control {
    border-width: 2px;
  }

  .table th,
  .table td {
    border-width: 2px;
  }

  .card {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .btn::before {
    display: none;
  }

  .nav-link::before {
    display: none;
  }
}

/* Color scheme preferences */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #f8f9fa;
    --bg-color: #212529;
    --card-bg: #343a40;
    --input-bg: #495057;
  }
}

/* Enhanced focus management */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
}

/* ARIA live regions */
.live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Enhanced button states for accessibility */
.btn[aria-pressed="true"] {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn[aria-expanded="true"] {
  background-color: var(--primary-light);
}

/* Form validation accessibility */
.form-control[aria-invalid="true"] {
  border-color: var(--error-color);
  box-shadow: var(--shadow-focus-error);
}

.form-control[aria-invalid="false"] {
  border-color: var(--success-color);
  box-shadow: var(--shadow-focus-success);
}

/* Table accessibility */
.table th[scope="col"],
.table th[scope="row"] {
  font-weight: var(--font-weight-semibold);
}

.table caption {
  padding: var(--spacing-md);
  color: var(--gray-600);
  text-align: left;
  caption-side: top;
}

/* Modal accessibility */
.modal[aria-hidden="true"] {
  display: none;
}

.modal[aria-hidden="false"] {
  display: block;
}

/* Navigation accessibility */
.nav-link[aria-current="page"] {
  background: var(--nav-link-active-bg);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* Field Mapping Specific Styles */
.airline-response-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: var(--spacing-sm);
  width: 100%;
}

.airline-response-textarea,
.supplementary-logic-textarea {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: 1.4;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm);
  transition: border-color var(--transition-fast);
}

.airline-response-textarea {
  flex: 1;
  min-height: 80px;
  resize: vertical;
}

.airline-response-textarea:focus,
.supplementary-logic-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.select-path-btn {
  flex-shrink: 0;
  align-self: center;
  font-size: var(--font-size-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  height: auto;
  min-height: 36px;
  white-space: nowrap;
}

/* Responsive design for airline response container */
@media (max-width: 768px) {
  .airline-response-container {
    flex-direction: column;
    align-items: stretch;
  }

  .select-path-btn {
    align-self: flex-start;
    margin-top: var(--spacing-xs);
  }

  /* Responsive design for JSON tree selector */
  .json-tree-selector {
    top: 10px;
    right: 10px;
    left: 10px;
    width: auto;
    height: calc(100vh - 20px);
  }
}

.mapping-rule-display {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: 1.4;
  background-color: var(--gray-50);
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm);
}

/* JSON Tree Selector Styles */
.json-tree-selector {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  height: calc(100vh - 40px);
  background: transparent;
  display: none;
  z-index: var(--z-modal);
  pointer-events: none;
}

.json-tree-selector.show {
  display: block;
  pointer-events: auto;
}

.json-tree-panel {
  background: white;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-2xl);
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid var(--gray-300);
}

.json-tree-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.json-tree-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
}

.json-tree-search {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
}

.json-tree-content {
  flex: 1;
  overflow: auto;
  padding: var(--spacing-md);
}

.json-tree {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.json-tree-node {
  margin-left: var(--spacing-md);
  position: relative;
}

.json-tree-node-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) 0;
  cursor: pointer;
  border-radius: var(--border-radius-sm);
  transition: background-color var(--transition-fast);
}

.json-tree-node-item:hover {
  background-color: var(--gray-100);
}

.json-tree-node-item.selected {
  background-color: var(--primary-50);
  color: var(--primary-color);
}

.json-tree-toggle {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-xs);
  color: var(--gray-500);
  font-size: var(--font-size-xs);
}

.json-tree-checkbox {
  margin-right: var(--spacing-xs);
  accent-color: var(--primary-color);
}

.json-tree-key {
  font-weight: var(--font-weight-medium);
  color: var(--gray-900);
}

.json-tree-type {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
  margin-left: var(--spacing-xs);
  font-style: italic;
}

.json-tree-node-item.highlighted {
  background-color: rgba(255, 235, 59, 0.3);
  border-radius: var(--border-radius-sm);
}

.json-tree-children {
  margin-left: var(--spacing-lg);
  border-left: 1px solid var(--gray-200);
  padding-left: var(--spacing-sm);
}
