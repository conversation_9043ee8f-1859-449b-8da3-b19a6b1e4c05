/**
 * Field Mapping JavaScript functionality
 */

// Global variables
let currentFieldMappingId = null;
let currentMappingRules = [];
let currentJsonData = null;
let currentTargetInput = null;
let selectedJsonPaths = new Set();
let jsonTreeSelector = null;

// Tree data for cascading dropdowns
let platformTreeData = null;
let supplierTreeData = null;

// Store interface IDs for API calls
let platformInterfaceIdMap = new Map(); // key: "businessDomain-interfaceName-version", value: interfaceId
let supplierInterfaceIdMap = new Map(); // key: "businessDomain-supplierName-interfaceName-version", value: interfaceId

// Store available prompt versions for current interface combination
let availablePromptVersions = [];

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeFieldMapping();
});

async function initializeFieldMapping() {
    try {
        // Load initial data first
        await loadDropdownData();

        // Check for URL parameters after dropdown data is loaded
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('associationId')) {
            await loadFieldMappingByAssociation(urlParams.get('associationId'));
        } else if (urlParams.has('businessDomain')) {
            // Auto-populate form from URL parameters
            populateFormFromParams(urlParams);

            // If we have enough parameters, automatically search
            if (hasRequiredSearchParams(urlParams)) {
                setTimeout(() => {
                    searchFieldMapping();
                }, 500); // Small delay to ensure UI is ready
            }
        }

        // Initialize character count for general logic textarea
        initializeCharacterCount();

        // Initialize JSON tree selector
        initializeJsonTreeSelector();

        console.log('Field mapping initialization completed successfully');
    } catch (error) {
        console.error('Error initializing field mapping:', error);
        MappingGen.Utils.showAlert('页面初始化失败：' + error.message, 'error');
    }
}

async function loadDropdownData() {
    try {
        console.log('Loading tree data for cascading dropdowns...');
        
        // Load platform interface tree data
        const platformResponse = await MappingGen.API.get('/api/platform-interface/tree');
        if (platformResponse.success && platformResponse.data) {
            platformTreeData = platformResponse.data;
            buildPlatformInterfaceIdMap(platformTreeData);
            console.log('Platform tree data loaded:', platformTreeData);
        } else {
            throw new Error('获取平台接口树形数据失败：' + (platformResponse.message || '未知错误'));
        }
        
        // Load supplier interface tree data
        const supplierResponse = await MappingGen.API.get('/api/supplier-interface/tree');
        if (supplierResponse.success && supplierResponse.data) {
            supplierTreeData = supplierResponse.data;
            buildSupplierInterfaceIdMap(supplierTreeData);
            console.log('Supplier tree data loaded:', supplierTreeData);
        } else {
            throw new Error('获取供应商接口树形数据失败：' + (supplierResponse.message || '未知错误'));
        }
        
        // Initialize cascading dropdowns
        initializeCascadingDropdowns();
        
        console.log('Field mapping dropdowns initialized successfully');
    } catch (error) {
        console.error('Error loading dropdown data:', error);
        MappingGen.Utils.showAlert('加载下拉数据失败：' + error.message, 'error');
        throw error; // Re-throw to handle in initialization
    }
}

function hasRequiredSearchParams(urlParams) {
    // Check if we have the minimum required parameters for automatic search
    const requiredFields = ['businessDomain', 'platformInterface', 'vendor', 'vendorInterface'];
    return requiredFields.every(field => urlParams.has(field) && urlParams.get(field).trim() !== '');
}

function populateFormFromParams(urlParams) {
    console.log('Populating form from URL parameters:', Object.fromEntries(urlParams));

    const fields = ['businessDomain', 'platformInterface', 'platformVersion', 'vendor', 'vendorInterface', 'vendorVersion'];
    const populatedFields = [];

    fields.forEach(field => {
        if (urlParams.has(field)) {
            const element = document.getElementById(field);
            if (element) {
                const value = urlParams.get(field);
                element.value = value;
                populatedFields.push(`${field}: ${value}`);

                // Trigger change event to ensure any dependent logic is executed
                element.dispatchEvent(new Event('change', { bubbles: true }));
            } else {
                console.warn(`Form element not found for field: ${field}`);
            }
        }
    });

    console.log('Populated fields:', populatedFields);

    if (populatedFields.length > 0) {
        MappingGen.Utils.showAlert(`已从URL参数填充 ${populatedFields.length} 个字段`, 'info');
    }
}

async function loadFieldMappingByAssociation(associationId) {
    try {
        console.log('🔄 开始加载字段映射，关联ID:', associationId);

        // 调用新的根据映射ID查询字段映射API
        const response = await MappingGen.API.get('/api/field-mapping/getFieldMappingByMappingId', {
            platformSupplierId: associationId
        });

        if (response.success) {
            const data = response.data;
            console.log('📥 后端返回的字段映射数据:', data);

            // 存储映射数据
            currentFieldMappingId = associationId;
            currentMappingRules = convertFieldMappingsToRules(data.fieldMappings || []);

            // 存储供应商字段JSON数据
            if (data.supplierFields) {
                try {
                    currentJsonData = JSON.parse(data.supplierFields);
                } catch (e) {
                    console.warn('Failed to parse supplier fields JSON:', e);
                    currentJsonData = null;
                }
            }

            // 使用后端返回的6个字段优先填充表单，而不是从URL参数获取
            const formFields = {
                businessDomain: data.businessDomain || '',
                platformInterface: data.platformInterfaceName || '',
                platformVersion: data.platformVersion || '',
                vendor: data.supplierName || '',
                vendorInterface: data.supplierInterfaceName || '',
                vendorVersion: data.supplierVersion || ''
            };

            console.log('📋 准备填充的表单字段:', formFields);

            // 验证必要字段是否存在
            const requiredFields = ['businessDomain', 'platformInterface', 'platformVersion', 'vendor', 'vendorInterface', 'vendorVersion'];
            const missingFields = requiredFields.filter(field => !formFields[field]);
            if (missingFields.length > 0) {
                console.warn('⚠️ 缺少必要字段:', missingFields);
            }

            // 设置加载状态标记，防止在填充过程中触发级联更新
            const allSelects = [
                document.getElementById('businessDomain'),
                document.getElementById('platformInterface'),
                document.getElementById('platformVersion'),
                document.getElementById('vendor'),
                document.getElementById('vendorInterface'),
                document.getElementById('vendorVersion')
            ];

            allSelects.forEach(select => {
                if (select) {
                    select.setAttribute('data-loading-state', 'true');
                }
            });

            // 填充表单字段
            Object.keys(formFields).forEach(field => {
                const element = document.getElementById(field);
                if (element && formFields[field]) {
                    element.value = formFields[field];
                    console.log(`✅ 设置字段 ${field} = ${formFields[field]}`);
                } else if (element) {
                    console.log(`⚠️ 字段 ${field} 为空或元素不存在`);
                }
            });

            // 强制刷新级联下拉框数据以确保一致性
            console.log('🔄 开始刷新级联下拉框...');
            await refreshCascadingDropdowns(formFields);

            // 移除加载状态标记
            allSelects.forEach(select => {
                if (select) {
                    select.removeAttribute('data-loading-state');
                }
            });

            // Show field mapping content
            document.getElementById('noSelectionState').style.display = 'none';
            document.getElementById('fieldMappingContent').style.display = 'block';

            // Populate general logic
            document.getElementById('generalLogic').value = data.commonLogic || '';
            updateCharacterCount(document.getElementById('generalLogic'));

            // Populate mapping rules table
            displayMappingRules(currentMappingRules);

            console.log('✅ 字段映射加载完成');
            MappingGen.Utils.showAlert('字段映射加载成功', 'success');
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('❌ 加载字段映射失败:', error);
        MappingGen.Utils.showAlert('加载字段映射失败：' + error.message, 'error');
    }
}

// 新增：强制刷新级联下拉框的函数
async function refreshCascadingDropdowns(formFields) {
    const businessDomainSelect = document.getElementById('businessDomain');
    const platformInterfaceSelect = document.getElementById('platformInterface');
    const platformVersionSelect = document.getElementById('platformVersion');
    const vendorSelect = document.getElementById('vendor');
    const vendorInterfaceSelect = document.getElementById('vendorInterface');
    const vendorVersionSelect = document.getElementById('vendorVersion');

    // 设置加载状态标记，防止在刷新过程中触发事件
    const allSelects = [businessDomainSelect, platformInterfaceSelect, platformVersionSelect,
                       vendorSelect, vendorInterfaceSelect, vendorVersionSelect];

    allSelects.forEach(select => {
        if (select) {
            select.setAttribute('data-refreshing', 'true');
        }
    });

    try {
        // 1. 设置业务域选中值
        if (formFields.businessDomain && businessDomainSelect) {
            businessDomainSelect.value = formFields.businessDomain;

            // 更新平台接口下拉框
            updatePlatformInterfaces(formFields.businessDomain, platformInterfaceSelect, platformVersionSelect);

            // 更新供应商下拉框
            updateSuppliers(formFields.businessDomain, vendorSelect, vendorInterfaceSelect, vendorVersionSelect);
        }

        // 等待DOM更新
        await new Promise(resolve => setTimeout(resolve, 50));

        // 2. 设置平台接口选中值并更新版本
        if (formFields.businessDomain && formFields.platformInterface && platformInterfaceSelect) {
            // 验证选项是否存在
            const platformInterfaceOption = Array.from(platformInterfaceSelect.options).find(option => option.value === formFields.platformInterface);
            if (platformInterfaceOption) {
                platformInterfaceSelect.value = formFields.platformInterface;

                // 更新平台版本下拉框
                updatePlatformVersions(formFields.businessDomain, formFields.platformInterface, platformVersionSelect);
            } else {
                console.warn('❌ Platform interface option not found:', formFields.platformInterface);
                console.warn('Available options:', Array.from(platformInterfaceSelect.options).map(opt => opt.value));
            }
        }

        // 3. 设置供应商选中值并更新接口
        if (formFields.businessDomain && formFields.vendor && vendorSelect) {
            // 验证选项是否存在
            const vendorOption = Array.from(vendorSelect.options).find(option => option.value === formFields.vendor);
            if (vendorOption) {
                vendorSelect.value = formFields.vendor;

                // 更新供应商接口下拉框
                updateVendorInterfaces(formFields.businessDomain, formFields.vendor, vendorInterfaceSelect, vendorVersionSelect);
            } else {
                console.warn('❌ Vendor option not found:', formFields.vendor);
                console.warn('Available options:', Array.from(vendorSelect.options).map(opt => opt.value));
            }
        }

        // 等待DOM更新
        await new Promise(resolve => setTimeout(resolve, 50));

        // 4. 设置平台版本选中值
        if (formFields.businessDomain && formFields.platformInterface && formFields.platformVersion && platformVersionSelect) {
            // 验证选项是否存在
            const platformVersionOption = Array.from(platformVersionSelect.options).find(option => option.value === formFields.platformVersion);
            if (platformVersionOption) {
                platformVersionSelect.value = formFields.platformVersion;
            } else {
                console.warn('❌ Platform version option not found:', formFields.platformVersion);
                console.warn('Available options:', Array.from(platformVersionSelect.options).map(opt => opt.value));
            }
        }

        // 5. 设置供应商接口选中值并更新版本
        if (formFields.businessDomain && formFields.vendor && formFields.vendorInterface && vendorInterfaceSelect) {
            // 验证选项是否存在
            const vendorInterfaceOption = Array.from(vendorInterfaceSelect.options).find(option => option.value === formFields.vendorInterface);
            if (vendorInterfaceOption) {
                vendorInterfaceSelect.value = formFields.vendorInterface;

                // 更新供应商版本下拉框
                updateVendorVersions(formFields.businessDomain, formFields.vendor, formFields.vendorInterface, vendorVersionSelect);
            } else {
                console.warn('❌ Vendor interface option not found:', formFields.vendorInterface);
                console.warn('Available options:', Array.from(vendorInterfaceSelect.options).map(opt => opt.value));
            }
        }

        // 等待DOM更新
        await new Promise(resolve => setTimeout(resolve, 50));

        // 6. 设置供应商版本选中值
        if (formFields.businessDomain && formFields.vendor && formFields.vendorInterface && formFields.vendorVersion && vendorVersionSelect) {
            // 验证选项是否存在
            const vendorVersionOption = Array.from(vendorVersionSelect.options).find(option => option.value === formFields.vendorVersion);
            if (vendorVersionOption) {
                vendorVersionSelect.value = formFields.vendorVersion;
            } else {
                console.warn('❌ Vendor version option not found:', formFields.vendorVersion);
                console.warn('Available options:', Array.from(vendorVersionSelect.options).map(opt => opt.value));
            }
        }

        console.log('✅ 下拉菜单级联回填完成:', formFields);
    } finally {
        // 移除加载状态标记
        allSelects.forEach(select => {
            if (select) {
                select.removeAttribute('data-refreshing');
            }
        });
    }
}


async function searchFieldMapping() {
    const form = document.getElementById('fieldMappingSearchForm');
    if (!MappingGen.Utils.validateForm(form)) {
        MappingGen.Utils.showAlert('请填写所有必填字段', 'warning');
        return;
    }
    
    try {
        const formData = MappingGen.Utils.getFormData(form);
        
        // 获取接口ID
        const platformInterfaceId = getPlatformInterfaceId(
            formData.businessDomain, 
            formData.platformInterface, 
            formData.platformVersion
        );
        
        const supplierInterfaceId = getSupplierInterfaceId(
            formData.businessDomain, 
            formData.vendor, 
            formData.vendorInterface, 
            formData.vendorVersion
        );
        
        if (!platformInterfaceId || !supplierInterfaceId) {
            MappingGen.Utils.showAlert('无法找到对应的接口ID，请检查选择的数据', 'error');
            return;
        }
        
        console.log('Searching field mapping with IDs:', { platformInterfaceId, supplierInterfaceId });
        
        // 调用新的字段映射API
        const response = await MappingGen.API.get('/api/field-mapping', {
            platformInterfaceId: platformInterfaceId,
            supplierInterfaceId: supplierInterfaceId
        });
        
        if (response.success) {
            const data = response.data;
            
            // 存储当前映射数据
            currentFieldMappingId = `${platformInterfaceId}_${supplierInterfaceId}`;
            currentMappingRules = convertFieldMappingsToRules(data.fieldMappings || []);
            
            // 存储供应商字段JSON数据
            if (data.supplierFields) {
                try {
                    currentJsonData = JSON.parse(data.supplierFields);
                } catch (e) {
                    console.warn('Failed to parse supplier fields JSON:', e);
                    currentJsonData = null;
                }
            }
            
            // Show field mapping content
            document.getElementById('noSelectionState').style.display = 'none';
            document.getElementById('fieldMappingContent').style.display = 'block';
            
            // Populate general logic
            document.getElementById('generalLogic').value = data.commonLogic || '';
            updateCharacterCount(document.getElementById('generalLogic'));
            
            // Populate mapping rules table
            displayMappingRules(currentMappingRules);
            
            MappingGen.Utils.showAlert('字段映射查询成功', 'success');
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error searching field mapping:', error);
        MappingGen.Utils.showAlert('查询字段映射失败：' + error.message, 'error');
        showNoSelectionState();
    }
}

function showNoSelectionState() {
    document.getElementById('fieldMappingContent').style.display = 'none';
    document.getElementById('noSelectionState').style.display = 'block';
}

function displayMappingRules(rules) {
    const tableBody = document.getElementById('mappingRulesTableBody');
    tableBody.innerHTML = '';
    
    if (!rules || rules.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="4" class="text-center text-secondary">暂无映射规则</td>';
        tableBody.appendChild(row);
        return;
    }
    
    rules.forEach((rule, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <div class="form-control" readonly>${rule.ifsField || ''}</div>
            </td>
            <td>
                <div class="airline-response-container">
                    <textarea class="form-control airline-response-textarea" 
                              id="airlineResponse_${index}" 
                              placeholder="请选择或输入路径..."
                              onchange="updateMappingRule(${index}, 'airlineResponseResult', this.value)"
                              style="min-height: 80px; resize: vertical; overflow-y: auto;">${rule.airlineResponseResult || ''}</textarea>
                    <button type="button" class="btn btn-outline btn-sm select-path-btn" 
                            onclick="openJsonTreeSelector('airlineResponse_${index}')">
                        选择路径
                    </button>
                </div>
            </td>
            <td>
                <textarea class="form-control supplementary-logic-textarea" 
                          id="supplementaryLogic_${index}"
                          placeholder="请输入补充逻辑..."
                          onchange="updateMappingRule(${index}, 'supplementaryLogic', this.value)"
                          style="min-height: 80px; resize: vertical; overflow-y: auto;">${rule.supplementaryLogic || ''}</textarea>
            </td>
            <td>
                <div class="form-control mapping-rule-display" readonly 
                     style="min-height: 80px; white-space: pre-wrap; overflow-y: auto; resize: vertical;">${rule.mappingRule || ''}</div>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function updateMappingRule(index, field, value) {
    if (currentMappingRules[index]) {
        currentMappingRules[index][field] = value;
    }
}

async function loadJsonData() {
    try {
        const response = await MappingGen.API.get('/api/field-mapping/json-sample');
        
        if (response.success) {
            currentJsonData = response.data;
            console.log('JSON data loaded successfully');
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error loading JSON data:', error);
        MappingGen.Utils.showAlert('加载JSON数据失败：' + error.message, 'error');
    }
}

function initializeCharacterCount() {
    const textarea = document.getElementById('generalLogic');
    if (textarea) {
        initializeTextareaCharCount(textarea);
    }
}

function initializeTextareaCharCount(textarea) {
    const updateCount = () => updateCharacterCount(textarea);
    textarea.addEventListener('input', updateCount);
    textarea.addEventListener('keyup', updateCount);
    updateCount(); // Initial count
}

function updateCharacterCount(textarea) {
    const charCountElement = textarea.parentElement.querySelector('.char-count');
    if (charCountElement) {
        charCountElement.textContent = textarea.value.length;
    }
}

// JSON Tree Selector Implementation
function initializeJsonTreeSelector() {
    jsonTreeSelector = document.getElementById('jsonTreeSelector');

    // Close button event
    const closeBtn = jsonTreeSelector.querySelector('.btn-ghost');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeJsonTreeSelector);
    }

    // Backdrop click to close
    jsonTreeSelector.addEventListener('click', function(e) {
        if (e.target === jsonTreeSelector) {
            closeJsonTreeSelector();
        }
    });

    // Search functionality
    const searchInput = document.getElementById('jsonTreeSearchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            searchJsonNodes(this.value);
        });
    }

    // Confirm selection button
    const confirmBtn = jsonTreeSelector.querySelector('.btn-primary');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', confirmJsonSelection);
    }
}

function openJsonTreeSelector(targetInputId) {
    currentTargetInput = document.getElementById(targetInputId);
    if (!currentTargetInput) {
        console.error('Target input not found:', targetInputId);
        return;
    }

    if (!currentJsonData) {
        MappingGen.Utils.showAlert('请先查询字段映射以加载JSON数据', 'warning');
        return;
    }

    // Clear previous selections
    selectedJsonPaths.clear();

    // Pre-select existing paths from input value
    preSelectExistingPaths();

    // Render JSON tree
    renderJsonTree(currentJsonData);

    // Update checkbox states based on pre-selected paths
    updateCheckboxStates();

    // Show selector (non-blocking)
    jsonTreeSelector.classList.add('show');

    // Focus search input
    const searchInput = document.getElementById('jsonTreeSearchInput');
    if (searchInput) {
        searchInput.focus();
        searchInput.value = '';
    }
}

function closeJsonTreeSelector() {
    jsonTreeSelector.classList.remove('show');
    currentTargetInput = null;
    selectedJsonPaths.clear();
}

function preSelectExistingPaths() {
    if (!currentTargetInput || !currentTargetInput.value) {
        return;
    }

    // Parse existing paths from input value
    const existingValue = currentTargetInput.value.trim();
    if (existingValue) {
        // Split by newlines and filter out empty lines
        const paths = existingValue.split('\n')
            .map(path => path.trim())
            .filter(path => path.length > 0);

        // Add to selected paths
        paths.forEach(path => {
            selectedJsonPaths.add(path);
        });

        console.log('Pre-selected paths:', Array.from(selectedJsonPaths));
    }
}

function updateCheckboxStates() {
    const checkboxes = document.querySelectorAll('.json-tree-checkbox');
    checkboxes.forEach(checkbox => {
        const path = checkbox.dataset.path;
        if (selectedJsonPaths.has(path)) {
            checkbox.checked = true;
        }
    });
}

function renderJsonTree(data) {
    const container = document.getElementById('jsonTreeContainer');
    container.innerHTML = '';

    // Create root node
    const rootNode = document.createElement('div');
    rootNode.className = 'json-tree-node';

    // Build tree recursively
    buildJsonTreeNode(rootNode, data, 'root', '');

    container.appendChild(rootNode);
}

function buildJsonTreeNode(container, data, key, path, isArrayElement = false) {
    const nodeItem = document.createElement('div');
    nodeItem.className = 'json-tree-node-item';

    // Build path with proper array notation
    let currentPath;
    if (path === '') {
        currentPath = key;
    } else if (isArrayElement) {
        currentPath = `${path}[i]`;
    } else {
        currentPath = `${path}.${key}`;
    }

    // Toggle button for objects/arrays
    const hasChildren = data !== null && typeof data === 'object' && Object.keys(data).length > 0;
    const toggle = document.createElement('div');
    toggle.className = 'json-tree-toggle';

    if (hasChildren) {
        toggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
        toggle.style.cursor = 'pointer';
        toggle.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleJsonNode(this);
        });
    } else {
        toggle.innerHTML = '<span style="width: 12px; display: inline-block;"></span>';
    }

    // Checkbox for selection
    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.className = 'json-tree-checkbox';
    checkbox.dataset.path = currentPath;
    checkbox.addEventListener('change', function() {
        if (this.checked) {
            selectedJsonPaths.add(currentPath);
        } else {
            selectedJsonPaths.delete(currentPath);
        }
    });

    // Key name
    const keySpan = document.createElement('span');
    keySpan.className = 'json-tree-key';
    if (key === 'root') {
        keySpan.textContent = 'root';
    } else if (isArrayElement) {
        keySpan.textContent = `${key}[i]`;
    } else {
        keySpan.textContent = key;
    }

    // Type indicator
    const typeSpan = document.createElement('span');
    typeSpan.className = 'json-tree-type';
    typeSpan.textContent = getJsonDataType(data);

    nodeItem.appendChild(toggle);
    nodeItem.appendChild(checkbox);
    nodeItem.appendChild(keySpan);
    nodeItem.appendChild(typeSpan);

    container.appendChild(nodeItem);

    // Build children
    if (hasChildren) {
        const childrenContainer = document.createElement('div');
        childrenContainer.className = 'json-tree-children';

        if (Array.isArray(data)) {
            // For arrays, show structure of first element with [i] notation
            if (data.length > 0) {
                const sampleItem = data[0];
                if (typeof sampleItem === 'object' && sampleItem !== null) {
                    Object.keys(sampleItem).forEach(childKey => {
                        const childNode = document.createElement('div');
                        childNode.className = 'json-tree-node';
                        buildJsonTreeNode(childNode, sampleItem[childKey], childKey, currentPath, true);
                        childrenContainer.appendChild(childNode);
                    });
                }
            }
        } else {
            // For objects
            Object.keys(data).forEach(childKey => {
                const childNode = document.createElement('div');
                childNode.className = 'json-tree-node';
                buildJsonTreeNode(childNode, data[childKey], childKey, currentPath, false);
                childrenContainer.appendChild(childNode);
            });
        }

        container.appendChild(childrenContainer);
    }
}

function getJsonDataType(value) {
    if (value === null) return 'null';
    if (Array.isArray(value)) return 'array';
    return typeof value;
}

function toggleJsonNode(toggleElement) {
    const nodeItem = toggleElement.parentElement;
    const childrenContainer = nodeItem.parentElement.querySelector('.json-tree-children');

    if (childrenContainer) {
        const isCollapsed = childrenContainer.style.display === 'none';
        childrenContainer.style.display = isCollapsed ? 'block' : 'none';

        const icon = toggleElement.querySelector('i');
        if (icon) {
            icon.className = isCollapsed ? 'fas fa-chevron-down' : 'fas fa-chevron-right';
        }
    }
}

function searchJsonNodes(searchTerm) {
    const nodes = document.querySelectorAll('.json-tree-key');
    const term = searchTerm.toLowerCase().trim();

    nodes.forEach(node => {
        const nodeItem = node.parentElement;
        const key = node.textContent.toLowerCase();

        if (!term || key.includes(term)) {
            nodeItem.style.display = 'flex';
            // Highlight matching text
            if (term && key.includes(term)) {
                nodeItem.classList.add('highlighted');
            } else {
                nodeItem.classList.remove('highlighted');
            }
        } else {
            nodeItem.style.display = 'none';
            nodeItem.classList.remove('highlighted');
        }
    });
}

function confirmJsonSelection() {
    if (selectedJsonPaths.size === 0) {
        MappingGen.Utils.showAlert('请选择至少一个路径', 'warning');
        return;
    }

    if (!currentTargetInput) {
        console.error('No target input specified');
        return;
    }

    // Convert Set to Array and join with newlines for multiple selections
    const pathsArray = Array.from(selectedJsonPaths);
    const pathsText = pathsArray.join('\n');

    // Update target input
    if (currentTargetInput.tagName.toLowerCase() === 'textarea') {
        currentTargetInput.value = pathsText;
    } else {
        currentTargetInput.value = pathsArray[0]; // For input fields, use first selection
    }

    // Trigger change event
    const changeEvent = new Event('change', { bubbles: true });
    currentTargetInput.dispatchEvent(changeEvent);

    // Close selector
    closeJsonTreeSelector();

    MappingGen.Utils.showAlert(`已选择 ${pathsArray.length} 个路径`, 'success');
}

// Main functionality functions
async function generateMapping() {
    if (!currentFieldMappingId) {
        MappingGen.Utils.showAlert('请先查询字段映射', 'warning');
        return;
    }

    // Validate that at least one airline response result is filled
    const hasValidResponse = currentMappingRules.some(rule =>
        rule.airlineResponseResult && rule.airlineResponseResult.trim() !== ''
    );

    if (!hasValidResponse) {
        MappingGen.Utils.showAlert('请至少填写一个航空公司响应结果字段', 'warning');
        return;
    }

    try {
        // 获取当前的接口ID
        const [platformInterfaceId, supplierInterfaceId] = currentFieldMappingId.split('_');
        
        // 准备字段映射请求数据
        const fieldMappingRequests = convertRulesToFieldMappingRequests(currentMappingRules);
        
        // 调用新的生成映射API
        const response = await MappingGen.API.post('/api/field-mapping/generate', fieldMappingRequests, {
            params: {
                platformInterfaceId: parseInt(platformInterfaceId),
                supplierInterfaceId: parseInt(supplierInterfaceId),
                commonLogic: document.getElementById('generalLogic').value || ''
            }
        });

        if (response.success) {
            MappingGen.Utils.showAlert('映射规则生成成功', 'success');
            
            // 更新映射规则和显示
            if (response.data && Array.isArray(response.data)) {
                // 转换后端返回的数据为前端格式
                currentMappingRules = convertFieldMappingsToRules(response.data);
                displayMappingRules(currentMappingRules);
            }
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error generating mapping:', error);
        MappingGen.Utils.showAlert('生成映射失败：' + error.message, 'error');
    }
}

async function generateCodePrompt() {
    if (!currentFieldMappingId) {
        MappingGen.Utils.showAlert('请先查询字段映射', 'warning');
        return;
    }

    // Validate that at least one airline response result is filled
    const hasValidResponse = currentMappingRules.some(rule =>
        rule.airlineResponseResult && rule.airlineResponseResult.trim() !== ''
    );

    if (!hasValidResponse) {
        MappingGen.Utils.showAlert('请至少填写一个航空公司响应结果字段', 'warning');
        return;
    }

    try {
        // Step 1: Get current form data for parameters
        const formData = MappingGen.Utils.getFormData(document.getElementById('fieldMappingSearchForm'));
        
        // Step 2: Get interface IDs
        const platformInterfaceId = getPlatformInterfaceId(
            formData.businessDomain, 
            formData.platformInterface, 
            formData.platformVersion
        );
        
        const supplierInterfaceId = getSupplierInterfaceId(
            formData.businessDomain, 
            formData.vendor, 
            formData.vendorInterface, 
            formData.vendorVersion
        );
        
        if (!platformInterfaceId || !supplierInterfaceId) {
            MappingGen.Utils.showAlert('无法找到对应的接口ID，请检查选择的数据', 'error');
            return;
        }

        // Step 3: Get available prompt versions using new API
        MappingGen.Utils.showAlert('正在获取提示词版本...', 'info');

        const versionsResponse = await MappingGen.API.get('/api/prompt-version/versionList', {
            platformInterfaceId: platformInterfaceId,
            supplierInterfaceId: supplierInterfaceId
        });

        if (!versionsResponse.success || !versionsResponse.data || versionsResponse.data.length === 0) {
            throw new Error('未找到可用的提示词版本');
        }

        // Step 4: Select the latest version (first in the sorted list)
        const latestVersion = versionsResponse.data[0];
        console.log('Selected prompt version:', latestVersion);

        // Step 5: Create new Prompt version using new API
        MappingGen.Utils.showAlert('正在生成 CodePrompt...', 'info');

        const createParams = new URLSearchParams({
            platformInterfaceId: platformInterfaceId,
            supplierInterfaceId: supplierInterfaceId
        });
        
        const createResponse = await MappingGen.API.post(`/api/prompt-version?${createParams.toString()}`, {});

        if (createResponse.success) {
            MappingGen.Utils.showAlert('CodePrompt 生成成功，正在跳转...', 'success');

            // Step 6: Navigate to CodePrompt management page with all parameters
            const params = new URLSearchParams({
                ...formData,
                fieldMappingId: currentFieldMappingId,
                promptVersionId: createResponse.data.id,
                promptVersion: createResponse.data.version
            });

            setTimeout(() => {
                window.location.href = `/mapping-gen/code-prompt?${params.toString()}`;
            }, 1000);
        } else {
            throw new Error(createResponse.message);
        }
    } catch (error) {
        console.error('Error generating code prompt:', error);
        MappingGen.Utils.showAlert('生成CodePrompt失败：' + error.message, 'error');
    }
}

async function saveStage() {
    if (!currentFieldMappingId) {
        MappingGen.Utils.showAlert('请先查询字段映射', 'warning');
        return;
    }

    try {
        // 获取当前的接口ID
        const [platformInterfaceId, supplierInterfaceId] = currentFieldMappingId.split('_');
        
        // 准备字段映射请求数据
        const fieldMappingRequests = convertRulesToFieldMappingRequests(currentMappingRules);
        
        // 调用新的暂存API
        const response = await MappingGen.API.post('/api/field-mapping/save', fieldMappingRequests, {
            params: {
                platformInterfaceId: parseInt(platformInterfaceId),
                supplierInterfaceId: parseInt(supplierInterfaceId),
                commonLogic: document.getElementById('generalLogic').value || ''
            }
        });

        if (response.success) {
            MappingGen.Utils.showAlert('暂存成功', 'success');
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error saving stage:', error);
        MappingGen.Utils.showAlert('暂存失败：' + error.message, 'error');
    }
}

// ===== 级联下拉框相关函数 =====

/**
 * 构建平台接口ID映射
 */
function buildPlatformInterfaceIdMap(treeData) {
    platformInterfaceIdMap.clear();
    
    if (!treeData || !Array.isArray(treeData)) return;
    
    treeData.forEach(domain => {
        if (domain.businessDomain && domain.interfaces) {
            domain.interfaces.forEach(interfaceNode => {
                if (interfaceNode.interfaceName && interfaceNode.versions) {
                    interfaceNode.versions.forEach(versionNode => {
                        const key = `${domain.businessDomain}-${interfaceNode.interfaceName}-${versionNode.version}`;
                        platformInterfaceIdMap.set(key, versionNode.id);
                    });
                }
            });
        }
    });
    
    console.log('Platform interface ID map built:', platformInterfaceIdMap);
}

/**
 * 构建供应商接口ID映射
 */
function buildSupplierInterfaceIdMap(treeData) {
    supplierInterfaceIdMap.clear();
    
    if (!treeData || !Array.isArray(treeData)) return;
    
    treeData.forEach(domain => {
        if (domain.businessDomain && domain.suppliers) {
            domain.suppliers.forEach(supplierNode => {
                if (supplierNode.supplierName && supplierNode.interfaces) {
                    supplierNode.interfaces.forEach(interfaceNode => {
                        if (interfaceNode.interfaceName && interfaceNode.versions) {
                            interfaceNode.versions.forEach(versionNode => {
                                const key = `${domain.businessDomain}-${supplierNode.supplierName}-${interfaceNode.interfaceName}-${versionNode.version}`;
                                supplierInterfaceIdMap.set(key, versionNode.id);
                            });
                        }
                    });
                }
            });
        }
    });
    
    console.log('Supplier interface ID map built:', supplierInterfaceIdMap);
}

/**
 * 初始化级联下拉框
 */
function initializeCascadingDropdowns() {
    console.log('Initializing cascading dropdowns for field mapping...');

    // 获取表单元素
    const businessDomainSelect = document.getElementById('businessDomain');
    const platformInterfaceSelect = document.getElementById('platformInterface');
    const platformVersionSelect = document.getElementById('platformVersion');
    const vendorSelect = document.getElementById('vendor');
    const vendorInterfaceSelect = document.getElementById('vendorInterface');
    const vendorVersionSelect = document.getElementById('vendorVersion');

    if (!businessDomainSelect || !platformInterfaceSelect || !platformVersionSelect ||
        !vendorSelect || !vendorInterfaceSelect || !vendorVersionSelect) {
        console.warn('Some form elements not found for cascading dropdowns');
        return;
    }

    // 填充业务域选项
    populateBusinessDomains(businessDomainSelect);

    // 修改事件监听器，添加加载状态和刷新状态检查
    businessDomainSelect.addEventListener('change', function() {
        // 检查是否处于加载状态或刷新状态，如果是则不触发级联更新
        if (this.hasAttribute('data-loading-state') || this.hasAttribute('data-refreshing')) {
            console.log('Business domain changed during loading/refreshing, skipping cascading update');
            return;
        }

        const selectedDomain = this.value;
        console.log('Business domain changed:', selectedDomain);

        updatePlatformInterfaces(selectedDomain, platformInterfaceSelect, platformVersionSelect);
        updateSuppliers(selectedDomain, vendorSelect, vendorInterfaceSelect, vendorVersionSelect);
    });

    platformInterfaceSelect.addEventListener('change', function() {
        // 检查是否处于加载状态或刷新状态，如果是则不触发级联更新
        if (this.hasAttribute('data-loading-state') || 
            businessDomainSelect.hasAttribute('data-loading-state') || 
            this.hasAttribute('data-refreshing') || 
            businessDomainSelect.hasAttribute('data-refreshing')) {
            console.log('Platform interface changed during loading/refreshing, skipping cascading update');
            return;
        }

        const selectedDomain = businessDomainSelect.value;
        const selectedInterface = this.value;
        console.log('Platform interface changed:', selectedInterface);

        updatePlatformVersions(selectedDomain, selectedInterface, platformVersionSelect);
    });

    vendorSelect.addEventListener('change', function() {
        // 检查是否处于加载状态或刷新状态，如果是则不触发级联更新
        if (this.hasAttribute('data-loading-state') || this.hasAttribute('data-refreshing')) {
            console.log('Vendor changed during loading/refreshing, skipping cascading update');
            return;
        }

        const selectedDomain = businessDomainSelect.value;
        const selectedVendor = this.value;
        console.log('Vendor changed:', selectedVendor);

        updateVendorInterfaces(selectedDomain, selectedVendor, vendorInterfaceSelect, vendorVersionSelect);
    });

    vendorInterfaceSelect.addEventListener('change', function() {
        // 检查是否处于加载状态或刷新状态，如果是则不触发级联更新
        if (this.hasAttribute('data-loading-state') || 
            businessDomainSelect.hasAttribute('data-loading-state') || 
            vendorSelect.hasAttribute('data-loading-state') ||
            this.hasAttribute('data-refreshing') || 
            businessDomainSelect.hasAttribute('data-refreshing') || 
            vendorSelect.hasAttribute('data-refreshing')) {
            console.log('Vendor interface changed during loading/refreshing, skipping cascading update');
            return;
        }

        const selectedDomain = businessDomainSelect.value;
        const selectedVendor = vendorSelect.value;
        const selectedInterface = this.value;
        console.log('Vendor interface changed:', selectedInterface);

        updateVendorVersions(selectedDomain, selectedVendor, selectedInterface, vendorVersionSelect);
    });

    // 版本变更事件保持不变，用于加载提示词版本
    platformVersionSelect.addEventListener('change', checkAndLoadPromptVersions);
    vendorVersionSelect.addEventListener('change', checkAndLoadPromptVersions);
}

/**
 * 填充业务域下拉框（平台和供应商业务域的并集）
 */
function populateBusinessDomains(selectElement) {
    if (!selectElement) return;
    
    const businessDomains = new Set();
    
    // 收集平台业务域
    if (platformTreeData && Array.isArray(platformTreeData)) {
        platformTreeData.forEach(domain => {
            if (domain.businessDomain) {
                businessDomains.add(domain.businessDomain);
            }
        });
    }
    
    // 收集供应商业务域
    if (supplierTreeData && Array.isArray(supplierTreeData)) {
        supplierTreeData.forEach(domain => {
            if (domain.businessDomain) {
                businessDomains.add(domain.businessDomain);
            }
        });
    }
    
    // 清空并填充选项
    clearSelect(selectElement, '请选择业务领域');
    Array.from(businessDomains).sort().forEach(domain => {
        const option = document.createElement('option');
        option.value = domain;
        option.textContent = domain;
        selectElement.appendChild(option);
    });
    
    console.log('Business domains populated:', Array.from(businessDomains));
}

/**
 * 更新平台接口下拉框
 */
function updatePlatformInterfaces(businessDomain, interfaceSelect, versionSelect) {
    clearSelect(interfaceSelect, '请选择平台端接口');
    clearSelect(versionSelect, '请选择平台端版本');
    
    if (!businessDomain || !platformTreeData) return;
    
    const domainData = platformTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.interfaces) {
        domainData.interfaces.forEach(interfaceNode => {
            const option = document.createElement('option');
            option.value = interfaceNode.interfaceName;
            option.textContent = interfaceNode.interfaceName;
            interfaceSelect.appendChild(option);
        });
    }
}

/**
 * 更新平台版本下拉框
 */
function updatePlatformVersions(businessDomain, interfaceName, versionSelect) {
    clearSelect(versionSelect, '请选择平台端版本');
    
    if (!businessDomain || !interfaceName || !platformTreeData) return;
    
    const domainData = platformTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.interfaces) {
        const interfaceData = domainData.interfaces.find(i => i.interfaceName === interfaceName);
        if (interfaceData && interfaceData.versions) {
            interfaceData.versions.forEach(versionNode => {
                const option = document.createElement('option');
                option.value = versionNode.version;
                option.textContent = versionNode.version;
                versionSelect.appendChild(option);
            });
        }
    }
}

/**
 * 更新供应商下拉框
 */
function updateSuppliers(businessDomain, supplierSelect, interfaceSelect, versionSelect) {
    clearSelect(supplierSelect, '请选择供应商');
    clearSelect(interfaceSelect, '请选择供应商端接口');
    clearSelect(versionSelect, '请选择供应商端版本');
    
    if (!businessDomain || !supplierTreeData) return;
    
    const domainData = supplierTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.suppliers) {
        domainData.suppliers.forEach(supplierNode => {
            const option = document.createElement('option');
            option.value = supplierNode.supplierName;
            option.textContent = supplierNode.supplierName;
            supplierSelect.appendChild(option);
        });
    }
}

/**
 * 更新供应商接口下拉框
 */
function updateVendorInterfaces(businessDomain, supplierName, interfaceSelect, versionSelect) {
    clearSelect(interfaceSelect, '请选择供应商端接口');
    clearSelect(versionSelect, '请选择供应商端版本');
    
    if (!businessDomain || !supplierName || !supplierTreeData) return;
    
    const domainData = supplierTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.suppliers) {
        const supplierData = domainData.suppliers.find(s => s.supplierName === supplierName);
        if (supplierData && supplierData.interfaces) {
            supplierData.interfaces.forEach(interfaceNode => {
                const option = document.createElement('option');
                option.value = interfaceNode.interfaceName;
                option.textContent = interfaceNode.interfaceName;
                interfaceSelect.appendChild(option);
            });
        }
    }
}

/**
 * 更新供应商版本下拉框
 */
function updateVendorVersions(businessDomain, supplierName, interfaceName, versionSelect) {
    clearSelect(versionSelect, '请选择供应商端版本');
    
    if (!businessDomain || !supplierName || !interfaceName || !supplierTreeData) return;
    
    const domainData = supplierTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.suppliers) {
        const supplierData = domainData.suppliers.find(s => s.supplierName === supplierName);
        if (supplierData && supplierData.interfaces) {
            const interfaceData = supplierData.interfaces.find(i => i.interfaceName === interfaceName);
            if (interfaceData && interfaceData.versions) {
                interfaceData.versions.forEach(versionNode => {
                    const option = document.createElement('option');
                    option.value = versionNode.version;
                    option.textContent = versionNode.version;
                    versionSelect.appendChild(option);
                });
            }
        }
    }
}

/**
 * 清空下拉框并添加占位符选项
 */
function clearSelect(selectElement, placeholder) {
    if (!selectElement) return;
    
    selectElement.innerHTML = '';
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = placeholder;
    defaultOption.disabled = true;
    defaultOption.selected = true;
    selectElement.appendChild(defaultOption);
}

/**
 * 获取平台接口ID
 */
function getPlatformInterfaceId(businessDomain, interfaceName, version) {
    const key = `${businessDomain}-${interfaceName}-${version}`;
    return platformInterfaceIdMap.get(key);
}

/**
 * 获取供应商接口ID
 */
function getSupplierInterfaceId(businessDomain, supplierName, interfaceName, version) {
    const key = `${businessDomain}-${supplierName}-${interfaceName}-${version}`;
    return supplierInterfaceIdMap.get(key);
}

/**
 * 转换后端字段映射数据为前端规则格式
 */
function convertFieldMappingsToRules(fieldMappings) {
    if (!fieldMappings || !Array.isArray(fieldMappings)) {
        return [];
    }
    
    return fieldMappings.map(mapping => ({
        id: mapping.id,
        ifsField: mapping.platformField,
        airlineResponseResult: mapping.supplierField,
        supplementaryLogic: mapping.supplementaryLogic,
        mappingRule: mapping.mappingInfo
    }));
}

/**
 * 检查并加载提示词版本
 */
async function checkAndLoadPromptVersions() {
    const businessDomainSelect = document.getElementById('businessDomain');
    const platformInterfaceSelect = document.getElementById('platformInterface');
    const platformVersionSelect = document.getElementById('platformVersion');
    const vendorSelect = document.getElementById('vendor');
    const vendorInterfaceSelect = document.getElementById('vendorInterface');
    const vendorVersionSelect = document.getElementById('vendorVersion');
    
    // 检查是否所有必要字段都已选择
    if (!businessDomainSelect.value || !platformInterfaceSelect.value || 
        !platformVersionSelect.value || !vendorSelect.value || 
        !vendorInterfaceSelect.value || !vendorVersionSelect.value) {
        console.log('Not all interface parameters selected yet, skipping prompt version loading');
        return;
    }
    
    try {
        // 获取接口ID
        const platformInterfaceId = getPlatformInterfaceId(
            businessDomainSelect.value, 
            platformInterfaceSelect.value, 
            platformVersionSelect.value
        );
        
        const supplierInterfaceId = getSupplierInterfaceId(
            businessDomainSelect.value, 
            vendorSelect.value, 
            vendorInterfaceSelect.value, 
            vendorVersionSelect.value
        );
        
        if (!platformInterfaceId || !supplierInterfaceId) {
            console.warn('Unable to find interface IDs for prompt version loading');
            return;
        }
        
        console.log('Loading prompt versions for interfaces:', { platformInterfaceId, supplierInterfaceId });
        
        // 查询可用的提示词版本
        const response = await MappingGen.API.get('/api/prompt-version/versionList', {
            platformInterfaceId: platformInterfaceId,
            supplierInterfaceId: supplierInterfaceId
        });
        
        if (response.success && response.data) {
            availablePromptVersions = response.data;
            displayPromptVersionsInfo(availablePromptVersions);
            console.log('Available prompt versions loaded:', availablePromptVersions);
        } else {
            console.warn('No prompt versions found or API error:', response.message);
            availablePromptVersions = [];
            displayPromptVersionsInfo([]);
        }
    } catch (error) {
        console.error('Error loading prompt versions:', error);
        availablePromptVersions = [];
        displayPromptVersionsInfo([]);
    }
}

/**
 * 显示提示词版本信息
 */
function displayPromptVersionsInfo(versions) {
    // 查找显示提示词版本信息的容器
    let versionInfoContainer = document.getElementById('promptVersionInfo');
    
    // 如果容器不存在，创建一个
    if (!versionInfoContainer) {
        versionInfoContainer = document.createElement('div');
        versionInfoContainer.id = 'promptVersionInfo';
        versionInfoContainer.className = 'prompt-version-info mt-3';
        
        // 找到合适的位置插入（在查询按钮附近）
        const searchButton = document.querySelector('#fieldMappingSearchForm button[type="submit"]');
        if (searchButton && searchButton.parentElement) {
            searchButton.parentElement.appendChild(versionInfoContainer);
        }
    }
    
    if (versions && versions.length > 0) {
        versionInfoContainer.innerHTML = `
            <div class="alert alert-info">
                <strong>可用提示词版本：</strong>
                ${versions.map(version => `<span class="badge badge-primary mr-1">${version}</span>`).join('')}
                <br>
                <small class="text-muted">生成CodePrompt时将使用最新版本（${versions[0]}）</small>
            </div>
        `;
        versionInfoContainer.style.display = 'block';
    } else {
        versionInfoContainer.innerHTML = `
            <div class="alert alert-warning">
                <strong>提示：</strong> 当前接口组合暂无可用的提示词版本，生成CodePrompt时将创建新版本。
            </div>
        `;
        versionInfoContainer.style.display = 'block';
    }
}

/**
 * 转换前端规则为后端字段映射请求格式
 */
function convertRulesToFieldMappingRequests(rules) {
    if (!rules || !Array.isArray(rules)) {
        return [];
    }
    
    return rules.map(rule => ({
        platformField: rule.ifsField,
        supplierField: rule.airlineResponseResult,
        supplementaryLogic: rule.supplementaryLogic,
        mappingInfo: rule.mappingRule || 'DIRECT'
    }));
}
