/**
 * Knowledge Base JavaScript functionality
 * 企业级知识库管理系统
 */

// =============================================================================
// 命名空间封装 - 企业级模块化结构
// =============================================================================
const KnowledgeBase = {
    // 状态管理
    state: {
        currentPlatformPage: 0,
        currentVendorPage: 0,
        currentDeleteId: null,
        currentDeleteType: null,
        platformTreeData: null,
        supplierTreeData: null,
        isInitialized: false
    },
    
    // 配置项
    config: {
        pageSize: 10,
        fileTypes: '.txt,.md,.json,.java,.js',
        maxFileSize: 5 * 1024 * 1024, // 5MB
        retryAttempts: 3,
        allowedMimeTypes: [
            'text/plain', 
            'text/markdown', 
            'application/json', 
            'text/javascript', 
            'application/javascript'
        ]
    },
    
    // 工具方法
    utils: {
        // 事件管理器 - 防止内存泄漏
        EventManager: {
            listeners: new Map(),
            
            addListener(element, event, handler, options = {}) {
                if (!element) {
                    console.warn('事件管理器：元素不存在，跳过添加监听器');
                    return null;
                }
                
                const wrappedHandler = (e) => {
                    try {
                        handler(e);
                    } catch (error) {
                        console.error('事件处理器执行错误:', error);
                        KnowledgeBase.utils.handleError(error, '事件处理');
                    }
                };
                
                element.addEventListener(event, wrappedHandler, options);
                
                const key = `${element.id || 'anonymous'}_${event}_${Date.now()}`;
                if (!this.listeners.has(key)) {
                    this.listeners.set(key, []);
                }
                this.listeners.get(key).push({ 
                    element, 
                    event, 
                    handler: wrappedHandler 
                });
                
                return { key, handler: wrappedHandler };
            },
            
            removeListener(key) {
                if (this.listeners.has(key)) {
                    const listenerGroup = this.listeners.get(key);
                    listenerGroup.forEach(({ element, event, handler }) => {
                        element?.removeEventListener?.(event, handler);
                    });
                    this.listeners.delete(key);
                }
            },
            
            removeAllListeners() {
                this.listeners.forEach((listenerGroup, key) => {
                    listenerGroup.forEach(({ element, event, handler }) => {
                        try {
                            element?.removeEventListener?.(event, handler);
                        } catch (error) {
                            console.warn('移除事件监听器时出错:', error);
                        }
                    });
                });
                this.listeners.clear();
                console.log('✅ 所有事件监听器已清理');
            }
        },
        
        // 文件安全验证器
        FileValidator: {
            validateFile(file) {
                if (!file) {
                    throw new Error('文件对象不存在');
                }
                
                // 文件大小检查
                if (file.size > KnowledgeBase.config.maxFileSize) {
                    throw new Error(`文件大小超出限制（最大${KnowledgeBase.config.maxFileSize / (1024 * 1024)}MB）`);
                }
                
                // 文件扩展名检查
                const extension = '.' + file.name.split('.').pop().toLowerCase();
                const allowedExtensions = KnowledgeBase.config.fileTypes.split(',');
                if (!allowedExtensions.includes(extension)) {
                    throw new Error(`不支持的文件类型：${extension}`);
                }
                
                // MIME类型检查
                if (file.type && !KnowledgeBase.config.allowedMimeTypes.includes(file.type)) {
                    console.warn(`可疑的文件MIME类型：${file.type}，文件名：${file.name}`);
                }
                
                // 文件名安全检查
                if (/[<>:"/\\|?*]/.test(file.name)) {
                    throw new Error('文件名包含非法字符');
                }
                
                return true;
            },
            
            sanitizeFileName(fileName) {
                return fileName.replace(/[<>:"/\\|?*]/g, '_').slice(0, 255);
            }
        },
        
        // 错误处理器
        handleError(error, context = '', showAlert = true) {
            const errorInfo = {
                message: error.message || '未知错误',
                context,
                timestamp: new Date().toISOString(),
                stack: error.stack
            };
            
            console.error('KnowledgeBase Error:', errorInfo);
            
            if (showAlert && typeof MappingGen !== 'undefined' && MappingGen.Utils) {
                const userMessage = this.getUserFriendlyMessage(error);
                MappingGen.Utils.showAlert(userMessage, 'error');
            }
        },
        
        getUserFriendlyMessage(error) {
            const errorMap = {
                'NetworkError': '网络连接异常，请检查网络后重试',
                'TimeoutError': '请求超时，请稍后重试',
                'ValidationError': '数据验证失败，请检查输入内容',
                'TypeError': '数据类型错误，请联系管理员'
            };
            
            return errorMap[error.name] || `操作失败：${error.message}`;
        },
        
        // 初始化事件管理器
        initEventManager() {
            // 页面卸载时清理所有事件监听器
            window.addEventListener('beforeunload', () => {
                this.EventManager.removeAllListeners();
            });
            
            // 页面隐藏时也清理（移动端支持）
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    console.log('页面隐藏，清理事件监听器');
                }
            });
        },
        
        // 初始化文件验证器
        initFileValidator() {
            console.log('✅ 文件安全验证器已初始化');
        }
    },
    
    // 平台相关方法
    platform: {},
    
    // 供应商相关方法
    vendor: {},
    
    // 初始化方法
    init: function() {
        if (this.state.isInitialized) {
            console.warn('知识库已经初始化，跳过重复初始化');
            return;
        }
        
        try {
            this.utils.initEventManager();
            this.utils.initFileValidator();
            this.state.isInitialized = true;
            console.log('✅ KnowledgeBase模块初始化成功');
        } catch (error) {
            console.error('❌ KnowledgeBase模块初始化失败:', error);
        }
    }
};

// =============================================================================
// 向后兼容全局变量（保持现有功能不受影响）
// =============================================================================
let currentPlatformPage = 0;
let currentVendorPage = 0;
let currentDeleteId = null;
let currentDeleteType = null;

// =============================================================================
// 向后兼容全局函数包装器（确保现有调用方式正常工作）
// =============================================================================

// 同步全局状态到模块状态
function syncGlobalState() {
    KnowledgeBase.state.currentPlatformPage = currentPlatformPage;
    KnowledgeBase.state.currentVendorPage = currentVendorPage;
    KnowledgeBase.state.currentDeleteId = currentDeleteId;
    KnowledgeBase.state.currentDeleteType = currentDeleteType;
}

// 从模块状态同步到全局状态
function syncModuleState() {
    currentPlatformPage = KnowledgeBase.state.currentPlatformPage;
    currentVendorPage = KnowledgeBase.state.currentVendorPage;
    currentDeleteId = KnowledgeBase.state.currentDeleteId;
    currentDeleteType = KnowledgeBase.state.currentDeleteType;
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // 初始化模块化结构
    KnowledgeBase.init();
    // 初始化传统功能（向后兼容）
    initializeKnowledgeBase();
});

function initializeKnowledgeBase() {
    // console.log('🚀 Initializing Knowledge Base...');
    
    // 立即进行页面状态验证
    validatePageState();
    
    // Load initial data
    loadDropdownData();

    // Initialize file upload handlers
    initializeFileUploads();

    // Initialize content based on URL parameter with delay to ensure DOM is ready
    setTimeout(() => {
        initializeContent();
    }, 100);
}

/**
 * 验证页面状态和元素
 */
function validatePageState() {
    // console.log('🔍 Validating page state...');
    
    const urlParams = new URLSearchParams(window.location.search);
    const activeTab = urlParams.get('type') || 'platform';
    
    console.log('📋 Page validation report:', {
        currentURL: window.location.href,
        detectedPageType: activeTab,
        pageTitle: document.querySelector('.page-title')?.textContent,
        hasVendorElements: {
            vendorBusinessDomain: !!document.getElementById('vendorBusinessDomain'),
            vendorName: !!document.getElementById('vendorName'),
            vendorInterface: !!document.getElementById('vendorInterface'),
            vendorVersion: !!document.getElementById('vendorVersion'),
            vendorKnowledgeTableBody: !!document.getElementById('vendorKnowledgeTableBody')
        },
        hasPlatformElements: {
            platformBusinessDomain: !!document.getElementById('platformBusinessDomain'),
            platformInterface: !!document.getElementById('platformInterface'),
            platformVersion: !!document.getElementById('platformVersion'),
            platformKnowledgeTableBody: !!document.getElementById('platformKnowledgeTableBody')
        }
    });
    
    // 验证页面类型与DOM元素的一致性
    if (activeTab === 'vendor') {
        const vendorElementsExist = document.getElementById('vendorBusinessDomain') && 
                                   document.getElementById('vendorName') && 
                                   document.getElementById('vendorInterface') && 
                                   document.getElementById('vendorVersion');
        
        if (!vendorElementsExist) {
            console.error('❌ 页面类型为vendor但缺少必要的DOM元素！');
        } else {
            console.log('✅ Vendor页面DOM元素验证通过');
        }
    } else if (activeTab === 'platform') {
        const platformElementsExist = document.getElementById('platformBusinessDomain') && 
                                     document.getElementById('platformInterface') && 
                                     document.getElementById('platformVersion');
        
        if (!platformElementsExist) {
            console.error('❌ 页面类型为platform但缺少必要的DOM元素！');
        } else {
            console.log('✅ Platform页面DOM元素验证通过');
        }
    }
}

function initializeContent() {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const activeTab = urlParams.get('type') || 'platform';

    // console.log('🚀 Initializing content for:', activeTab);
    // console.log('🔍 Complete URL info:', {
    //     href: window.location.href,
    //     search: window.location.search,
    //     pathname: window.location.pathname,
    //     urlParams: Object.fromEntries(urlParams)
    // });

    // Test API connectivity first
    testApiConnectivity().then(() => {
        // Load data for the active content
        // console.log('✅ API connectivity test passed, loading content data...');
        loadContentData(activeTab);
    }).catch(error => {
        console.error('❌ API connectivity test failed:', error);
        // Still try to load content, but show error
        console.log('⚠️ 尽管连接性测试失败，仍尝试加载内容数据...');
        loadContentData(activeTab);
        MappingGen.Utils.showAlert('API连接测试失败，可能影响数据加载', 'warning');
    });
}

async function testApiConnectivity() {
    console.log('🔍 Testing API connectivity...');
    try {
        // 获取URL参数，判断当前页面类型，增强调试信息
        const urlParams = new URLSearchParams(window.location.search);
        const activeTab = urlParams.get('type') || 'platform';
        
        console.log('🔍 API connectivity test - 页面类型判断:', {
            fullURL: window.location.href,
            search: window.location.search,
            activeTab: activeTab
        });
        
        if (activeTab === 'platform') {
            console.log('📱 执行平台接口连接性测试...');
            // 使用新的平台接口API进行连接性测试
            const response = await MappingGen.API.get('/api/platform-interface/list', {
                pageNum: 1,
                pageSize: 1
            });
            console.log('✅ Platform API connectivity test successful with new API:', response);
        return response;
        } else if (activeTab === 'vendor') {
            console.log('🏢 执行供应商接口连接性测试...');
            // 使用新的供应商接口API进行连接性测试
            const response = await MappingGen.API.get('/api/supplier-interface/list', {
                pageNum: 1,
                pageSize: 1
            });
            console.log('✅ Supplier API connectivity test successful with new API:', response);
            return response;
        } else {
            console.warn('⚠️ 未知的页面类型，跳过连接性测试:', activeTab);
            return null;
        }
    } catch (error) {
        console.error('❌ API connectivity test error:', error);
        throw error;
    }
}

function loadContentData(contentType) {
    console.log('📊 Loading content data for:', contentType);

    if (contentType === 'platform') {
        console.log('📱 Loading platform knowledge data...');
        loadPlatformKnowledge();
    } else if (contentType === 'vendor') {
        console.log('🏢 Loading vendor knowledge data...');
        loadVendorKnowledge();
    } else {
        console.warn('⚠️ 未知的内容类型:', contentType);
    }
}

// Tab switching functions removed - navigation now handled by sidebar only

function initializeFileUploads() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const label = this.nextElementSibling;
            const placeholder = label.querySelector('.file-placeholder');
            if (this.files.length > 0) {
                placeholder.textContent = this.files[0].name;
            } else {
                placeholder.textContent = '选择文件...';
            }
        });
    });
}

async function loadDropdownData() {
    try {
        console.log('Loading dropdown data using new APIs...');
        
        // 获取URL参数，判断当前页面类型，增强调试信息
        const urlParams = new URLSearchParams(window.location.search);
        const activeTab = urlParams.get('type') || 'platform';
        
        // 添加额外的页面类型确认
        const currentPageTitle = document.querySelector('.page-title')?.textContent;
        console.log('🔍 页面标题确认:', currentPageTitle);
        
        if (activeTab === 'platform') {
            console.log('📱 执行平台接口初始化流程...');
            // 调用新的平台接口树形结构API
            const response = await MappingGen.API.get('/api/platform-interface/tree');
            
            if (response.success && response.data) {
                // 存储树形数据供级联使用
                window.platformTreeData = response.data;
                // 初始化级联下拉框
                initializeCascadingDropdowns(response.data);
            } else {
                throw new Error(response.message || '获取平台接口树形数据失败');
            }
        } else if (activeTab === 'vendor') {
            // 调用新的供应商接口树形结构API
            const response = await MappingGen.API.get('/api/supplier-interface/tree');
            if (response.success && response.data) {
                // 存储树形数据供级联使用
                window.supplierTreeData = response.data;
                // 初始化供应商级联下拉框
                initializeSupplierCascadingDropdowns(response.data);
            } else {
                throw new Error(response.message || '获取供应商接口树形数据失败');
            }
        } else {
            console.warn('⚠️ 未知的页面类型:', activeTab);
        }
    } catch (error) {
        console.error('❌ Error loading dropdown data:', error);
        MappingGen.Utils.showAlert('加载下拉数据失败：' + error.message, 'error');
        // 根据页面类型进行有针对性的fallback处理
        const urlParams = new URLSearchParams(window.location.search);
        const activeTab = urlParams.get('type') || 'platform';
        if (activeTab === 'vendor') {
            // 供应商页面fallback：只有在确实无法获取供应商数据时才使用旧方式
            console.log('🏢 供应商页面fallback - 初始化空下拉框');
            initializeSupplierCascadingDropdowns([]);
        }

    }
}

/**
 * 初始化供应商级联下拉框（四层结构：业务域-供应商-接口-版本）
 * @param {Array} treeData - 从供应商接口API获取的树形数据
 */
function initializeSupplierCascadingDropdowns(treeData) {
    console.log('🏢 Initializing supplier cascading dropdowns with data:', treeData);
    
    // 获取所有下拉框元素
    const businessDomainSelect = document.getElementById('vendorBusinessDomain');
    const supplierSelect = document.getElementById('vendorName');
    const interfaceSelect = document.getElementById('vendorInterface');
    const versionSelect = document.getElementById('vendorVersion');
    

    // 验证必需的DOM元素
    if (!businessDomainSelect || !supplierSelect || !interfaceSelect || !versionSelect) {
        console.warn('❌ 部分供应商下拉框元素未找到，跳过级联初始化');
        console.log('🔍 Missing elements:', {
            vendorBusinessDomain: !businessDomainSelect,
            vendorName: !supplierSelect,
            vendorInterface: !interfaceSelect,
            vendorVersion: !versionSelect
        });
        return;
    }

    // 验证数据结构
    if (!Array.isArray(treeData)) {
        console.warn('❌ Invalid supplier tree data structure:', typeof treeData);
        // 仍然初始化空的下拉框
        clearSelect(businessDomainSelect, '请选择业务域');
        clearSelect(supplierSelect, '请选择供应商');
        clearSelect(interfaceSelect, '请选择接口');
        clearSelect(versionSelect, '请选择版本');
        return;
    }

    // 清空所有下拉框
    clearSelect(businessDomainSelect, '请选择业务域');
    clearSelect(supplierSelect, '请选择供应商');
    clearSelect(interfaceSelect, '请选择接口');
    clearSelect(versionSelect, '请选择版本');

    // 空数据处理
    if (!Array.isArray(treeData) || treeData.length === 0) {
        return;
    }

    // 1. 填充业务域下拉框
    clearSelect(businessDomainSelect, '请选择业务域');
    treeData.forEach((domain, index) => {
        const option = document.createElement('option');
        option.value = domain.businessDomain;
        option.textContent = domain.businessDomain;
        businessDomainSelect.appendChild(option);
        console.log(`✅ 已添加业务域选项 ${index + 1}:`, domain.businessDomain);
    });
    
    // 2. 业务域变更事件 - 使用事件管理器
    KnowledgeBase.utils.EventManager.addListener(businessDomainSelect, 'change', function() {
        console.log('🔄 业务域变更:', this.value);
        syncGlobalState(); // 同步状态
        updateSupplierOptions(this.value, treeData, supplierSelect, interfaceSelect, versionSelect);
    });
    
    // 3. 供应商变更事件 - 使用事件管理器
    KnowledgeBase.utils.EventManager.addListener(supplierSelect, 'change', function() {
        console.log('🔄 供应商变更:', this.value);
        syncGlobalState(); // 同步状态
        updateSupplierInterfaceOptions(businessDomainSelect.value, this.value, treeData, interfaceSelect, versionSelect);
    });
    
    // 4. 接口变更事件 - 使用事件管理器
    KnowledgeBase.utils.EventManager.addListener(interfaceSelect, 'change', function() {
        console.log('🔄 接口变更:', this.value);
        syncGlobalState(); // 同步状态
        updateSupplierVersionOptions(businessDomainSelect.value, supplierSelect.value, this.value, treeData, versionSelect);
    });
}

/**
 * 更新供应商选项
 */
function updateSupplierOptions(businessDomain, treeData, supplierSelect, interfaceSelect, versionSelect) {
    console.log('🔄 更新供应商选项，业务域:', businessDomain);
    
    // 清空下级选项
    clearSelect(supplierSelect, '请选择供应商');
    clearSelect(interfaceSelect, '请选择接口');
    clearSelect(versionSelect, '请选择版本');
    
    if (!businessDomain) {
        console.log('⚠️ 业务域为空，跳过供应商选项更新');
        return;
    }
    
    // 找到对应的业务域
    const domain = treeData.find(d => d.businessDomain === businessDomain);
    if (!domain) {
        console.warn('❌ 未找到业务域:', businessDomain);
        console.log('🔍 可用业务域:', treeData.map(d => d.businessDomain));
        return;
    }
    
    if (!domain.suppliers || !Array.isArray(domain.suppliers)) {
        console.warn('❌ 业务域中没有供应商数据:', domain);
        return;
    }
    
    console.log('📋 找到供应商数据:', {
        businessDomain,
        suppliersCount: domain.suppliers.length,
        suppliers: domain.suppliers.map(s => s.supplierName)
    });
    
    // 填充供应商选项
    domain.suppliers.forEach((supplier, index) => {
        const option = document.createElement('option');
        option.value = supplier.supplierName;
        option.textContent = supplier.supplierName;
        supplierSelect.appendChild(option);
        console.log(`✅ 已添加供应商选项 ${index + 1}:`, supplier.supplierName);
    });
    
    console.log('✅ 供应商选项更新完成');
}

/**
 * 更新供应商接口选项
 */
function updateSupplierInterfaceOptions(businessDomain, supplierName, treeData, interfaceSelect, versionSelect) {
    // 清空下级选项
    clearSelect(interfaceSelect, '请选择接口');
    clearSelect(versionSelect, '请选择版本');
    
    if (!businessDomain || !supplierName) return;
    
    // 找到对应的供应商
    const domain = treeData.find(d => d.businessDomain === businessDomain);
    if (!domain || !domain.suppliers) return;
    
    const supplier = domain.suppliers.find(s => s.supplierName === supplierName);
    if (!supplier || !supplier.interfaces) return;
    
    // 填充接口选项
    supplier.interfaces.forEach(interfaceNode => {
        const option = document.createElement('option');
        option.value = interfaceNode.interfaceName;
        option.textContent = interfaceNode.interfaceName;
        interfaceSelect.appendChild(option);
    });
}

/**
 * 更新供应商版本选项
 */
function updateSupplierVersionOptions(businessDomain, supplierName, interfaceName, treeData, versionSelect) {
    // 清空版本选项
    clearSelect(versionSelect, '请选择版本');
    
    if (!businessDomain || !supplierName || !interfaceName) return;
    
    // 找到对应的接口
    const domain = treeData.find(d => d.businessDomain === businessDomain);
    if (!domain || !domain.suppliers) return;
    
    const supplier = domain.suppliers.find(s => s.supplierName === supplierName);
    if (!supplier || !supplier.interfaces) return;
    
    const interfaceNode = supplier.interfaces.find(i => i.interfaceName === interfaceName);
    if (!interfaceNode || !interfaceNode.versions) return;
    
    // 填充版本选项
    interfaceNode.versions.forEach(versionNode => {
        const option = document.createElement('option');
        option.value = versionNode.version;
        option.textContent = versionNode.version;
        versionSelect.appendChild(option);
    });
}

/**
 * 初始化级联下拉框
 * @param {Array} treeData - 从API获取的树形数据
 */
function initializeCascadingDropdowns(treeData) {
    console.log('Initializing cascading dropdowns...');
    
    // 获取下拉框元素
    const businessDomainSelect = document.getElementById('platformBusinessDomain');
    const interfaceSelect = document.getElementById('platformInterface');
    const versionSelect = document.getElementById('platformVersion');
    
    if (!businessDomainSelect || !interfaceSelect || !versionSelect) {
        console.error('One or more dropdown elements not found');
        return;
    }
    
    // 清空所有下拉框
    clearSelect(businessDomainSelect, '请选择业务领域');
    clearSelect(interfaceSelect, '请选择接口');
    clearSelect(versionSelect, '请选择版本');
    
    // 填充业务领域下拉框
    treeData.forEach(domain => {
        const option = document.createElement('option');
        option.value = domain.businessDomain;
        option.textContent = domain.businessDomain;
        businessDomainSelect.appendChild(option);
    });
    
    // 绑定业务领域变化事件 - 使用事件管理器
    KnowledgeBase.utils.EventManager.addListener(businessDomainSelect, 'change', function() {
        const selectedDomain = this.value;
        syncGlobalState(); // 同步状态
        updateInterfaceOptions(selectedDomain, treeData, interfaceSelect, versionSelect);
    });
    
    // 绑定接口变化事件 - 使用事件管理器
    KnowledgeBase.utils.EventManager.addListener(interfaceSelect, 'change', function() {
        const selectedDomain = businessDomainSelect.value;
        const selectedInterface = this.value;
        syncGlobalState(); // 同步状态
        updateVersionOptions(selectedDomain, selectedInterface, treeData, versionSelect);
    });
    
    console.log('Cascading dropdowns initialized');
}

/**
 * 更新接口下拉框选项
 * @param {string} selectedDomain - 选中的业务域
 * @param {Array} treeData - 树形数据
 * @param {HTMLElement} interfaceSelect - 接口下拉框
 * @param {HTMLElement} versionSelect - 版本下拉框
 */
function updateInterfaceOptions(selectedDomain, treeData, interfaceSelect, versionSelect) {
    console.log('Updating interface options for domain:', selectedDomain);
    
    // 清空接口和版本下拉框
    clearSelect(interfaceSelect, '请选择接口');
    clearSelect(versionSelect, '请选择版本');
    
    if (!selectedDomain) {
        return;
    }
    
    // 找到对应的业务域数据
    const domainData = treeData.find(domain => domain.businessDomain === selectedDomain);
    if (domainData && domainData.interfaces) {
        // 填充接口选项
        domainData.interfaces.forEach(interfaceNode => {
            const option = document.createElement('option');
            option.value = interfaceNode.interfaceName;
            option.textContent = interfaceNode.interfaceName;
            interfaceSelect.appendChild(option);
        });
    }
}

/**
 * 更新版本下拉框选项
 * @param {string} selectedDomain - 选中的业务域
 * @param {string} selectedInterface - 选中的接口
 * @param {Array} treeData - 树形数据
 * @param {HTMLElement} versionSelect - 版本下拉框
 */
function updateVersionOptions(selectedDomain, selectedInterface, treeData, versionSelect) {
    console.log('Updating version options for:', selectedDomain, selectedInterface);
    
    // 清空版本下拉框
    clearSelect(versionSelect, '请选择版本');
    
    if (!selectedDomain || !selectedInterface) {
        return;
    }
    
    // 找到对应的业务域和接口数据
    const domainData = treeData.find(domain => domain.businessDomain === selectedDomain);
    if (domainData && domainData.interfaces) {
        const interfaceData = domainData.interfaces.find(iface => iface.interfaceName === selectedInterface);
        if (interfaceData && interfaceData.versions) {
            // 填充版本选项
            interfaceData.versions.forEach(versionNode => {
                const option = document.createElement('option');
                option.value = versionNode.version;
                option.textContent = versionNode.version;
                versionSelect.appendChild(option);
            });
        }
    }
}

/**
 * 清空下拉框并添加默认选项
 * @param {HTMLElement} selectElement - 下拉框元素
 * @param {string} placeholder - 占位符文本
 */
function clearSelect(selectElement, placeholder) {
    if (!selectElement) {
        console.warn('无法清理下拉框：元素不存在');
        return;
    }
    selectElement.innerHTML = '';
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = placeholder || '请选择';
    selectElement.appendChild(defaultOption);
}

// populateSelect function removed - now using MappingGen.InitData.populateSelect

// Platform Knowledge Base Functions
async function loadPlatformKnowledge(page = 0) {
    console.log('loadPlatformKnowledge called with page:', page);

    try {
        // 使用新的API接口参数格式（pageNum从1开始，而不是从0开始）
        const params = {
            pageNum: page + 1,  // 新API使用pageNum从1开始
            pageSize: 10
        };

        // Add filter parameters
        const businessDomain = document.getElementById('platformBusinessDomain')?.value;
        const interfaceName = document.getElementById('platformInterface')?.value;
        const version = document.getElementById('platformVersion')?.value;

        if (businessDomain) params.businessDomain = businessDomain;
        if (interfaceName) params.interfaceName = interfaceName;
        if (version) params.version = version;

        console.log('New API request params:', params);
        
        // 调用新的平台接口列表API
        const response = await MappingGen.API.get('/api/platform-interface/list', params);
        console.log('New API response:', response);

        if (response.success) {
            console.log('Platform interface list data received:', response.data);
            
            // 适配新的分页数据格式
            const adaptedPageResult = {
                content: response.data.records,           // records -> content
                page: response.data.pageNum - 1,         // pageNum -> page (转为从0开始)
                size: response.data.pageSize,            // pageSize -> size
                totalElements: response.data.total,      // total -> totalElements
                totalPages: response.data.totalPages,    // totalPages保持不变
                hasNext: response.data.hasNext,          // hasNext保持不变
                hasPrevious: response.data.hasPrevious   // hasPrevious保持不变
            };
            
            displayPlatformKnowledge(adaptedPageResult);
            currentPlatformPage = page;
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error loading platform knowledge:', error);
        MappingGen.Utils.showAlert('加载平台知识库失败：' + error.message, 'error');
        showPlatformEmptyState();
    }
}

function displayPlatformKnowledge(pageResult) {
    console.log('displayPlatformKnowledge called with:', pageResult);

    const tableBody = document.getElementById('platformKnowledgeTableBody');
    const emptyState = document.getElementById('platformEmptyState');
    const pagination = document.getElementById('platformPagination');

    console.log('DOM elements found:', {
        tableBody: !!tableBody,
        emptyState: !!emptyState,
        pagination: !!pagination
    });

    if (!pageResult.content || pageResult.content.length === 0) {
        console.log('No content found, showing empty state');
        showPlatformEmptyState();
        return;
    }

    console.log('Content found, displaying', pageResult.content.length, 'items');

    // Hide empty state and show table
    if (emptyState) emptyState.style.display = 'none';
    if (tableBody && tableBody.parentElement) {
        tableBody.parentElement.style.display = 'table';
        tableBody.parentElement.parentElement.style.display = 'block'; // Ensure container is visible
    }

    // Clear existing rows
    if (tableBody) {
        tableBody.innerHTML = '';

        // Add rows
        pageResult.content.forEach(item => {
            console.log('Adding row for item:', item);
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.businessDomain}</td>
                <td>${item.interfaceName}</td>
                <td>${item.interfaceVersion}</td>
                <td>${MappingGen.Utils.formatDate(item.createTime)}</td>
                <td class="action-buttons">
                    <button class="btn btn-sm btn-primary" onclick="viewPlatformDocuments(${item.id})">
                        查看所有文档
                    </button>
                    <button class="btn btn-sm btn-error" onclick="deletePlatformKnowledge(${item.id})">
                        删除
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        console.log('Table rows added, total rows:', tableBody.children.length);
    }

    // Update pagination
    updatePlatformPagination(pageResult);
}

function showPlatformEmptyState() {
    const tableBody = document.getElementById('platformKnowledgeTableBody');
    const emptyState = document.getElementById('platformEmptyState');
    const pagination = document.getElementById('platformPagination');
    
    tableBody.parentElement.style.display = 'none';
    pagination.style.display = 'none';
    emptyState.style.display = 'block';
}

function updatePlatformPagination(pageResult) {
    const pagination = document.getElementById('platformPagination');
    const paginationInfo = document.getElementById('platformPaginationInfo');
    const paginationControls = document.getElementById('platformPaginationControls');
    
    if (pageResult.totalPages <= 1) {
        pagination.style.display = 'none';
        return;
    }
    
    pagination.style.display = 'flex';
    
    // Update info
    const start = pageResult.page * pageResult.size + 1;
    const end = Math.min((pageResult.page + 1) * pageResult.size, pageResult.totalElements);
    paginationInfo.innerHTML = `显示第 ${start} 到 ${end} 条，共 ${pageResult.totalElements} 条记录`;
    
    // Update controls
    let controlsHtml = '';
    
    // Previous button
    if (pageResult.hasPrevious) {
        controlsHtml += `<button class="pagination-btn" onclick="loadPlatformKnowledge(${pageResult.page - 1})">上一页</button>`;
    } else {
        controlsHtml += `<span class="pagination-btn disabled">上一页</span>`;
    }
    
    // Page numbers
    const startPage = Math.max(0, pageResult.page - 2);
    const endPage = Math.min(pageResult.totalPages - 1, pageResult.page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        if (i === pageResult.page) {
            controlsHtml += `<span class="pagination-btn active">${i + 1}</span>`;
        } else {
            controlsHtml += `<button class="pagination-btn" onclick="loadPlatformKnowledge(${i})">${i + 1}</button>`;
        }
    }
    
    // Next button
    if (pageResult.hasNext) {
        controlsHtml += `<button class="pagination-btn" onclick="loadPlatformKnowledge(${pageResult.page + 1})">下一页</button>`;
    } else {
        controlsHtml += `<span class="pagination-btn disabled">下一页</span>`;
    }
    
    paginationControls.innerHTML = controlsHtml;
}

function searchPlatformKnowledge() {
    loadPlatformKnowledge(0);
}

async function savePlatformKnowledge() {
    const form = document.getElementById('addPlatformKnowledgeForm');
    
    if (!MappingGen.Utils.validateForm(form)) {
        MappingGen.Utils.showAlert('请填写所有必填字段', 'warning');
        return;
    }
    
    try {
        const formData = new FormData(form);
        
        // 适配新API的字段名称
        // 将表单字段名映射到新API期望的参数名
        const interfaceName = formData.get('interfaceName');
        const interfaceVersion = formData.get('version'); // 表单用version，API用interfaceVersion
        const businessDomain = formData.get('businessDomain');
        
        // 创建新的FormData，使用新API的参数名
        const newFormData = new FormData();
        newFormData.append('businessDomain', businessDomain || '');
        newFormData.append('interfaceName', interfaceName || '');
        newFormData.append('interfaceVersion', interfaceVersion || '');
        newFormData.append('operator', '系统用户'); // 设置默认操作人
        
        // 添加文件字段
        const fileFields = ['respExample', 'reqDoc', 'respDoc', 'reqBeanPrompt', 'respBeanPrompt', 'codeGenPrompt', 'codeTemplateDoc'];
        fileFields.forEach(fieldName => {
            const file = formData.get(fieldName);
            if (file && file.size > 0) {
                newFormData.append(fieldName, file);
            }
        });
        
        console.log('Sending data to new platform interface API...');
        
        // 调用新的平台接口创建API
        const response = await MappingGen.API.upload('/api/platform-interface', newFormData);
        
        if (response.success) {
            MappingGen.Utils.showAlert('平台接口保存成功', 'success');
            MappingGen.Modal.hide('addPlatformKnowledgeModal');
            MappingGen.Utils.clearForm(form);
            loadPlatformKnowledge();
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error saving platform knowledge:', error);
        MappingGen.Utils.showAlert('保存失败：' + error.message, 'error');
    }
}

async function viewPlatformDocuments(id) {
    try {
        console.log('开始加载平台文档，知识库ID:', id);
        
        // 调用新的平台接口详情API
        const response = await MappingGen.API.get(`/api/platform-interface/${id}`);

        if (response.success) {
            const data = response.data;
            console.log('成功获取平台接口详情数据:', data);

            // Populate knowledge base info - 使用新的字段名
            document.getElementById('viewPlatformBusinessDomain').textContent = data.businessDomain;
            document.getElementById('viewPlatformInterface').textContent = data.interfaceName;
            document.getElementById('viewPlatformVersion').textContent = data.interfaceVersion;

            // Clear and populate document cards with fixed 7 fields
            const cardsContainer = document.getElementById('platformDocumentCards');
            if (!cardsContainer) {
                console.error('找不到文档卡片容器元素: platformDocumentCards');
                throw new Error('文档容器元素不存在');
            }
            
            cardsContainer.innerHTML = '';
            console.log('已清空文档卡片容器');

            // 适配新API返回的数据结构，创建7个固定字段的文档卡片
            const documentData = {
                respExample: data.respExample || '',
                reqDoc: data.reqDoc || '',
                respDoc: data.respDoc || '',
                reqBeanPrompt: data.reqBeanPrompt || '',
                respBeanPrompt: data.respBeanPrompt || '',
                codeGenPrompt: data.codeGenPrompt || '',
                codeTemplateDoc: data.codeTemplateDoc || ''
            };

            // Create fixed 7 document cards based on platform interface fields
            createFixedPlatformDocumentCards(cardsContainer, documentData, id);
            
            // Verify cards were created
            const createdCards = cardsContainer.querySelectorAll('.document-card');
            console.log('已创建文档卡片数量:', createdCards.length);
            
            if (createdCards.length === 0) {
                console.warn('警告：没有创建任何文档卡片');
            } else {
                console.log('文档卡片创建成功，准备显示模态框');
            }

            MappingGen.Modal.show('viewPlatformDocumentsModal');
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error loading platform documents:', error);
        MappingGen.Utils.showAlert('加载文档失败：' + error.message, 'error');
    }
}

function deletePlatformKnowledge(id) {
    currentDeleteId = id;
    currentDeleteType = 'platform';
    MappingGen.Modal.show('confirmDeletePlatformModal');
}

async function confirmDeletePlatformKnowledge() {
    if (!currentDeleteId) return;

    try {
        console.log('Deleting platform interface with ID:', currentDeleteId);
        
        // 调用新的平台接口删除API
        const response = await MappingGen.API.delete(`/api/platform-interface/${currentDeleteId}`);

        if (response.success) {
            MappingGen.Utils.showAlert('删除成功', 'success');
            MappingGen.Modal.hide('confirmDeletePlatformModal');
            currentDeleteId = null;
            currentDeleteType = null;
            loadPlatformKnowledge();
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error deleting platform interface:', error);
        MappingGen.Utils.showAlert('删除失败：' + error.message, 'error');
    }
}

// Vendor/Supplier Knowledge Base Functions
async function loadVendorKnowledge(page = 0) {
    console.log('🏢 loadVendorKnowledge called with page:', page);

    try {
        // 使用新的供应商接口API参数格式（pageNum从1开始，而不是从0开始）
        const params = {
            pageNum: page + 1,  // 新API使用pageNum从1开始
            pageSize: 10
        };

        // Add filter parameters with new field names
        const businessDomain = document.getElementById('vendorBusinessDomain')?.value;
        const supplierName = document.getElementById('vendorName')?.value;
        const interfaceName = document.getElementById('vendorInterface')?.value;
        const version = document.getElementById('vendorVersion')?.value;

        if (businessDomain) params.businessDomain = businessDomain;
        if (supplierName) params.supplierName = supplierName;  // vendor -> supplierName
        if (interfaceName) params.interfaceName = interfaceName;
        if (version) params.version = version;

        console.log('🏢 New supplier API request params:', params);
        console.log('🔍 Filter values:', {
            businessDomain,
            supplierName,
            interfaceName,
            version
        });
        
        // 调用新的供应商接口列表API
        console.log('🚀 调用供应商接口列表API: /api/supplier-interface/list');
        const response = await MappingGen.API.get('/api/supplier-interface/list', params);
        console.log('✅ New supplier API response:', response);

        if (response.success) {
            console.log('📊 Supplier interface list data received:', response.data);
            console.log('🔍 Response data structure:', {
                records: response.data.records?.length || 0,
                total: response.data.total,
                pageNum: response.data.pageNum,
                pageSize: response.data.pageSize,
                totalPages: response.data.totalPages
            });
            
            // 适配新的分页数据格式
            const adaptedPageResult = {
                content: response.data.records,           // records -> content
                page: response.data.pageNum - 1,         // pageNum -> page (转为从0开始)
                size: response.data.pageSize,            // pageSize -> size
                totalElements: response.data.total,      // total -> totalElements
                totalPages: response.data.totalPages,    // totalPages保持不变
                hasNext: response.data.hasNext,          // hasNext保持不变
                hasPrevious: response.data.hasPrevious   // hasPrevious保持不变
            };
            
            console.log('🔄 Adapted page result:', adaptedPageResult);
            displayVendorKnowledge(adaptedPageResult);
            currentVendorPage = page;
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('❌ Error loading vendor knowledge:', error);
        MappingGen.Utils.showAlert('加载供应商知识库失败：' + error.message, 'error');
        showVendorEmptyState();
    }
}

function displayVendorKnowledge(pageResult) {
    console.log('displayVendorKnowledge called with:', pageResult);

    const tableBody = document.getElementById('vendorKnowledgeTableBody');
    const emptyState = document.getElementById('vendorEmptyState');
    const pagination = document.getElementById('vendorPagination');

    console.log('DOM elements found:', {
        tableBody: !!tableBody,
        emptyState: !!emptyState,
        pagination: !!pagination
    });

    if (!pageResult.content || pageResult.content.length === 0) {
        console.log('No content found, showing empty state');
        showVendorEmptyState();
        return;
    }

    console.log('Content found, displaying', pageResult.content.length, 'items');

    // Hide empty state and show table
    if (emptyState) emptyState.style.display = 'none';
    if (tableBody && tableBody.parentElement) {
        tableBody.parentElement.style.display = 'table';
        tableBody.parentElement.parentElement.style.display = 'block'; // Ensure container is visible
    }

    // Clear existing rows
    if (tableBody) {
        tableBody.innerHTML = '';

        // Add rows
        pageResult.content.forEach(item => {
            console.log('Adding row for item:', item);
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.businessDomain}</td>
                <td>${item.supplierName}</td>
                <td>${item.interfaceName}</td>
                <td>${item.version}</td>
                <td>${MappingGen.Utils.formatDate(item.createTime)}</td>
                <td class="action-buttons">
                    <button class="btn btn-sm btn-primary" onclick="viewVendorDocuments(${item.id})">
                        查看所有文档
                    </button>
                    <button class="btn btn-sm btn-error" onclick="deleteVendorKnowledge(${item.id})">
                        删除
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        console.log('Table rows added, total rows:', tableBody.children.length);
    }

    // Update pagination
    updateVendorPagination(pageResult);
}

function showVendorEmptyState() {
    const tableBody = document.getElementById('vendorKnowledgeTableBody');
    const emptyState = document.getElementById('vendorEmptyState');
    const pagination = document.getElementById('vendorPagination');

    tableBody.parentElement.style.display = 'none';
    pagination.style.display = 'none';
    emptyState.style.display = 'block';
}

function updateVendorPagination(pageResult) {
    const pagination = document.getElementById('vendorPagination');
    const paginationInfo = document.getElementById('vendorPaginationInfo');
    const paginationControls = document.getElementById('vendorPaginationControls');

    if (pageResult.totalPages <= 1) {
        pagination.style.display = 'none';
        return;
    }

    pagination.style.display = 'flex';

    // Update info
    const start = pageResult.page * pageResult.size + 1;
    const end = Math.min((pageResult.page + 1) * pageResult.size, pageResult.totalElements);
    paginationInfo.innerHTML = `显示第 ${start} 到 ${end} 条，共 ${pageResult.totalElements} 条记录`;

    // Update controls
    let controlsHtml = '';

    // Previous button
    if (pageResult.hasPrevious) {
        controlsHtml += `<button class="pagination-btn" onclick="loadVendorKnowledge(${pageResult.page - 1})">上一页</button>`;
    } else {
        controlsHtml += `<span class="pagination-btn disabled">上一页</span>`;
    }

    // Page numbers
    const startPage = Math.max(0, pageResult.page - 2);
    const endPage = Math.min(pageResult.totalPages - 1, pageResult.page + 2);

    for (let i = startPage; i <= endPage; i++) {
        if (i === pageResult.page) {
            controlsHtml += `<span class="pagination-btn active">${i + 1}</span>`;
        } else {
            controlsHtml += `<button class="pagination-btn" onclick="loadVendorKnowledge(${i})">${i + 1}</button>`;
        }
    }

    // Next button
    if (pageResult.hasNext) {
        controlsHtml += `<button class="pagination-btn" onclick="loadVendorKnowledge(${pageResult.page + 1})">下一页</button>`;
    } else {
        controlsHtml += `<span class="pagination-btn disabled">下一页</span>`;
    }

    paginationControls.innerHTML = controlsHtml;
}

function searchVendorKnowledge() {
    loadVendorKnowledge(0);
}

async function saveVendorKnowledge() {
    const form = document.getElementById('addVendorKnowledgeForm');

    if (!MappingGen.Utils.validateForm(form)) {
        MappingGen.Utils.showAlert('请填写所有必填字段', 'warning');
        return;
    }

    try {
        // 手动构造FormData以匹配新API的字段名
        const formData = new FormData();
        
        // 获取表单字段并映射到新API的字段名
        const businessDomain = form.querySelector('[name="businessDomain"]')?.value;
        const supplierName = form.querySelector('[name="vendor"]')?.value || form.querySelector('[name="supplierName"]')?.value;
        const interfaceName = form.querySelector('[name="interfaceName"]')?.value;
        const version = form.querySelector('[name="version"]')?.value;
        const respFields = form.querySelector('[name="respFields"]')?.value;
        const operator = form.querySelector('[name="operator"]')?.value || '系统用户';
        
        // 添加基本字段
        if (businessDomain) formData.append('businessDomain', businessDomain);
        if (supplierName) formData.append('supplierName', supplierName);  // vendor -> supplierName
        if (interfaceName) formData.append('interfaceName', interfaceName);
        if (version) formData.append('version', version);
        if (respFields) formData.append('respFields', respFields);
        formData.append('operator', operator);
        
        // 添加文件字段（如果存在）
        const fileFields = ['reqExample', 'respExample', 'reqDoc', 'respDoc'];
        fileFields.forEach(fieldName => {
            const fileInput = form.querySelector(`[name="${fieldName}"]`);
            if (fileInput && fileInput.files[0]) {
                formData.append(fieldName, fileInput.files[0]);
            }
        });

        console.log('Saving supplier interface with new API...');
        const response = await MappingGen.API.upload('/api/supplier-interface', formData);

        if (response.success) {
            MappingGen.Utils.showAlert('供应商知识库保存成功', 'success');
            MappingGen.Modal.hide('addVendorKnowledgeModal');
            MappingGen.Utils.clearForm(form);
            loadVendorKnowledge();
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error saving vendor knowledge:', error);
        MappingGen.Utils.showAlert('保存失败：' + error.message, 'error');
    }
}

// Handle reupload for platform document fields
function reuploadPlatformDocument(fieldName, knowledgeBaseId) {
    // Create a hidden file input
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.txt,.md,.json,.java,.js';
    fileInput.style.display = 'none';

    fileInput.onchange = async function(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            // Show loading state
            MappingGen.Utils.showAlert('正在上传文件...', 'info');

            // Read file content
            const fileContent = await readFileAsText(file);

            // Prepare update request data for new API
            const updateData = {};
            updateData[fieldName] = fileContent;
            updateData.operator = '系统用户'; // 设置操作人

            console.log('Updating platform interface field:', fieldName, 'for ID:', knowledgeBaseId);

            // Call new platform interface update API
            const response = await MappingGen.API.put(`/api/platform-interface/${knowledgeBaseId}`, updateData);

            if (response.success) {
                MappingGen.Utils.showAlert('文件上传成功', 'success');

                // Update the document card content
                const card = document.querySelector(`[data-field-name="${fieldName}"]`);
                if (card) {
                    const contentDiv = card.querySelector('.document-content');
                    if (contentDiv) {
                        // 对文件内容进行HTML转义显示
                        contentDiv.innerHTML = escapeHtml(fileContent);
                    }
                }
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            console.error('Error uploading file:', error);
            MappingGen.Utils.showAlert('文件上传失败：' + error.message, 'error');
        }

        // Clean up
        document.body.removeChild(fileInput);
    };

    // Add to DOM and trigger click
    document.body.appendChild(fileInput);
    fileInput.click();
}

/**
 * 读取文件内容为文本
 * @param {File} file - 文件对象
 * @returns {Promise<string>} 文件内容
 */
function readFileAsText(file) {
    return new Promise((resolve, reject) => {
        try {
            // 文件安全验证
            KnowledgeBase.utils.FileValidator.validateFile(file);
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const content = e.target.result;
                    
                    // 内容安全检查
                    if (typeof content !== 'string') {
                        throw new Error('文件内容格式无效');
                    }
                    
                    // 内容长度检查（防止过大的文件导致内存问题）
                    if (content.length > 10 * 1024 * 1024) { // 10MB 文本内容限制
                        throw new Error('文件内容过大，请选择较小的文件');
                    }
                    
                    resolve(content);
                } catch (error) {
                    reject(error);
                }
            };
            
            reader.onerror = function(e) {
                reject(new Error('文件读取失败：' + (e.target.error?.message || '未知错误')));
            };
            
            reader.readAsText(file, 'UTF-8');
        } catch (error) {
            reject(error);
        }
    });
}

/**
 * HTML转义函数
 * @param {string} text - 需要转义的文本
 * @returns {string} 转义后的文本
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

async function viewVendorDocuments(id) {
    try {
        // 调用新的供应商接口详情API
        const response = await MappingGen.API.get(`/api/supplier-interface/${id}`);

        if (response.success) {
            const data = response.data;
            console.log('Supplier interface details loaded:', data);

            // Populate knowledge base info (字段名适配)
            document.getElementById('viewVendorBusinessDomain').textContent = data.businessDomain;
            document.getElementById('viewVendorName').textContent = data.supplierName;  // vendor -> supplierName
            document.getElementById('viewVendorInterface').textContent = data.interfaceName;
            document.getElementById('viewVendorVersion').textContent = data.version;

            // 创建供应商文档卡片
            const cardsContainer = document.getElementById('vendorDocumentCards');
            cardsContainer.innerHTML = '';

            // 构造文档数据对象，用于供应商接口的固定4个字段
            const documentData = {
                reqExample: data.reqExample || '',
                respExample: data.respExample || '',
                reqDoc: data.reqDoc || '',
                respDoc: data.respDoc || ''
            };

            console.log('Creating supplier document cards with data:', documentData);
            createFixedSupplierDocumentCards(cardsContainer, documentData, id);

            MappingGen.Modal.show('viewVendorDocumentsModal');
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error loading vendor documents:', error);
        MappingGen.Utils.showAlert('加载文档失败：' + error.message, 'error');
    }
}

function deleteVendorKnowledge(id) {
    currentDeleteId = id;
    currentDeleteType = 'vendor';
    MappingGen.Modal.show('confirmDeleteVendorModal');
}

async function confirmDeleteVendorKnowledge() {
    if (!currentDeleteId) return;

    try {
        // 调用新的供应商接口删除API
        const response = await MappingGen.API.delete(`/api/supplier-interface/${currentDeleteId}`);

        if (response.success) {
            MappingGen.Utils.showAlert('删除成功', 'success');
            MappingGen.Modal.hide('confirmDeleteVendorModal');
            loadVendorKnowledge();
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error deleting vendor knowledge:', error);
        MappingGen.Utils.showAlert('删除失败：' + error.message, 'error');
    } finally {
        currentDeleteId = null;
        currentDeleteType = null;
    }
}

// Create fixed 4 document cards for supplier interface
function createFixedSupplierDocumentCards(container, data, supplierId) {
    // Define the 4 fixed fields for supplier interface
    const fixedFields = [
        { field: 'reqExample', name: '供应入参示例文件' },
        { field: 'respExample', name: '供应返参示例文件' },
        { field: 'reqDoc', name: '供应入参文档文件' },
        { field: 'respDoc', name: '供应返参文档文件' }
    ];

    console.log('开始创建供应商固定字段文档卡片，字段数量:', fixedFields.length);
    console.log('可用数据字段:', Object.keys(data));

    fixedFields.forEach((fieldInfo, index) => {
        console.log(`创建第${index + 1}个供应商卡片:`, fieldInfo.field, '内容长度:', (data[fieldInfo.field] || '').length);
        const card = createFixedSupplierDocumentCard(fieldInfo, data, supplierId, index);
        if (card) {
            container.appendChild(card);
            console.log(`第${index + 1}个供应商卡片创建成功:`, fieldInfo.name);
        } else {
            console.error(`第${index + 1}个供应商卡片创建失败:`, fieldInfo.name);
        }
    });

    console.log('供应商固定字段文档卡片创建完成');
}

function createFixedSupplierDocumentCard(fieldInfo, data, supplierId, index) {
    console.log('创建供应商文档卡片开始:', fieldInfo.field);
    try {
        const card = document.createElement('div');
        card.className = 'document-card';
        card.setAttribute('data-field-name', fieldInfo.field);
        card.setAttribute('data-supplier-id', supplierId);

        let content = data[fieldInfo.field] || '';
        console.log('字段内容:', fieldInfo.field, '长度:', content.length, '前50个字符:', content.substring(0, 50));

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        const displayContent = content ? escapeHtml(content) : '暂无内容';

        const cardHeader = document.createElement('div');
        cardHeader.className = 'document-card-header';
        const documentType = document.createElement('span');
        documentType.className = 'document-type';
        documentType.textContent = fieldInfo.name;
        cardHeader.appendChild(documentType);

        const cardBody = document.createElement('div');
        cardBody.className = 'document-card-body';
        const documentContent = document.createElement('div');
        documentContent.className = 'document-content';
        documentContent.innerHTML = displayContent;
        cardBody.appendChild(documentContent);

        const cardActions = document.createElement('div');
        cardActions.className = 'document-actions';
        const reuploadButton = document.createElement('button');
        reuploadButton.className = 'btn btn-sm btn-primary';
        reuploadButton.textContent = '重新上传';
        reuploadButton.addEventListener('click', function() {
            reuploadSupplierDocument(fieldInfo.field, supplierId);
        });
        cardActions.appendChild(reuploadButton);

        card.appendChild(cardHeader);
        card.appendChild(cardBody);
        card.appendChild(cardActions);

        console.log('供应商文档卡片创建成功:', fieldInfo.field);
        return card;
    } catch (error) {
        console.error('创建供应商文档卡片时发生错误:', fieldInfo.field, error);
        return null;
    }
}

// Handle reupload for supplier document fields
function reuploadSupplierDocument(fieldName, supplierId) {
    // Create a hidden file input
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.txt,.md,.json,.java,.js';
    fileInput.style.display = 'none';

    fileInput.onchange = async function(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            // Show loading state
            MappingGen.Utils.showAlert('正在上传文件...', 'info');

            // Read file content
            const fileContent = await readFileAsText(file);

            // Prepare update request data for new supplier API
            const updateData = {};
            updateData[fieldName] = fileContent;
            updateData.operator = '系统用户'; // 设置操作人

            console.log('Updating supplier interface field:', fieldName, 'for ID:', supplierId);

            // Call new supplier interface update API
            const response = await MappingGen.API.put(`/api/supplier-interface/${supplierId}`, updateData);

            if (response.success) {
                MappingGen.Utils.showAlert('文件上传成功', 'success');

                // Update the document card content
                const card = document.querySelector(`[data-field-name="${fieldName}"]`);
                if (card) {
                    const contentDiv = card.querySelector('.document-content');
                    if (contentDiv) {
                        // 对文件内容进行HTML转义显示
                        contentDiv.innerHTML = escapeHtml(fileContent);
                    }
                }
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            console.error('Error uploading file:', error);
            MappingGen.Utils.showAlert('文件上传失败：' + error.message, 'error');
        }

        // Clean up
        document.body.removeChild(fileInput);
    };

    // Add to DOM and trigger click
    document.body.appendChild(fileInput);
    fileInput.click();
}

// Create fixed 7 document cards for platform interface
function createFixedPlatformDocumentCards(container, data, knowledgeBaseId) {
    // Define the 7 fixed fields with their display names (matching API documentation)
    const fixedFields = [
        { field: 'respExample', name: '平台返参示例文件' },
        { field: 'reqDoc', name: '平台入参文档文件' },
        { field: 'respDoc', name: '平台返参文档文件' },
        { field: 'reqBeanPrompt', name: '入参Bean生成Prompt文件' },
        { field: 'respBeanPrompt', name: '返参Bean生成Prompt文件' },
        { field: 'codeGenPrompt', name: '代码生成Prompt文件' },
        { field: 'codeTemplateDoc', name: '代码模板文档文件' }
    ];

    console.log('开始创建固定字段文档卡片，字段数量:', fixedFields.length);
    console.log('可用数据字段:', Object.keys(data));

    fixedFields.forEach((fieldInfo, index) => {
        console.log(`创建第${index + 1}个卡片:`, fieldInfo.field, '内容长度:', (data[fieldInfo.field] || '').length);
        const card = createFixedDocumentCard(fieldInfo, data, knowledgeBaseId, index);
        if (card) {
        container.appendChild(card);
            console.log(`第${index + 1}个卡片创建成功:`, fieldInfo.name);
        } else {
            console.error(`第${index + 1}个卡片创建失败:`, fieldInfo.name);
        }
    });
    
    console.log('所有文档卡片创建完成');
}

// Create a fixed document card for a specific field
function createFixedDocumentCard(fieldInfo, data, knowledgeBaseId, index) {
    console.log('创建文档卡片开始:', fieldInfo.field);
    
    try {
    const card = document.createElement('div');
    card.className = 'document-card';
    card.setAttribute('data-field-name', fieldInfo.field);
    card.setAttribute('data-knowledge-base-id', knowledgeBaseId);

        // Get content from data (if available) and escape it properly
        let content = data[fieldInfo.field] || '';
        console.log('字段内容:', fieldInfo.field, '长度:', content.length, '前50个字符:', content.substring(0, 50));
        
        // Escape HTML characters to prevent injection and rendering issues
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // If content is empty, show placeholder
        const displayContent = content ? escapeHtml(content) : '暂无内容';

        // Create card structure using DOM methods instead of innerHTML to avoid template string issues
        const cardHeader = document.createElement('div');
        cardHeader.className = 'document-card-header';
        
        const documentType = document.createElement('span');
        documentType.className = 'document-type';
        documentType.textContent = fieldInfo.name;
        cardHeader.appendChild(documentType);

        const cardBody = document.createElement('div');
        cardBody.className = 'document-card-body';
        
        const documentContent = document.createElement('div');
        documentContent.className = 'document-content';
        documentContent.innerHTML = displayContent;
        cardBody.appendChild(documentContent);

        const cardActions = document.createElement('div');
        cardActions.className = 'document-actions';
        
        const reuploadButton = document.createElement('button');
        reuploadButton.className = 'btn btn-sm btn-primary';
        reuploadButton.textContent = '重新上传';
        reuploadButton.addEventListener('click', function() {
            reuploadPlatformDocument(fieldInfo.field, knowledgeBaseId);
        });
        cardActions.appendChild(reuploadButton);

        // Assemble the card
        card.appendChild(cardHeader);
        card.appendChild(cardBody);
        card.appendChild(cardActions);

        console.log('文档卡片创建成功:', fieldInfo.field);
    return card;
    } catch (error) {
        console.error('创建文档卡片时发生错误:', fieldInfo.field, error);
        return null;
    }
}

// Common utility functions
function createDocumentCard(docData, editable = false) {
    // Validate input parameters
    if (!docData || typeof docData !== 'object') {
        console.error('createDocumentCard: Invalid document data provided', docData);
        return null;
    }

    // Ensure we have access to the global document object
    if (typeof document === 'undefined' || typeof document.createElement !== 'function') {
        console.error('createDocumentCard: document.createElement is not available');
        return null;
    }

    try {
        const card = document.createElement('div');
        card.className = 'document-card';
        card.setAttribute('data-document-id', docData.id || '');

        card.innerHTML = `
            <div class="document-card-header">
                <span class="document-type">${docData.fileType || '未知类型'}</span>
                ${editable ? `
                    <button class="btn btn-sm btn-ghost" onclick="editDocument(this)">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                    </button>
                ` : ''}
            </div>
            <div class="document-card-body">
                <div class="document-content">${docData.content || '暂无内容'}</div>
            </div>
            ${editable ? `
                <div class="document-actions">
                    <button class="btn btn-sm btn-primary" onclick="reuploadDocument(this)" data-id="${docData.id || ''}">
                        重新上传
                    </button>
                </div>
            ` : ''}
        `;

        return card;
    } catch (error) {
        console.error('createDocumentCard: Error creating document card', error);
        return null;
    }
}

function editDocument(button) {
    const card = button.closest('.document-card');
    const contentDiv = card.querySelector('.document-content');
    const originalContent = contentDiv.textContent;

    // Create textarea for editing
    const textarea = document.createElement('textarea');
    textarea.className = 'form-control textarea';
    textarea.style.minHeight = '200px';
    textarea.value = originalContent;

    // Replace content with textarea
    contentDiv.innerHTML = '';
    contentDiv.appendChild(textarea);

    // Change button to save/cancel
    button.innerHTML = `
        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3-3-3m3-3v12"></path>
        </svg>
    `;
    button.onclick = () => saveDocumentEdit(button, originalContent);

    // Add cancel button
    const cancelButton = document.createElement('button');
    cancelButton.className = 'btn btn-sm btn-ghost';
    cancelButton.innerHTML = `
        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
    `;
    cancelButton.onclick = () => cancelDocumentEdit(button, originalContent);

    button.parentElement.appendChild(cancelButton);
}

async function saveDocumentEdit(button, originalContent) {
    const card = button.closest('.document-card');
    const textarea = card.querySelector('textarea');
    const newContent = textarea.value;
    const documentId = card.getAttribute('data-document-id');
    const knowledgeBaseId = card.getAttribute('data-knowledge-base-id');

    if (newContent === originalContent) {
        MappingGen.Utils.showAlert('内容未更改', 'info');
        cancelDocumentEdit(button, originalContent);
        return;
    }

    try {
        // 检查当前页面类型决定使用哪个API
        const urlParams = new URLSearchParams(window.location.search);
        const activeTab = urlParams.get('type') || 'platform';
        
        let response;
        if (activeTab === 'platform') {
            // 使用新的平台接口更新API
            const fieldName = card.getAttribute('data-field-name');
            const updateData = {};
            updateData[fieldName] = newContent;
            updateData.operator = '系统用户';
            
            console.log('Updating platform interface field via saveDocumentEdit:', fieldName, 'for ID:', knowledgeBaseId);
            response = await MappingGen.API.put(`/api/platform-interface/${knowledgeBaseId}`, updateData);
        } else {
            // 使用新的供应商接口更新API
            const fieldName = card.getAttribute('data-field-name');
            const supplierId = card.getAttribute('data-supplier-id');
            const updateData = {};
            updateData[fieldName] = newContent;
            updateData.operator = '系统用户';
            
            console.log('Updating supplier interface field via saveDocumentEdit:', fieldName, 'for ID:', supplierId);
            response = await MappingGen.API.put(`/api/supplier-interface/${supplierId}`, updateData);
        }

        if (response.success) {
            MappingGen.Utils.showAlert('文档更新成功', 'success');
            // 更新显示内容而不是重新加载整个页面
            const contentDiv = card.querySelector('.document-content');
            if (contentDiv) {
                contentDiv.innerHTML = escapeHtml(newContent);
            }
            cancelDocumentEdit(button, newContent);
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('Error updating document:', error);
        MappingGen.Utils.showAlert('更新失败：' + error.message, 'error');
        cancelDocumentEdit(button, originalContent);
    }
}

function cancelDocumentEdit(button, originalContent) {
    const card = button.closest('.document-card');
    const contentDiv = card.querySelector('.document-content');

    // Restore original content
    contentDiv.textContent = originalContent;

    // Restore edit button
    button.innerHTML = `
        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
    `;
    button.onclick = () => editDocument(button);

    // Remove cancel button
    const cancelButton = button.parentElement.querySelector('button:last-child');
    if (cancelButton && cancelButton !== button) {
        cancelButton.remove();
    }
}

function reuploadDocument(button) {
    const documentId = button.getAttribute('data-id');
    const knowledgeBaseId = button.getAttribute('data-knowledge-base-id');
    const fieldName = button.getAttribute('data-field-name');

    // Create file input
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.txt,.md,.json,.xml,.pdf';

    fileInput.onchange = async function() {
        if (this.files.length > 0) {
            const file = this.files[0];

            try {
                // 获取卡片元素（统一在开始获取）
                const card = button.closest('.document-card');
                if (!card) {
                    throw new Error('无法找到文档卡片容器');
                }
                
                // 检查当前页面类型决定使用哪个API
                const urlParams = new URLSearchParams(window.location.search);
                const activeTab = urlParams.get('type') || 'platform';
                
                // 显示上传提示
                MappingGen.Utils.showAlert('正在上传文件...', 'info');
                
                // 读取文件内容
                const fileContent = await readFileAsText(file);
                
                let response;
                const updateData = {
                    [fieldName]: fileContent,
                    operator: '系统用户'
                };
                
                if (activeTab === 'platform') {
                    // 使用新的平台接口更新API
                    console.log('Reuploading platform interface field:', fieldName, 'for ID:', knowledgeBaseId);
                    response = await MappingGen.API.put(`/api/platform-interface/${knowledgeBaseId}`, updateData);
                } else {
                    // 使用新的供应商接口更新API
                    const supplierId = card.getAttribute('data-supplier-id');
                    if (!supplierId) {
                        throw new Error('无法获取供应商ID');
                    }
                    console.log('Reuploading supplier interface field:', fieldName, 'for ID:', supplierId);
                    response = await MappingGen.API.put(`/api/supplier-interface/${supplierId}`, updateData);
                }
                
                if (response.success) {
                    // 更新卡片显示内容
                    const contentDiv = card.querySelector('.document-content');
                    if (contentDiv) {
                        contentDiv.innerHTML = escapeHtml(fileContent);
                    } else {
                        console.warn('无法找到文档内容容器');
                    }
                }

                if (response.success) {
                    MappingGen.Utils.showAlert('文档重新上传成功', 'success');
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                console.error('Error reuploading document:', error);
                MappingGen.Utils.showAlert('重新上传失败：' + error.message, 'error');
            }
        }
    };

    // Trigger file selection
    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
}
