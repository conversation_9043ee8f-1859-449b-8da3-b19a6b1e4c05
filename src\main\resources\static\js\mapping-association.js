/**
 * Mapping Association JavaScript functionality
 * 企业级建立关联页面前端实现
 */

// Global variables
let currentPage = 0;
let currentDeleteId = null;
let pendingMappingData = null;

// 存储树形数据，用于级联下拉框
let platformTreeData = null;
let supplierTreeData = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeMappingAssociation();
});

async function initializeMappingAssociation() {
    console.log('🚀 Initializing Mapping Association...');
    
    // 并行加载平台和供应商树形数据
    await loadTreeData();
    
    // 初始化级联下拉框
    initializeCascadingDropdowns();
    
    // 加载映射关联数据
    loadAssociations();
}

/**
 * 加载平台和供应商的树形数据
 */
async function loadTreeData() {
    try {
        console.log('🌳 Loading tree data from new APIs...');
        
        // 并行请求平台和供应商树形数据
        const [platformResponse, supplierResponse] = await Promise.all([
            MappingGen.API.get('/api/platform-interface/tree'),
            MappingGen.API.get('/api/supplier-interface/tree')
        ]);
        
        if (platformResponse.success && supplierResponse.success) {
            platformTreeData = platformResponse.data;
            supplierTreeData = supplierResponse.data;
            
            console.log('✅ Tree data loaded successfully:', {
                platformData: platformTreeData,
                supplierData: supplierTreeData
            });
        } else {
            throw new Error('Failed to load tree data: ' + 
                (platformResponse.message || supplierResponse.message));
        }
    } catch (error) {
        console.error('❌ Error loading tree data:', error);
        MappingGen.Utils.showAlert('加载下拉数据失败：' + error.message, 'error');
        
        // 初始化空数据，防止页面报错
        platformTreeData = [];
        supplierTreeData = [];
    }
}

/**
 * 初始化级联下拉框
 */
function initializeCascadingDropdowns() {
    console.log('🔗 Initializing cascading dropdowns...');
    
    // 初始化查询表单的级联下拉框
    initializeSearchFormCascading();
    
    // 初始化添加表单的级联下拉框
    initializeAddFormCascading();
}

/**
 * 初始化查询表单的级联下拉框
 */
function initializeSearchFormCascading() {
    // 业务域下拉框
    const businessDomainSelect = document.getElementById('businessDomain');
    const platformInterfaceSelect = document.getElementById('platformInterface');
    const platformVersionSelect = document.getElementById('platformVersion');
    const vendorSelect = document.getElementById('vendor');
    const vendorInterfaceSelect = document.getElementById('vendorInterface');
    const vendorVersionSelect = document.getElementById('vendorVersion');
    
    if (!businessDomainSelect || !platformInterfaceSelect || !platformVersionSelect ||
        !vendorSelect || !vendorInterfaceSelect || !vendorVersionSelect) {
        console.warn('⚠️ Some form elements not found for search cascading');
        return;
    }
    
    // 填充业务域选项（取平台和供应商业务域的并集）
    populateBusinessDomains(businessDomainSelect);
    
    // 业务域变更事件
    businessDomainSelect.addEventListener('change', function() {
        const selectedDomain = this.value;
        console.log('🔄 Business domain changed in search form:', selectedDomain);
        
        updatePlatformInterfaces(selectedDomain, platformInterfaceSelect, platformVersionSelect);
        updateSuppliers(selectedDomain, vendorSelect, vendorInterfaceSelect, vendorVersionSelect);
    });
    
    // 平台接口变更事件
    platformInterfaceSelect.addEventListener('change', function() {
        const selectedDomain = businessDomainSelect.value;
        const selectedInterface = this.value;
        console.log('🔄 Platform interface changed in search form:', selectedInterface);
        
        updatePlatformVersions(selectedDomain, selectedInterface, platformVersionSelect);
    });
    
    // 供应商变更事件
    vendorSelect.addEventListener('change', function() {
        const selectedDomain = businessDomainSelect.value;
        const selectedVendor = this.value;
        console.log('🔄 Vendor changed in search form:', selectedVendor);
        
        updateVendorInterfaces(selectedDomain, selectedVendor, vendorInterfaceSelect, vendorVersionSelect);
    });
    
    // 供应商接口变更事件
    vendorInterfaceSelect.addEventListener('change', function() {
        const selectedDomain = businessDomainSelect.value;
        const selectedVendor = vendorSelect.value;
        const selectedInterface = this.value;
        console.log('🔄 Vendor interface changed in search form:', selectedInterface);
        
        updateVendorVersions(selectedDomain, selectedVendor, selectedInterface, vendorVersionSelect);
    });
}

/**
 * 初始化添加表单的级联下拉框
 */
function initializeAddFormCascading() {
    // 添加表单的下拉框元素
    const addBusinessDomainSelect = document.getElementById('addBusinessDomain');
    const addPlatformInterfaceSelect = document.getElementById('addPlatformInterface');
    const addPlatformVersionSelect = document.getElementById('addPlatformVersion');
    const addVendorSelect = document.getElementById('addVendor');
    const addVendorInterfaceSelect = document.getElementById('addVendorInterface');
    const addVendorVersionSelect = document.getElementById('addVendorVersion');
    
    if (!addBusinessDomainSelect || !addPlatformInterfaceSelect || !addPlatformVersionSelect ||
        !addVendorSelect || !addVendorInterfaceSelect || !addVendorVersionSelect) {
        console.warn('⚠️ Some form elements not found for add cascading');
        return;
    }
    
    // 填充业务域选项
    populateBusinessDomains(addBusinessDomainSelect);
    
    // 业务域变更事件
    addBusinessDomainSelect.addEventListener('change', function() {
        const selectedDomain = this.value;
        console.log('🔄 Business domain changed in add form:', selectedDomain);
        
        updatePlatformInterfaces(selectedDomain, addPlatformInterfaceSelect, addPlatformVersionSelect);
        updateSuppliers(selectedDomain, addVendorSelect, addVendorInterfaceSelect, addVendorVersionSelect);
    });
    
    // 平台接口变更事件
    addPlatformInterfaceSelect.addEventListener('change', function() {
        const selectedDomain = addBusinessDomainSelect.value;
        const selectedInterface = this.value;
        console.log('🔄 Platform interface changed in add form:', selectedInterface);
        
        updatePlatformVersions(selectedDomain, selectedInterface, addPlatformVersionSelect);
    });
    
    // 供应商变更事件
    addVendorSelect.addEventListener('change', function() {
        const selectedDomain = addBusinessDomainSelect.value;
        const selectedVendor = this.value;
        console.log('🔄 Vendor changed in add form:', selectedVendor);
        
        updateVendorInterfaces(selectedDomain, selectedVendor, addVendorInterfaceSelect, addVendorVersionSelect);
    });
    
    // 供应商接口变更事件
    addVendorInterfaceSelect.addEventListener('change', function() {
        const selectedDomain = addBusinessDomainSelect.value;
        const selectedVendor = addVendorSelect.value;
        const selectedInterface = this.value;
        console.log('🔄 Vendor interface changed in add form:', selectedInterface);
        
        updateVendorVersions(selectedDomain, selectedVendor, selectedInterface, addVendorVersionSelect);
    });
}

/**
 * 填充业务域下拉框（平台和供应商业务域的并集）
 */
function populateBusinessDomains(selectElement) {
    if (!selectElement) return;
    
    const businessDomains = new Set();
    
    // 收集平台业务域
    if (platformTreeData && Array.isArray(platformTreeData)) {
        platformTreeData.forEach(domain => {
            if (domain.businessDomain) {
                businessDomains.add(domain.businessDomain);
            }
        });
    }
    
    // 收集供应商业务域
    if (supplierTreeData && Array.isArray(supplierTreeData)) {
        supplierTreeData.forEach(domain => {
            if (domain.businessDomain) {
                businessDomains.add(domain.businessDomain);
            }
        });
    }
    
    // 清空并填充选项
    clearSelect(selectElement, '请选择业务领域');
    Array.from(businessDomains).sort().forEach(domain => {
        const option = document.createElement('option');
        option.value = domain;
        option.textContent = domain;
        selectElement.appendChild(option);
    });
    
    console.log('📋 Business domains populated:', Array.from(businessDomains));
}

/**
 * 更新平台接口下拉框
 */
function updatePlatformInterfaces(businessDomain, interfaceSelect, versionSelect) {
    clearSelect(interfaceSelect, '请选择平台端接口');
    clearSelect(versionSelect, '请选择平台端版本');
    
    if (!businessDomain || !platformTreeData) return;
    
    const domainData = platformTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.interfaces) {
        domainData.interfaces.forEach(interfaceNode => {
            const option = document.createElement('option');
            option.value = interfaceNode.interfaceName;
            option.textContent = interfaceNode.interfaceName;
            interfaceSelect.appendChild(option);
        });
    }
}

/**
 * 更新平台版本下拉框
 */
function updatePlatformVersions(businessDomain, interfaceName, versionSelect) {
    clearSelect(versionSelect, '请选择平台端版本');
    
    if (!businessDomain || !interfaceName || !platformTreeData) return;
    
    const domainData = platformTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.interfaces) {
        const interfaceData = domainData.interfaces.find(i => i.interfaceName === interfaceName);
        if (interfaceData && interfaceData.versions) {
            interfaceData.versions.forEach(versionNode => {
                const option = document.createElement('option');
                option.value = versionNode.version;
                option.textContent = versionNode.version;
                versionSelect.appendChild(option);
            });
        }
    }
}

/**
 * 更新供应商下拉框
 */
function updateSuppliers(businessDomain, supplierSelect, interfaceSelect, versionSelect) {
    clearSelect(supplierSelect, '请选择供应商');
    clearSelect(interfaceSelect, '请选择供应商端接口');
    clearSelect(versionSelect, '请选择供应商端版本');
    
    if (!businessDomain || !supplierTreeData) return;
    
    const domainData = supplierTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.suppliers) {
        domainData.suppliers.forEach(supplierNode => {
            const option = document.createElement('option');
            option.value = supplierNode.supplierName;
            option.textContent = supplierNode.supplierName;
            supplierSelect.appendChild(option);
        });
    }
}

/**
 * 更新供应商接口下拉框
 */
function updateVendorInterfaces(businessDomain, supplierName, interfaceSelect, versionSelect) {
    clearSelect(interfaceSelect, '请选择供应商端接口');
    clearSelect(versionSelect, '请选择供应商端版本');
    
    if (!businessDomain || !supplierName || !supplierTreeData) return;
    
    const domainData = supplierTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.suppliers) {
        const supplierData = domainData.suppliers.find(s => s.supplierName === supplierName);
        if (supplierData && supplierData.interfaces) {
            supplierData.interfaces.forEach(interfaceNode => {
                const option = document.createElement('option');
                option.value = interfaceNode.interfaceName;
                option.textContent = interfaceNode.interfaceName;
                interfaceSelect.appendChild(option);
            });
        }
    }
}

/**
 * 更新供应商版本下拉框
 */
function updateVendorVersions(businessDomain, supplierName, interfaceName, versionSelect) {
    clearSelect(versionSelect, '请选择供应商端版本');
    
    if (!businessDomain || !supplierName || !interfaceName || !supplierTreeData) return;
    
    const domainData = supplierTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.suppliers) {
        const supplierData = domainData.suppliers.find(s => s.supplierName === supplierName);
        if (supplierData && supplierData.interfaces) {
            const interfaceData = supplierData.interfaces.find(i => i.interfaceName === interfaceName);
            if (interfaceData && interfaceData.versions) {
                interfaceData.versions.forEach(versionNode => {
                    const option = document.createElement('option');
                    option.value = versionNode.version;
                    option.textContent = versionNode.version;
                    versionSelect.appendChild(option);
                });
            }
        }
    }
}

/**
 * 清空下拉框并添加占位符选项
 */
function clearSelect(selectElement, placeholder) {
    if (!selectElement) return;
    
    selectElement.innerHTML = '';
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = placeholder;
    selectElement.appendChild(defaultOption);
}

/**
 * 加载映射关联数据 - 使用新的API
 */
async function loadAssociations(page = 0) {
    try {
        console.log('📊 Loading associations with page:', page);
        
        // 使用新API的参数格式（pageNum从1开始）
        const params = {
            pageNum: page + 1,  // 新API使用pageNum从1开始
            pageSize: 10
        };
        
        // 添加筛选参数
        const businessDomain = document.getElementById('businessDomain')?.value;
        const platformInterface = document.getElementById('platformInterface')?.value;
        const platformVersion = document.getElementById('platformVersion')?.value;
        const vendor = document.getElementById('vendor')?.value;
        const vendorInterface = document.getElementById('vendorInterface')?.value;
        const vendorVersion = document.getElementById('vendorVersion')?.value;
        
        if (businessDomain) params.businessDomain = businessDomain;
        if (platformInterface) params.platformInterfaceName = platformInterface; // 字段名适配
        if (platformVersion) params.platformVersion = platformVersion;
        if (vendor) params.supplierName = vendor; // vendor -> supplierName
        if (vendorInterface) params.supplierInterfaceName = vendorInterface; // 字段名适配
        if (vendorVersion) params.supplierVersion = vendorVersion;
        
        console.log('🔍 Request params:', params);
        
        // 调用新的映射搜索API
        const response = await MappingGen.API.get('/api/platform-supplier-mapping/search', params);
        
        if (response.success) {
            console.log('✅ Associations loaded successfully:', response.data);
            displayAssociations(response.data);
            currentPage = page;
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('❌ Error loading associations:', error);
        MappingGen.Utils.showAlert('加载映射关联失败：' + error.message, 'error');
        showEmptyState();
    }
}

/**
 * 显示映射关联数据
 */
function displayAssociations(pageResult) {
    console.log('🖼️ Displaying associations:', pageResult);
    
    const tableBody = document.getElementById('associationTableBody');
    const emptyState = document.getElementById('associationEmptyState');
    const pagination = document.getElementById('associationPagination');
    
    if (!pageResult.records || pageResult.records.length === 0) {
        showEmptyState();
        return;
    }
    
    // 隐藏空状态，显示表格
    emptyState.style.display = 'none';
    tableBody.parentElement.style.display = 'table';
    
    // 清空现有行
    tableBody.innerHTML = '';
    
    // 添加数据行
    pageResult.records.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <div>
                    <div class="text-base">${item.platformAppendInfo || ''}</div>
                    <div class="text-sm text-secondary">
                        业务域: ${item.platformBusinessDomain || ''} | 
                        接口: ${item.platformInterfaceName || ''} | 
                        版本: ${item.platformVersion || ''}
                    </div>
                </div>
            </td>
            <td>
                <div>
                    <div class="text-base">${item.supplierAppendInfo || ''}</div>
                    <div class="text-sm text-secondary">
                        业务域: ${item.supplierBusinessDomain || ''} | 
                        供应商: ${item.supplierName || ''} | 
                        接口: ${item.supplierInterfaceName || ''} | 
                        版本: ${item.supplierVersion || ''}
                    </div>
                </div>
            </td>
            <td>${MappingGen.Utils.formatDate(item.createTime)}</td>
            <td class="action-buttons">
                <button class="btn btn-sm btn-primary" onclick="viewFieldMapping(${item.id})">
                    查看字段映射
                </button>
                <button class="btn btn-sm btn-error" onclick="deleteAssociation(${item.id})">
                    删除
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
    
    // 更新分页
    updatePagination(pageResult);
}

function showEmptyState() {
    const tableBody = document.getElementById('associationTableBody');
    const emptyState = document.getElementById('associationEmptyState');
    const pagination = document.getElementById('associationPagination');
    
    tableBody.parentElement.style.display = 'none';
    pagination.style.display = 'none';
    emptyState.style.display = 'block';
}

function updatePagination(pageResult) {
    const pagination = document.getElementById('associationPagination');
    const paginationInfo = document.getElementById('associationPaginationInfo');
    const paginationControls = document.getElementById('associationPaginationControls');
    
    if (pageResult.totalPages <= 1) {
        pagination.style.display = 'none';
        return;
    }
    
    pagination.style.display = 'flex';
    
    // 更新信息
    const start = (pageResult.pageNum - 1) * pageResult.pageSize + 1;
    const end = Math.min(pageResult.pageNum * pageResult.pageSize, pageResult.total);
    paginationInfo.innerHTML = `显示第 ${start} 到 ${end} 条，共 ${pageResult.total} 条记录`;
    
    // 更新控件
    let controlsHtml = '';
    
    // 上一页按钮
    if (pageResult.hasPrevious) {
        controlsHtml += `<button class="pagination-btn" onclick="loadAssociations(${pageResult.pageNum - 2})">上一页</button>`;
    } else {
        controlsHtml += `<span class="pagination-btn disabled">上一页</span>`;
    }
    
    // 页码按钮
    const startPage = Math.max(1, pageResult.pageNum - 2);
    const endPage = Math.min(pageResult.totalPages, pageResult.pageNum + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        if (i === pageResult.pageNum) {
            controlsHtml += `<span class="pagination-btn active">${i}</span>`;
        } else {
            controlsHtml += `<button class="pagination-btn" onclick="loadAssociations(${i - 1})">${i}</button>`;
        }
    }
    
    // 下一页按钮
    if (pageResult.hasNext) {
        controlsHtml += `<button class="pagination-btn" onclick="loadAssociations(${pageResult.pageNum})">下一页</button>`;
    } else {
        controlsHtml += `<span class="pagination-btn disabled">下一页</span>`;
    }
    
    paginationControls.innerHTML = controlsHtml;
}

function searchAssociations() {
    console.log('🔍 Searching associations...');
    loadAssociations(0);
}

/**
 * 创建映射关联
 */
function createMapping() {
    const form = document.getElementById('addMappingForm');
    
    if (!MappingGen.Utils.validateForm(form)) {
        MappingGen.Utils.showAlert('请填写所有必填字段', 'warning');
        return;
    }
    
    // 获取表单数据
    const formData = MappingGen.Utils.getFormData(form);
    
    // 验证必填字段
    if (!formData.businessDomain || !formData.platformInterface || !formData.platformVersion ||
        !formData.vendor || !formData.vendorInterface || !formData.vendorVersion) {
        MappingGen.Utils.showAlert('请完整填写所有字段', 'warning');
        return;
    }
    
    // 根据选择的接口信息查找对应的ID
    const platformInterfaceId = findPlatformInterfaceId(formData.businessDomain, formData.platformInterface, formData.platformVersion);
    const supplierInterfaceId = findSupplierInterfaceId(formData.businessDomain, formData.vendor, formData.vendorInterface, formData.vendorVersion);
    
    if (!platformInterfaceId || !supplierInterfaceId) {
        MappingGen.Utils.showAlert('无法找到对应的接口ID，请检查选择的数据', 'error');
        return;
    }
    
    // 构造映射数据
    pendingMappingData = {
        platformInterfaceId: platformInterfaceId,
        supplierInterfaceId: supplierInterfaceId,
        // 保存原始选择信息，用于后续导航
        businessDomain: formData.businessDomain,
        platformInterface: formData.platformInterface,
        platformVersion: formData.platformVersion,
        vendor: formData.vendor,
        vendorInterface: formData.vendorInterface,
        vendorVersion: formData.vendorVersion
    };
    
    console.log('📝 Prepared mapping data:', pendingMappingData);
    
    // 隐藏添加表单，显示确认对话框
    MappingGen.Modal.hide('addMappingModal');
    MappingGen.Modal.show('confirmCreateModal');
}

/**
 * 查找平台接口ID
 */
function findPlatformInterfaceId(businessDomain, interfaceName, version) {
    if (!platformTreeData) return null;
    
    const domainData = platformTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.interfaces) {
        const interfaceData = domainData.interfaces.find(i => i.interfaceName === interfaceName);
        if (interfaceData && interfaceData.versions) {
            const versionData = interfaceData.versions.find(v => v.version === version);
            return versionData ? versionData.id : null;
        }
    }
    return null;
}

/**
 * 查找供应商接口ID
 */
function findSupplierInterfaceId(businessDomain, supplierName, interfaceName, version) {
    if (!supplierTreeData) return null;
    
    const domainData = supplierTreeData.find(d => d.businessDomain === businessDomain);
    if (domainData && domainData.suppliers) {
        const supplierData = domainData.suppliers.find(s => s.supplierName === supplierName);
        if (supplierData && supplierData.interfaces) {
            const interfaceData = supplierData.interfaces.find(i => i.interfaceName === interfaceName);
            if (interfaceData && interfaceData.versions) {
                const versionData = interfaceData.versions.find(v => v.version === version);
                return versionData ? versionData.id : null;
            }
        }
    }
    return null;
}

/**
 * 确认创建映射关联 - 使用新的API
 */
async function confirmCreateMapping(navigateToFieldMapping) {
    if (!pendingMappingData) return;
    
    try {
        console.log('💾 Creating mapping with data:', pendingMappingData);
        
        // 调用新的映射创建API
        const response = await MappingGen.API.post('/api/platform-supplier-mapping', {
            platformInterfaceId: pendingMappingData.platformInterfaceId,
            supplierInterfaceId: pendingMappingData.supplierInterfaceId
        });
        
        if (response.success) {
            MappingGen.Utils.showAlert('映射关联创建成功', 'success');
            MappingGen.Modal.hide('confirmCreateModal');
            
            if (navigateToFieldMapping) {
                // 导航到字段映射页面
                const params = new URLSearchParams({
                    businessDomain: pendingMappingData.businessDomain,
                    platformInterface: pendingMappingData.platformInterface,
                    platformVersion: pendingMappingData.platformVersion,
                    vendor: pendingMappingData.vendor,
                    vendorInterface: pendingMappingData.vendorInterface,
                    vendorVersion: pendingMappingData.vendorVersion,
                    associationId: response.data.id
                });
                window.location.href = `/mapping-gen/field-mapping?${params.toString()}`;
            } else {
                // 刷新当前页面
                MappingGen.Utils.clearForm(document.getElementById('addMappingForm'));
                loadAssociations();
            }
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('❌ Error creating mapping:', error);
        MappingGen.Utils.showAlert('创建失败：' + error.message, 'error');
    } finally {
        pendingMappingData = null;
    }
}

function viewFieldMapping(associationId) {
    // 跳转到字段映射页面，传递关联ID参数
    window.location.href = `/mapping-gen/field-mapping?associationId=${associationId}`;
}

function viewKnowledgeBase(knowledgeBaseId, type) {
    window.location.href = `/mapping-gen/knowledge-base/documents/${knowledgeBaseId}?type=${type}`;
}

function deleteAssociation(id) {
    currentDeleteId = id;
    MappingGen.Modal.show('confirmDeleteModal');
}

/**
 * 确认删除映射关联 - 使用新的API
 */
async function confirmDeleteMapping() {
    if (!currentDeleteId) return;
    
    try {
        console.log('🗑️ Deleting mapping with ID:', currentDeleteId);
        
        // 调用新的映射删除API
        const response = await MappingGen.API.delete(`/api/platform-supplier-mapping/${currentDeleteId}`);
        
        if (response.success) {
            MappingGen.Utils.showAlert('删除成功', 'success');
            MappingGen.Modal.hide('confirmDeleteModal');
            loadAssociations();
        } else {
            throw new Error(response.message);
        }
    } catch (error) {
        console.error('❌ Error deleting association:', error);
        MappingGen.Utils.showAlert('删除失败：' + error.message, 'error');
    } finally {
        currentDeleteId = null;
    }
}