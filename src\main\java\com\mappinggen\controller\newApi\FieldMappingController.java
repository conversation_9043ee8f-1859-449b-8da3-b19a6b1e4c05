package com.mappinggen.controller.newApi;

import com.mappinggen.dto.ApiResponse;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 字段映射控制器
 * 提供字段映射关系的查询、保存、生成功能
 */
@RestController
@RequestMapping("/api/field-mapping")
public class FieldMappingController {

    // 模拟数据存储
    private static final Map<String, FieldMappingResponse> fieldMappings = new HashMap<>();
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static Long nextId = 1L;

    static {
        initializeMockData();
    }

    /**
     * 初始化模拟数据
     */
    private static void initializeMockData() {
        // 机票业务域 - 平台接口1 vs 供应商接口1 的字段映射
        String key1 = "1_1"; // platformInterfaceId_supplierInterfaceId
        FieldMappingResponse mapping1 = new FieldMappingResponse();


        // 设置业务字段
        mapping1.setBusinessDomain("机票");
        mapping1.setPlatformInterfaceName("搜索接口");
        mapping1.setPlatformVersion("v1.0");
        mapping1.setSupplierName("东航");
        mapping1.setSupplierInterfaceName("搜索接口");
        mapping1.setSupplierVersion("v1.0");
        // 基础信息
        mapping1.setFieldMappings(Arrays.asList(
            createFieldMapping(1L, 1L, 1L, "flightNo", "response.flightList[i].flightNumber", 
                "// 航班号标准化处理\nif (value.length() < 6) {\n    value = value.padStart(6, '0');\n}", "SCRIPT"),
            createFieldMapping(2L, 1L, 1L, "departureTime", "response.flightList[i].depTime", 
                "// 时间格式转换\nreturn moment(value).format('YYYY-MM-DD HH:mm:ss');", "SCRIPT"),
            createFieldMapping(3L, 1L, 1L, "arrivalTime", "response.flightList[i].arrTime", 
                "// 时间格式转换\nreturn moment(value).format('YYYY-MM-DD HH:mm:ss');", "SCRIPT"),
            createFieldMapping(4L, 1L, 1L, "price", "response.flightList[i].price", 
                "// 价格转换为分\nreturn Math.round(parseFloat(value) * 100);", "SCRIPT"),
            createFieldMapping(5L, 1L, 1L, "departureAirport", "response.flightList[i].originAirport", 
                "// 机场代码标准化\nreturn value.toUpperCase();", "SCRIPT"),
            createFieldMapping(6L, 1L, 1L, "arrivalAirport", "response.flightList[i].destAirport", 
                "// 机场代码标准化\nreturn value.toUpperCase();", "SCRIPT"),
            createFieldMapping(7L, 1L, 1L, "aircraftType", "response.flightList[i].aircraft", null, "DIRECT"),
            createFieldMapping(8L, 1L, 1L, "availableSeats", "response.flightList[i].seats", null, "DIRECT")
        ));
        
        mapping1.setPlatformFieldList(Arrays.asList(
            "flightNo", "departureTime", "arrivalTime", "price", 
            "departureAirport", "arrivalAirport", "aircraftType", "availableSeats"
        ));
        
        mapping1.setSupplierFields("{\"type\":\"object\",\"properties\":{\"response\":{\"type\":\"object\",\"properties\":{\"flightList\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"flightNumber\":{\"type\":\"string\",\"description\":\"航班号\"},\"depTime\":{\"type\":\"string\",\"description\":\"出发时间\"},\"arrTime\":{\"type\":\"string\",\"description\":\"到达时间\"},\"price\":{\"type\":\"number\",\"description\":\"价格\"},\"originAirport\":{\"type\":\"string\",\"description\":\"出发机场\"},\"destAirport\":{\"type\":\"string\",\"description\":\"到达机场\"},\"aircraft\":{\"type\":\"string\",\"description\":\"机型\"},\"seats\":{\"type\":\"number\",\"description\":\"可用座位\"}}}}}}}}");
        
        mapping1.setCommonLogic("// 通用处理逻辑\n// 1. 数据格式标准化\n// 2. 时区转换处理\n// 3. 异常数据过滤\nif (response && response.flightList) {\n    response.flightList = response.flightList.filter(flight => flight.flightNumber && flight.price > 0);\n}");
        
        fieldMappings.put(key1, mapping1);

        // 机票业务域 - 平台接口1 vs 供应商接口2 的字段映射
        String key2 = "1_2";
        FieldMappingResponse mapping2 = new FieldMappingResponse();

        // 设置业务字段
        mapping2.setBusinessDomain("机票");
        mapping2.setPlatformInterfaceName("搜索接口");
        mapping2.setPlatformVersion("v1.1");
        mapping2.setSupplierName("南航");
        mapping2.setSupplierInterfaceName("搜索接口");
        mapping2.setSupplierVersion("v1.0");

        mapping2.setFieldMappings(Arrays.asList(
            createFieldMapping(9L, 1L, 2L, "flightNo", "response.data.flights[i].flight_code", 
                "// 南航字段映射\nreturn value.replace('-', '');", "SCRIPT"),
            createFieldMapping(10L, 1L, 2L, "departureTime", "response.data.flights[i].departure_datetime", 
                "// 南航时间格式转换\nreturn new Date(value).toISOString();", "SCRIPT"),
            createFieldMapping(11L, 1L, 2L, "arrivalTime", "response.data.flights[i].arrival_datetime", 
                "// 南航时间格式转换\nreturn new Date(value).toISOString();", "SCRIPT"),
            createFieldMapping(12L, 1L, 2L, "price", "response.data.flights[i].fare.amount", 
                "// 南航价格转换\nreturn parseInt(value * 100);", "SCRIPT"),
            createFieldMapping(13L, 1L, 2L, "departureAirport", "response.data.flights[i].origin_code", null, "DIRECT"),
            createFieldMapping(14L, 1L, 2L, "arrivalAirport", "response.data.flights[i].dest_code", null, "DIRECT")
        ));
        
        mapping2.setPlatformFieldList(Arrays.asList(
            "flightNo", "departureTime", "arrivalTime", "price", 
            "departureAirport", "arrivalAirport"
        ));
        
        mapping2.setSupplierFields("{\"type\":\"object\",\"properties\":{\"response\":{\"type\":\"object\",\"properties\":{\"data\":{\"type\":\"object\",\"properties\":{\"flights\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"flight_code\":{\"type\":\"string\",\"description\":\"航班代码\"},\"departure_datetime\":{\"type\":\"string\",\"description\":\"出发时间\"},\"arrival_datetime\":{\"type\":\"string\",\"description\":\"到达时间\"},\"fare\":{\"type\":\"object\",\"properties\":{\"amount\":{\"type\":\"number\",\"description\":\"价格\"}}},\"origin_code\":{\"type\":\"string\",\"description\":\"出发机场代码\"},\"dest_code\":{\"type\":\"string\",\"description\":\"到达机场代码\"}}}}}}}}}}");
        
        mapping2.setCommonLogic("// 南航接口通用处理逻辑\n// 1. 数据清洗和验证\n// 2. 南航特有字段处理\nif (response && response.data && response.data.flights) {\n    response.data.flights = response.data.flights.filter(f => f.flight_code && f.fare.amount);\n}");
        
        fieldMappings.put(key2, mapping2);

        // 酒店业务域 - 平台接口4 vs 供应商接口5 的字段映射
        String key3 = "4_5";
        FieldMappingResponse mapping3 = new FieldMappingResponse();

        // 设置业务字段
        mapping3.setBusinessDomain("酒店");
        mapping3.setPlatformInterfaceName("搜索接口");
        mapping3.setPlatformVersion("v1.0");
        mapping3.setSupplierName("携程");
        mapping3.setSupplierInterfaceName("搜索接口");
        mapping3.setSupplierVersion("v1.0");

        mapping3.setFieldMappings(Arrays.asList(
            createFieldMapping(15L, 4L, 5L, "hotelId", "response.hotels[i].id", null, "DIRECT"),
            createFieldMapping(16L, 4L, 5L, "hotelName", "response.hotels[i].name", 
                "// 酒店名称标准化\nreturn value.trim().replace(/\\s+/g, ' ');", "SCRIPT"),
            createFieldMapping(17L, 4L, 5L, "price", "response.hotels[i].price", 
                "// 酒店价格转换\nreturn Math.round(value * 100);", "SCRIPT"),
            createFieldMapping(18L, 4L, 5L, "rating", "response.hotels[i].rating", 
                "// 评分标准化\nreturn Math.round(value * 10) / 10;", "SCRIPT"),
            createFieldMapping(19L, 4L, 5L, "location", "response.hotels[i].address", null, "DIRECT")
        ));
        
        mapping3.setPlatformFieldList(Arrays.asList(
            "hotelId", "hotelName", "price", "rating", "location"
        ));
        
        mapping3.setSupplierFields("{\"type\":\"object\",\"properties\":{\"response\":{\"type\":\"object\",\"properties\":{\"hotels\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"id\":{\"type\":\"string\",\"description\":\"酒店ID\"},\"name\":{\"type\":\"string\",\"description\":\"酒店名称\"},\"price\":{\"type\":\"number\",\"description\":\"价格\"},\"rating\":{\"type\":\"number\",\"description\":\"评分\"},\"address\":{\"type\":\"string\",\"description\":\"地址\"}}}}}}}}");
        
        mapping3.setCommonLogic("// 酒店接口通用处理逻辑\n// 1. 价格和评分数据验证\n// 2. 地址信息标准化\nif (response && response.hotels) {\n    response.hotels = response.hotels.filter(hotel => hotel.id && hotel.price > 0 && hotel.rating >= 0);\n}");
        
        fieldMappings.put(key3, mapping3);

        // 火车票业务域映射数据
        String key4 = "6_7";
        FieldMappingResponse mapping4 = new FieldMappingResponse();

        // 设置业务字段
        mapping4.setBusinessDomain("火车票");
        mapping4.setPlatformInterfaceName("搜索接口");
        mapping4.setPlatformVersion("v1.0");
        mapping4.setSupplierName("12306");
        mapping4.setSupplierInterfaceName("查询接口");
        mapping4.setSupplierVersion("v1.0");

        mapping4.setFieldMappings(Arrays.asList(
            createFieldMapping(20L, 6L, 7L, "trainNo", "response.trains[i].trainCode", null, "DIRECT"),
            createFieldMapping(21L, 6L, 7L, "departureTime", "response.trains[i].startTime", 
                "// 火车时间格式转换\nreturn moment(value, 'HH:mm').format('YYYY-MM-DD HH:mm:ss');", "SCRIPT"),
            createFieldMapping(22L, 6L, 7L, "arrivalTime", "response.trains[i].endTime", 
                "// 火车时间格式转换\nreturn moment(value, 'HH:mm').format('YYYY-MM-DD HH:mm:ss');", "SCRIPT"),
            createFieldMapping(23L, 6L, 7L, "price", "response.trains[i].ticketPrice", 
                "// 火车票价格转换\nreturn Math.round(value * 100);", "SCRIPT"),
            createFieldMapping(24L, 6L, 7L, "duration", "response.trains[i].travelTime", 
                "// 旅行时长计算\nreturn calculateDurationInMinutes(value);", "SCRIPT")
        ));
        
        mapping4.setPlatformFieldList(Arrays.asList(
            "trainNo", "departureTime", "arrivalTime", "price", "duration"
        ));
        
        mapping4.setSupplierFields("{\"type\":\"object\",\"properties\":{\"response\":{\"type\":\"object\",\"properties\":{\"trains\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"trainCode\":{\"type\":\"string\",\"description\":\"车次\"},\"startTime\":{\"type\":\"string\",\"description\":\"出发时间\"},\"endTime\":{\"type\":\"string\",\"description\":\"到达时间\"},\"ticketPrice\":{\"type\":\"number\",\"description\":\"票价\"},\"travelTime\":{\"type\":\"string\",\"description\":\"行程时间\"}}}}}}}}");
        
        mapping4.setCommonLogic("// 火车票接口通用处理逻辑\n// 1. 车次信息验证\n// 2. 时间数据标准化\nif (response && response.trains) {\n    response.trains = response.trains.filter(train => train.trainCode && train.ticketPrice > 0);\n}");
        
        fieldMappings.put(key4, mapping4);
        
        // 支付业务域映射数据
        String key5 = "9_10";
        FieldMappingResponse mapping5 = new FieldMappingResponse();

        mapping5.setBusinessDomain("用户管理");
        mapping5.setPlatformInterfaceName("用户查询");
        mapping5.setPlatformVersion("v1.2");
        mapping5.setSupplierName("微信");
        mapping5.setSupplierInterfaceName("用户接口");
        mapping5.setSupplierVersion("v1.0");

        mapping5.setFieldMappings(Arrays.asList(
            createFieldMapping(25L, 9L, 10L, "userId", "response.openid", null, "DIRECT"),
            createFieldMapping(26L, 9L, 10L, "username", "response.nickname",
                "// 用户名标准化\nreturn value ? value.trim() : '';", "SCRIPT"),
            createFieldMapping(27L, 9L, 10L, "avatar", "response.headimgurl", null, "DIRECT"),
            createFieldMapping(28L, 9L, 10L, "loginTime", "response.login_time",
                "// 登录时间转换\nreturn new Date(value * 1000).toISOString();", "SCRIPT")
        ));

        mapping5.setPlatformFieldList(Arrays.asList(
            "userId", "username", "avatar", "loginTime"
        ));

        mapping5.setSupplierFields("{\"type\":\"object\",\"properties\":{\"response\":{\"type\":\"object\",\"properties\":{\"openid\":{\"type\":\"string\",\"description\":\"用户唯一标识\"},\"nickname\":{\"type\":\"string\",\"description\":\"用户昵称\"},\"headimgurl\":{\"type\":\"string\",\"description\":\"头像URL\"},\"login_time\":{\"type\":\"number\",\"description\":\"登录时间戳\"}}}}}");

        mapping5.setCommonLogic("// 微信登录接口通用处理逻辑\n// 1. 用户信息验证\n// 2. 头像URL处理\n// 3. 时间戳转换\nif (response) {\n    if (!response.openid) {\n        throw new Error('缺少用户唯一标识');\n    }\n}");
        
        fieldMappings.put(key5, mapping5);
    }

    /**
     * 创建字段映射对象的辅助方法
     */
    private static FieldMappingInfo createFieldMapping(Long id, Long platformInterfaceId, Long supplierInterfaceId, 
                                                      String platformField, String supplierField, 
                                                      String supplementaryLogic, String mappingInfo) {
        FieldMappingInfo mapping = new FieldMappingInfo();
        mapping.setId(id);
        mapping.setPlatformInterfaceId(platformInterfaceId);
        mapping.setSupplierInterfaceId(supplierInterfaceId);
        mapping.setPlatformField(platformField);
        mapping.setSupplierField(supplierField);
        mapping.setSupplementaryLogic(supplementaryLogic);
        mapping.setMappingInfo(mappingInfo);
        mapping.setOperator("系统管理员");
        mapping.setCreateTime(LocalDateTime.now().minusDays((long)(Math.random() * 30)).format(DATE_FORMAT));
        mapping.setUpdateTime(LocalDateTime.now().minusDays((long)(Math.random() * 10)).format(DATE_FORMAT));
        return mapping;
    }

    /**
     * 6.1 查询字段映射
     * GET /api/field-mapping
     */
    @GetMapping
    public ApiResponse<FieldMappingResponse> getFieldMapping(
            @RequestParam Long platformInterfaceId,
            @RequestParam Long supplierInterfaceId) {
        
        try {
            String key = platformInterfaceId + "_" + supplierInterfaceId;
            FieldMappingResponse mapping = fieldMappings.get(key);
            
            if (mapping == null) {
                // 创建默认的字段映射
                mapping = createDefaultFieldMapping(platformInterfaceId, supplierInterfaceId);
            }
            
            return ApiResponse.success(mapping);
        } catch (Exception e) {
            return ApiResponse.error("查询字段映射失败：" + e.getMessage());
        }
    }

    /**
     * 6.2 根据映射ID查询字段映射
     * GET /api/field-mapping/getFieldMappingByMappingId
     */
    @GetMapping("/getFieldMappingByMappingId")
    public ApiResponse<FieldMappingResponse> getFieldMappingByMappingId(
            @RequestParam Long platformSupplierId) {

        try {
            // 根据platformSupplierId映射到对应的字段映射关系
            String key = "1_1"; // 默认返回第一个映射关系

            // 根据ID映射到不同的业务场景
            Map<Long, String> idMapping = new HashMap<>();
            idMapping.put(1L, "1_1");
            idMapping.put(2L, "1_2");
            idMapping.put(3L, "4_5");
            idMapping.put(4L, "6_7");
            idMapping.put(5L, "9_10");

            key = idMapping.getOrDefault(platformSupplierId, "1_1");

            FieldMappingResponse mapping = fieldMappings.get(key);
            if (mapping == null) {
                mapping = fieldMappings.get("1_1"); // 兜底返回
            }

            return ApiResponse.success(mapping);
        } catch (Exception e) {
            return ApiResponse.error("根据映射ID查询字段映射失败：" + e.getMessage());
        }
    }

    /**
     * 6.3 暂存字段映射关系
     * POST /api/field-mapping/save
     */
    @PostMapping("/save")
    public ApiResponse<Void> saveFieldMapping(
            @RequestParam Long platformInterfaceId,
            @RequestParam Long supplierInterfaceId,
            @RequestParam String commonLogic,
            @RequestBody List<FieldMappingRequest> fieldMappingRequests) {
        
        try {
            String key = platformInterfaceId + "_" + supplierInterfaceId;
            FieldMappingResponse mapping = fieldMappings.get(key);
            
            if (mapping == null) {
                mapping = createDefaultFieldMapping(platformInterfaceId, supplierInterfaceId);
            }
            
            // 更新通用逻辑
            mapping.setCommonLogic(commonLogic);
            
            // 更新字段映射
            List<FieldMappingInfo> updatedMappings = new ArrayList<>();
            for (int i = 0; i < fieldMappingRequests.size(); i++) {
                FieldMappingRequest request = fieldMappingRequests.get(i);
                FieldMappingInfo info = new FieldMappingInfo();
                info.setId((long) (nextId++));
                info.setPlatformInterfaceId(platformInterfaceId);
                info.setSupplierInterfaceId(supplierInterfaceId);
                info.setPlatformField(request.getPlatformField());
                info.setSupplierField(request.getSupplierField());
                info.setSupplementaryLogic(request.getSupplementaryLogic());
                info.setMappingInfo(request.getMappingInfo());
                info.setOperator("用户");
                info.setCreateTime(LocalDateTime.now().format(DATE_FORMAT));
                info.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
                updatedMappings.add(info);
            }
            mapping.setFieldMappings(updatedMappings);
            
            fieldMappings.put(key, mapping);
            
            return ApiResponse.success("暂存字段映射关系成功");
        } catch (Exception e) {
            return ApiResponse.error("暂存字段映射关系失败：" + e.getMessage());
        }
    }

    /**
     * 6.4 生成字段映射关系
     * POST /api/field-mapping/generate
     */
    @PostMapping("/generate")
    public ApiResponse<List<FieldMappingInfo>> generateFieldMapping(
            @RequestParam Long platformInterfaceId,
            @RequestParam Long supplierInterfaceId,
            @RequestParam String commonLogic,
            @RequestBody List<FieldMappingRequest> fieldMappingRequests) {
        
        try {
            String key = platformInterfaceId + "_" + supplierInterfaceId;
            FieldMappingResponse mapping = fieldMappings.get(key);
            
            if (mapping == null) {
                mapping = createDefaultFieldMapping(platformInterfaceId, supplierInterfaceId);
            }
            
            // 更新通用逻辑
            mapping.setCommonLogic(commonLogic);
            
            // 生成字段映射规则
            List<FieldMappingInfo> generatedMappings = new ArrayList<>();
            for (FieldMappingRequest request : fieldMappingRequests) {
                FieldMappingInfo info = new FieldMappingInfo();
                info.setId((long) (nextId++));
                info.setPlatformInterfaceId(platformInterfaceId);
                info.setSupplierInterfaceId(supplierInterfaceId);
                info.setPlatformField(request.getPlatformField());
                info.setSupplierField(request.getSupplierField());
                info.setSupplementaryLogic(request.getSupplementaryLogic());
                info.setMappingInfo(generateMappingRule(request));
                info.setOperator("系统");
                info.setCreateTime(LocalDateTime.now().format(DATE_FORMAT));
                info.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
                generatedMappings.add(info);
            }
            
            mapping.setFieldMappings(generatedMappings);
            fieldMappings.put(key, mapping);
            
            return ApiResponse.success("生成字段映射关系成功", generatedMappings);
        } catch (Exception e) {
            return ApiResponse.error("生成字段映射关系失败：" + e.getMessage());
        }
    }

    /**
     * 创建默认字段映射
     */
    private FieldMappingResponse createDefaultFieldMapping(Long platformInterfaceId, Long supplierInterfaceId) {
        FieldMappingResponse mapping = new FieldMappingResponse();
        
        // 设置默认的平台字段列表
        mapping.setPlatformFieldList(Arrays.asList("id", "name", "status", "createTime", "updateTime"));
        
        // 设置默认的供应商字段结构
        mapping.setSupplierFields("{\"type\":\"object\",\"properties\":{\"response\":{\"type\":\"object\",\"properties\":{\"data\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"id\":{\"type\":\"string\"},\"name\":{\"type\":\"string\"},\"status\":{\"type\":\"string\"},\"createTime\":{\"type\":\"string\"},\"updateTime\":{\"type\":\"string\"}}}}}}}}");
        
        // 设置默认通用逻辑
        mapping.setCommonLogic("// 默认通用处理逻辑\n// 1. 数据格式验证\n// 2. 空值处理\n// 3. 异常数据过滤\nif (response && response.data) {\n    response.data = response.data.filter(item => item.id && item.name);\n}");
        
        // 创建默认字段映射
        List<FieldMappingInfo> defaultMappings = new ArrayList<>();
        String[] platformFields = {"id", "name", "status", "createTime", "updateTime"};
        String[] supplierFields = {"response.data[i].id", "response.data[i].name", "response.data[i].status", "response.data[i].createTime", "response.data[i].updateTime"};
        
        for (int i = 0; i < platformFields.length; i++) {
            FieldMappingInfo info = new FieldMappingInfo();
            info.setId((long) (nextId++));
            info.setPlatformInterfaceId(platformInterfaceId);
            info.setSupplierInterfaceId(supplierInterfaceId);
            info.setPlatformField(platformFields[i]);
            info.setSupplierField(supplierFields[i]);
            info.setSupplementaryLogic(null);
            info.setMappingInfo("DIRECT");
            info.setOperator("系统");
            info.setCreateTime(LocalDateTime.now().format(DATE_FORMAT));
            info.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
            defaultMappings.add(info);
        }
        
        mapping.setFieldMappings(defaultMappings);
        return mapping;
    }

    /**
     * 生成映射规则
     */
    private String generateMappingRule(FieldMappingRequest request) {
        String mappingType = request.getMappingInfo();
        if (mappingType == null) {
            mappingType = "DIRECT";
        }
        
        switch (mappingType.toUpperCase()) {
            case "DIRECT":
                return "DIRECT";
            case "CONSTANT":
                return "CONSTANT";
            case "SCRIPT":
            default:
                String rule = "SCRIPT";
                if (request.getSupplementaryLogic() != null && !request.getSupplementaryLogic().trim().isEmpty()) {
                    rule = "SCRIPT_WITH_LOGIC";
                }
                
                // 根据字段类型生成智能映射规则
                String fieldName = request.getPlatformField().toLowerCase();
                if (fieldName.contains("time") || fieldName.contains("date")) {
                    rule = "SCRIPT_TIME_FORMAT";
                } else if (fieldName.contains("price") || fieldName.contains("amount")) {
                    rule = "SCRIPT_PRICE_CONVERSION";
                } else if (fieldName.contains("status")) {
                    rule = "SCRIPT_STATUS_MAPPING";
                }
                
                return rule;
        }
    }

    // ===== 内部DTO类定义 =====

    /**
     * 字段映射响应对象
     */
    public static class FieldMappingResponse {
        private List<FieldMappingInfo> fieldMappings;
        private List<String> platformFieldList;
        private String supplierFields;
        private String commonLogic;

        // 新增字段
        private String businessDomain;
        private String platformInterfaceName;
        private String platformVersion;
        private String supplierName;
        private String supplierInterfaceName;
        private String supplierVersion;

        // Getters and Setters
        public String getBusinessDomain() { return businessDomain; }
        public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }

        public String getPlatformInterfaceName() { return platformInterfaceName; }
        public void setPlatformInterfaceName(String platformInterfaceName) { this.platformInterfaceName = platformInterfaceName; }

        public String getPlatformVersion() { return platformVersion; }
        public void setPlatformVersion(String platformVersion) { this.platformVersion = platformVersion; }

        public String getSupplierName() { return supplierName; }
        public void setSupplierName(String supplierName) { this.supplierName = supplierName; }

        public String getSupplierInterfaceName() { return supplierInterfaceName; }
        public void setSupplierInterfaceName(String supplierInterfaceName) { this.supplierInterfaceName = supplierInterfaceName; }

        public String getSupplierVersion() { return supplierVersion; }
        public void setSupplierVersion(String supplierVersion) { this.supplierVersion = supplierVersion; }

        // Getters and Setters
        public List<FieldMappingInfo> getFieldMappings() { return fieldMappings; }
        public void setFieldMappings(List<FieldMappingInfo> fieldMappings) { this.fieldMappings = fieldMappings; }

        public List<String> getPlatformFieldList() { return platformFieldList; }
        public void setPlatformFieldList(List<String> platformFieldList) { this.platformFieldList = platformFieldList; }

        public String getSupplierFields() { return supplierFields; }
        public void setSupplierFields(String supplierFields) { this.supplierFields = supplierFields; }

        public String getCommonLogic() { return commonLogic; }
        public void setCommonLogic(String commonLogic) { this.commonLogic = commonLogic; }
    }

    /**
     * 字段映射信息对象
     */
    public static class FieldMappingInfo {
        private Long id;
        private Long platformInterfaceId;
        private Long supplierInterfaceId;
        private String platformField;
        private String supplierField;
        private String supplementaryLogic;
        private String mappingInfo;
        private String operator;
        private String createTime;
        private String updateTime;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public Long getPlatformInterfaceId() { return platformInterfaceId; }
        public void setPlatformInterfaceId(Long platformInterfaceId) { this.platformInterfaceId = platformInterfaceId; }

        public Long getSupplierInterfaceId() { return supplierInterfaceId; }
        public void setSupplierInterfaceId(Long supplierInterfaceId) { this.supplierInterfaceId = supplierInterfaceId; }

        public String getPlatformField() { return platformField; }
        public void setPlatformField(String platformField) { this.platformField = platformField; }

        public String getSupplierField() { return supplierField; }
        public void setSupplierField(String supplierField) { this.supplierField = supplierField; }

        public String getSupplementaryLogic() { return supplementaryLogic; }
        public void setSupplementaryLogic(String supplementaryLogic) { this.supplementaryLogic = supplementaryLogic; }

        public String getMappingInfo() { return mappingInfo; }
        public void setMappingInfo(String mappingInfo) { this.mappingInfo = mappingInfo; }

        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }

        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }

        public String getUpdateTime() { return updateTime; }
        public void setUpdateTime(String updateTime) { this.updateTime = updateTime; }
    }

    /**
     * 字段映射请求对象
     */
    public static class FieldMappingRequest {
        private String platformField;
        private String supplierField;
        private String mappingInfo;
        private String supplementaryLogic;

        // Getters and Setters
        public String getPlatformField() { return platformField; }
        public void setPlatformField(String platformField) { this.platformField = platformField; }

        public String getSupplierField() { return supplierField; }
        public void setSupplierField(String supplierField) { this.supplierField = supplierField; }

        public String getMappingInfo() { return mappingInfo; }
        public void setMappingInfo(String mappingInfo) { this.mappingInfo = mappingInfo; }

        public String getSupplementaryLogic() { return supplementaryLogic; }
        public void setSupplementaryLogic(String supplementaryLogic) { this.supplementaryLogic = supplementaryLogic; }
    }
}
