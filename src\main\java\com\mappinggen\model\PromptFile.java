package com.mappinggen.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * Prompt file entity for code prompt documents
 */
public class PromptFile {
    
    private Long id;
    private Long codePromptId;
    private String fileName;
    private String fileType;
    private String content;
    private boolean editable;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    // Constructors
    public PromptFile() {}
    
    public PromptFile(String fileName, String fileType, String content, boolean editable) {
        this.fileName = fileName;
        this.fileType = fileType;
        this.content = content;
        this.editable = editable;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Long getCodePromptId() { return codePromptId; }
    public void setCodePromptId(Long codePromptId) { this.codePromptId = codePromptId; }
    
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    
    public String getFileType() { return fileType; }
    public void setFileType(String fileType) { this.fileType = fileType; }
    
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    
    public boolean isEditable() { return editable; }
    public void setEditable(boolean editable) { this.editable = editable; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
}
