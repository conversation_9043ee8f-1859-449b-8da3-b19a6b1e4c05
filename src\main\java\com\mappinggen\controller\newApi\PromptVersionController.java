package com.mappinggen.controller.newApi;

import com.mappinggen.dto.ApiResponse;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Prompt版本管理控制器
 * 提供Prompt版本的查询、创建、更新功能
 */
@RestController
@RequestMapping("/api/prompt-version")
public class PromptVersionController {

    // 模拟数据存储
    private static final Map<Long, PromptVersionResponse> promptVersions = new HashMap<>();
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static Long nextId = 1L;

    static {
        initializeMockData();
    }

    /**
     * 初始化模拟数据
     */
    private static void initializeMockData() {
        // 机票业务域 - 东航搜索接口 v1.0
        PromptVersionResponse prompt1 = new PromptVersionResponse();
        prompt1.setId(nextId++);
        prompt1.setPlatformInterfaceId(1L);
        prompt1.setSupplierInterfaceId(1L);
        prompt1.setReqBeanPrompt("根据以下航班搜索接口文档生成Java Bean类：\n\n```\n接口：航班搜索\n参数：\n- origin: 出发城市代码（必填）\n- destination: 到达城市代码（必填）\n- departureDate: 出发日期（必填）\n```\n\n请生成对应的请求Bean类，包含：\n1. 字段定义\n2. getter/setter方法\n3. 参数校验注解\n4. 东航接口特定的字段映射");
        prompt1.setRespBeanPrompt("根据东航航班搜索响应结构生成Java Bean类：\n\n```json\n{\n  \"code\": \"200\",\n  \"message\": \"success\",\n  \"data\": {\n    \"flights\": [\n      {\n        \"flightNo\": \"MU1234\",\n        \"departureTime\": \"08:30\",\n        \"arrivalTime\": \"10:45\",\n        \"price\": 850,\n        \"aircraft\": \"A320\",\n        \"availableSeats\": 45\n      }\n    ]\n  }\n}\n```\n\n请生成对应的响应Bean类，包含嵌套对象处理。");
        prompt1.setCodeGenPrompt("基于以上文档和Bean定义，生成完整的东航航班搜索适配器代码，要求：\n\n1. 使用Spring Boot注解\n2. 包含参数校验\n3. 异常处理\n4. 返回统一响应格式\n5. 添加接口文档注解\n6. 东航特定的字段映射逻辑\n7. 处理东航接口的特殊返回格式\n\n示例适配器方法应包含@Service、完整的字段映射和错误处理。");
        prompt1.setVersion("v1.0");
        prompt1.setOperator("张三");
        prompt1.setCreateTime(LocalDateTime.now().minusDays(15).format(DATE_FORMAT));
        prompt1.setUpdateTime(LocalDateTime.now().minusDays(10).format(DATE_FORMAT));
        promptVersions.put(prompt1.getId(), prompt1);

        // 机票业务域 - 东航搜索接口 v1.1
        PromptVersionResponse prompt2 = new PromptVersionResponse();
        prompt2.setId(nextId++);
        prompt2.setPlatformInterfaceId(2L); // 平台接口v1.1
        prompt2.setSupplierInterfaceId(1L); // 同一个东航接口
        prompt2.setReqBeanPrompt("根据以下航班搜索接口v1.1文档生成Java Bean类：\n\n```\n接口：航班搜索v1.1\n新增参数：\n- tripType: 行程类型（单程/往返）\n- passengerType: 乘客类型\n- filters: 高级筛选条件\n```\n\n请生成对应的请求Bean类，支持东航v1.1的新特性。");
        prompt2.setRespBeanPrompt("根据东航航班搜索v1.1响应结构生成Java Bean类，新增了总结果数和筛选选项字段。");
        prompt2.setCodeGenPrompt("生成东航航班搜索v1.1适配器代码，支持多程航班和高级筛选功能。");
        prompt2.setVersion("v1.1");
        prompt2.setOperator("李四");
        prompt2.setCreateTime(LocalDateTime.now().minusDays(12).format(DATE_FORMAT));
        prompt2.setUpdateTime(LocalDateTime.now().minusDays(8).format(DATE_FORMAT));
        promptVersions.put(prompt2.getId(), prompt2);

        // 机票业务域 - 南航搜索接口 v1.0
        PromptVersionResponse prompt3 = new PromptVersionResponse();
        prompt3.setId(nextId++);
        prompt3.setPlatformInterfaceId(1L); // 平台接口v1.0
        prompt3.setSupplierInterfaceId(2L); // 南航接口
        prompt3.setReqBeanPrompt("根据以下航班搜索接口文档生成适配南航的Java Bean类：\n\n南航接口特点：\n- 使用不同的城市代码格式\n- 需要额外的航线验证\n- 特殊的日期格式要求");
        prompt3.setRespBeanPrompt("根据南航航班搜索响应结构生成Java Bean类，南航返回格式与东航略有不同。");
        prompt3.setCodeGenPrompt("生成南航航班搜索适配器代码，处理南航特有的接口格式和错误码。");
        prompt3.setVersion("v1.0");
        prompt3.setOperator("王五");
        prompt3.setCreateTime(LocalDateTime.now().minusDays(10).format(DATE_FORMAT));
        prompt3.setUpdateTime(LocalDateTime.now().minusDays(6).format(DATE_FORMAT));
        promptVersions.put(prompt3.getId(), prompt3);

        // 机票业务域 - 预订接口相关prompt
        PromptVersionResponse prompt4 = new PromptVersionResponse();
        prompt4.setId(nextId++);
        prompt4.setPlatformInterfaceId(3L); // 平台预订接口
        prompt4.setSupplierInterfaceId(3L); // 东航预订接口
        prompt4.setReqBeanPrompt("根据机票预订接口文档生成Java Bean类，包含乘客信息、支付信息等复杂对象。");
        prompt4.setRespBeanPrompt("生成机票预订响应Bean，包含订单状态、确认号等信息。");
        prompt4.setCodeGenPrompt("生成机票预订适配器代码，处理预订流程、支付集成和订单状态管理。");
        prompt4.setVersion("v1.0");
        prompt4.setOperator("赵六");
        prompt4.setCreateTime(LocalDateTime.now().minusDays(8).format(DATE_FORMAT));
        prompt4.setUpdateTime(LocalDateTime.now().minusDays(5).format(DATE_FORMAT));
        promptVersions.put(prompt4.getId(), prompt4);

        // 酒店业务域 - 搜索接口
        PromptVersionResponse prompt5 = new PromptVersionResponse();
        prompt5.setId(nextId++);
        prompt5.setPlatformInterfaceId(4L); // 酒店搜索接口
        prompt5.setSupplierInterfaceId(4L); // 携程酒店接口
        prompt5.setReqBeanPrompt("根据酒店搜索接口文档生成Java Bean类：\n\n```\n接口：酒店搜索\n参数：\n- city: 城市代码\n- checkInDate: 入住日期\n- checkOutDate: 离店日期\n- roomCount: 房间数量\n- guestCount: 客人数量\n```");
        prompt5.setRespBeanPrompt("生成酒店搜索响应Bean，包含酒店信息、房型、价格等。");
        prompt5.setCodeGenPrompt("生成酒店搜索适配器代码，处理携程酒店接口的特殊格式和房型映射。");
        prompt5.setVersion("v1.0");
        prompt5.setOperator("孙七");
        prompt5.setCreateTime(LocalDateTime.now().minusDays(6).format(DATE_FORMAT));
        prompt5.setUpdateTime(LocalDateTime.now().minusDays(4).format(DATE_FORMAT));
        promptVersions.put(prompt5.getId(), prompt5);

        // 用户管理业务域
        PromptVersionResponse prompt6 = new PromptVersionResponse();
        prompt6.setId(nextId++);
        prompt6.setPlatformInterfaceId(5L); // 用户查询接口
        prompt6.setSupplierInterfaceId(5L); // 内部用户服务
        prompt6.setReqBeanPrompt("生成用户管理相关的Bean类，包含用户信息、权限等。");
        prompt6.setRespBeanPrompt("生成用户查询响应Bean，包含用户详情和权限信息。");
        prompt6.setCodeGenPrompt("生成用户管理适配器代码，处理用户认证、授权和信息管理。");
        prompt6.setVersion("v1.2");
        prompt6.setOperator("周八");
        prompt6.setCreateTime(LocalDateTime.now().minusDays(5).format(DATE_FORMAT));
        prompt6.setUpdateTime(LocalDateTime.now().minusDays(3).format(DATE_FORMAT));
        promptVersions.put(prompt6.getId(), prompt6);

        // 火车票业务域 - 搜索接口
        PromptVersionResponse prompt7 = new PromptVersionResponse();
        prompt7.setId(nextId++);
        prompt7.setPlatformInterfaceId(6L); // 火车票搜索接口
        prompt7.setSupplierInterfaceId(6L); // 12306接口
        prompt7.setReqBeanPrompt("根据火车票搜索接口文档生成Java Bean类：\n\n```\n接口：火车票搜索\n参数：\n- fromStation: 出发站\n- toStation: 到达站\n- trainDate: 乘车日期\n- trainType: 车次类型（高铁/动车/普通）\n```");
        prompt7.setRespBeanPrompt("生成火车票搜索响应Bean，包含车次信息、座位类型、票价等。");
        prompt7.setCodeGenPrompt("生成火车票搜索适配器代码，处理12306接口的复杂验证和座位映射。");
        prompt7.setVersion("v1.0");
        prompt7.setOperator("吴九");
        prompt7.setCreateTime(LocalDateTime.now().minusDays(4).format(DATE_FORMAT));
        prompt7.setUpdateTime(LocalDateTime.now().minusDays(2).format(DATE_FORMAT));
        promptVersions.put(prompt7.getId(), prompt7);

        // 支付业务域 - 支付接口
        PromptVersionResponse prompt8 = new PromptVersionResponse();
        prompt8.setId(nextId++);
        prompt8.setPlatformInterfaceId(9L); // 支付接口
        prompt8.setSupplierInterfaceId(7L); // 微信支付接口
        prompt8.setReqBeanPrompt("为一名资深全栈开发人员，请详细分析的 Spring Boot + Thymeleaf 项目代码库，系统地优化以下功能需求。请严格按照企业级开发标准，确保与现有代码库和之前修复工作的完全兼容性。\n" +
                "\n" +
                "## 待优化需求\n" +
                "\n" +
                "\n" +
                "### 1. 建立关联页面点击查看字段映射按钮跳转字段映射页面流程优化\n" +
                "**点击查看字段映射按钮后的流程：**\n" +
                "  - 当前点击按钮后的流程中会请求 api/field-mapping/getFieldMappingByMappingId?platformSupplierId={id}接口获取后端返回值\n" +
                "  - 现在这个后端返回值中需要新增六个字段，这六个字段分别为这个映射关系的业务领域、平台接口、平台版本、供应商、供应商接口、供应商版本\n" +
                "  - 请在后端新增这六个字段，字段名与之前保持一致\n" +
                "  - 前端获取后端返回值，并将这六个字段填充在下拉菜单中展示出来\n" +
                "  - 下拉菜单的数据级联回填逻辑不能受到影响\n" +
                "\n" +
                "请你先详细了解代码，然后在前端和后端进行代码改动，改动不可以影响以前的任何逻辑\n" +
                "\n" +
                "\n" +
                "## 技术要求和约束\n" +
                "\n" +
                "### 1. 问题分析方法\n" +
                "- 使用代码库检索工具深入分析每个问题的根本原因\n" +
                "- 识别相关的 HTML 模板、CSS 样式、JavaScript 事件处理程序和后端控制器\n" +
                "- 确定问题是系统性问题（需要全局修复）还是局部问题（需要有针对性的修复）\n" +
                "- 检查与之前的修复迭代是否存在冲突或依赖关系\n" +
                "\n" +
                "### 2. 影响评估标准\n" +
                "- 评估潜在影响每次修复对现有功能的影响\n" +
                "- 识别可能受影响的页面、组件和用户工作流程\n" +
                "- 确保修复不会破坏之前已完成的修复工作\n" +
                "- 记录任何新的依赖关系或级联效应\n" +
                "\n" +
                "### 3. 代码质量要求\n" +
                "- **一致性**：确保修复后的代码与现有项目和之前的修复保持一致\n" +
                "- **可维护性**：编写清晰、带注释的代码，方便将来的维护和扩展\n" +
                "- **可扩展性**：设计修复时考虑未来的功能扩展需求\n" +
                "- **稳定性**：确保修复后的功能稳定可靠，无回归问题\n" +
                "- **性能**：确保修复不会对页面加载时间或交互性能产生负面影响\n" +
                "\n" +
                "### 4. 修复范围控制原则\n" +
                "- 专注于解决明确定义的问题；避免过度设计或功能蔓延\n" +
                "- 维护现有功能行为，尤其是之前修复的功能\n" +
                "- 遵循最小变更原则 - 仅修改必要代码\n" +
                "- 避免与当前问题无关的代码重构或优化\n" +
                "\n" +
                "### 5. 技术实施标准\n" +
                "- 严格遵循 Spring Boot 和 Thymeleaf 的最佳实践和项目约定\n" +
                "- 使用现有的 CSS 框架、JavaScript 模式和组件结构\n" +
                "- 实施适当的错误处理、用户反馈机制和边缘情况处理\n" +
                "- 遵循响应式设计原则，确保移动兼容性\n" +
                "\n" +
                "## 交付成果要求\n" +
                "\n" +
                "### 针对每个问题提供：\n" +
                "\n" +
                "**1. 深度根本原因分析**\n" +
                "- 通过具体的代码证据准确查明问题来源\n" +
                "- 分析技术原因、触发条件和影响范围\n" +
                "- 确定问题是全局性的还是局部性的\n" +
                "\n" +
                "**2.详细的修复实施计划**\n" +
                "- 提供具体的代码修改步骤和文件路径\n" +
                "- 解释技术实施细节和设计决策\n" +
                "- 包含必要的 CSS、JavaScript、HTML 和 Java 代码更改\n" +
                "- 提供前后对比说明\n" +
                "\n" +
                "**3. 修复影响评估报告**\n" +
                "- 列出所有修改的文件和修改细节\n" +
                "- 解释修复对其他系统组件和依赖项的影响\n" +
                "- 确认与先前修复结果的兼容性\n" +
                "\n" +
                "## 执行工作流程\n" +
                "\n" +
                "1. **全面代码调查阶段**：使用代码检索工具深入分析与问题相关的所有代码文件和组件\n" +
                "2. **根本原因识别阶段**：准确识别每个问题的技术根本原因、触发机制和影响范围\n" +
                "3. **修复策略设计阶段**：制定修复策略，在保持系统完整性和一致性的同时解决问题\n" +
                "4. **精准代码实施阶段**：\n" +
                "- 修复完成后无需进行编译、运行或测试，但需要提供清晰的验证指导\n" +
                "\n" +
                "## 执行流程\n" +
                "\n" +
                "1. **全面代码调查阶段**：使用代码检索工具深入分析与问题相关的所有代码文件和组件\n" +
                "2. **根本原因识别阶段**：准确识别每个问题的技术根本原因、触发机制和影响范围\n" +
                "3. **修复策略设计阶段**：制定修复策略，在保持系统完整性和一致性的同时解决问题\n" +
                "4. **精准代码实施阶段**：根据设计方案执行精准的代码修改，确保质量和稳定性\n" +
                "\n" +
                "## 特殊注意事项\n" +
                "- 本轮修复基于前面的修复 - 确保兼容性和一致性\n" +
                "- 模态框相关问题可能与之前的模态框修复工作相关 - 需要仔细分析\n" +
                "- 内容区域布局问题需要考虑响应式设计和用户体验\n" +
                "- 修复完成后，无需编译、运行或测试是必需的，但必须提供清晰的验证指南\n" +
                "- 所有修复必须使用 str-replace-editor 工具实施，而非创建新文件\n" +
                "- 在进行任何编辑之前，请使用 codebase-retrieval 收集待修改代码的详细信息\n" +
                "- 专注于问题修复，不要增加多余的日志打印代码\n" +
                "\n" +
                "\n" +
                "\n" +
                "\n" +
                "\n" +
                "\n" +
                "\n" +
                "\n" +
                "\n" +
                "\n" +
                "\n" +
                "作为一名资深全栈开发人员，请详细分析的 Spring Boot + Thymeleaf 项目代码库，系统地优化以下功能需求。请严格按照企业级开发标准，确保与现有代码库和之前修复工作的完全兼容性。\n" +
                "\n" +
                "## 待优化需求\n" +
                "\n" +
                "\n" +
                "### 1. 平台知识库页面添加知识页面的文件上传，是否是将文件传输给后端，还是读取文件后将内容传输给后端\n" +
                "  - 请排查当前前后端交互的文件传输方式\n" +
                "  - 如果是传输文件，那么无需修改，如果不是请改为文件传输\n" +
                "\n" +
                "### 2. 供应知识库页面添加知识页面的文件上传，是否是将文件传输给后端，还是读取文件后将内容传输给后端\n" +
                "  - 请排查当前前后端交互的文件传输方式\n" +
                "  - 如果是传输文件，那么无需修改，如果不是请改为文件传输\n" +
                "\n" +
                "### 3. 平台知识库页面点击查看所有文档，文本卡片没有滚动条\n" +
                "  - 请在文本卡片中设置固定高度，然后添加滚动条\n" +
                "\n" +
                "### 4. 供应知识库页面点击查看所有文档，文本卡片没有滚动条\n" +
                "  - 请在文本卡片中设置固定高度，然后添加滚动条\n" +
                "\n" +
                "### 5. CodePrompt页面的CodePrompt文本卡片，也没有滚动条\n" +
                "  - 请在文本卡片中设置固定高度，然后添加滚动条\n" +
                "\n" +
                "\n" +
                "请你先详细了解代码，然后在前端和后端进行代码改动，改动不可以影响以前的任何逻辑，尤其是文本卡片的展示逻辑\n" +
                "\n" +
                "\n" +
                "## 技术要求和约束\n" +
                "\n" +
                "### 1. 问题分析方法\n" +
                "- 使用代码库检索工具深入分析每个问题的根本原因\n" +
                "- 识别相关的 HTML 模板、CSS 样式、JavaScript 事件处理程序和后端控制器\n" +
                "- 确定问题是系统性问题（需要全局修复）还是局部问题（需要有针对性的修复）\n" +
                "- 检查与之前的修复迭代是否存在冲突或依赖关系\n" +
                "\n" +
                "### 2. 影响评估标准\n" +
                "- 评估潜在影响每次修复对现有功能的影响\n" +
                "- 识别可能受影响的页面、组件和用户工作流程\n" +
                "- 确保修复不会破坏之前已完成的修复工作\n" +
                "- 记录任何新的依赖关系或级联效应\n" +
                "\n" +
                "### 3. 代码质量要求\n" +
                "- **一致性**：确保修复后的代码与现有项目和之前的修复保持一致\n" +
                "- **可维护性**：编写清晰、带注释的代码，方便将来的维护和扩展\n" +
                "- **可扩展性**：设计修复时考虑未来的功能扩展需求\n" +
                "- **稳定性**：确保修复后的功能稳定可靠，无回归问题\n" +
                "- **性能**：确保修复不会对页面加载时间或交互性能产生负面影响\n" +
                "\n" +
                "### 4. 修复范围控制原则\n" +
                "- 专注于解决明确定义的问题；避免过度设计或功能蔓延\n" +
                "- 维护现有功能行为，尤其是之前修复的功能\n" +
                "- 遵循最小变更原则 - 仅修改必要代码\n" +
                "- 避免与当前问题无关的代码重构或优化\n" +
                "\n" +
                "### 5. 技术实施标准\n" +
                "- 严格遵循 Spring Boot 和 Thymeleaf 的最佳实践和项目约定\n" +
                "- 使用现有的 CSS 框架、JavaScript 模式和组件结构\n" +
                "- 实施适当的错误处理、用户反馈机制和边缘情况处理\n" +
                "- 遵循响应式设计原则，确保移动兼容性\n" +
                "\n" +
                "## 交付成果要求\n" +
                "\n" +
                "### 针对每个问题提供：\n" +
                "\n" +
                "**1. 深度根本原因分析**\n" +
                "- 通过具体的代码证据准确查明问题来源\n" +
                "- 分析技术原因、触发条件和影响范围\n" +
                "- 确定问题是全局性的还是局部性的\n" +
                "\n" +
                "**2.详细的修复实施计划**\n" +
                "- 提供具体的代码修改步骤和文件路径\n" +
                "- 解释技术实施细节和设计决策\n" +
                "- 包含必要的 CSS、JavaScript、HTML 和 Java 代码更改\n" +
                "- 提供前后对比说明\n" +
                "\n" +
                "**3. 修复影响评估报告**\n" +
                "- 列出所有修改的文件和修改细节\n" +
                "- 解释修复对其他系统组件和依赖项的影响\n" +
                "- 确认与先前修复结果的兼容性\n" +
                "\n" +
                "## 执行工作流程\n" +
                "\n" +
                "1. **全面代码调查阶段**：使用代码检索工具深入分析与问题相关的所有代码文件和组件\n" +
                "2. **根本原因识别阶段**：准确识别每个问题的技术根本原因、触发机制和影响范围\n" +
                "3. **修复策略设计阶段**：制定修复策略，在保持系统完整性和一致性的同时解决问题\n" +
                "4. **精准代码实施阶段**：\n" +
                "- 修复完成后无需进行编译、运行或测试，但需要提供清晰的验证指导\n" +
                "\n" +
                "## 执行流程\n" +
                "\n" +
                "1. **全面代码调查阶段**：使用代码检索工具深入分析与问题相关的所有代码文件和组件\n" +
                "2. **根本原因识别阶段**：准确识别每个问题的技术根本原因、触发机制和影响范围\n" +
                "3. **修复策略设计阶段**：制定修复策略，在保持系统完整性和一致性的同时解决问题\n" +
                "4. **精准代码实施阶段**：根据设计方案执行精准的代码修改，确保质量和稳定性\n" +
                "\n" +
                "## 特殊注意事项\n" +
                "- 本轮修复基于前面的修复 - 确保兼容性和一致性\n" +
                "- 模态框相关问题可能与之前的模态框修复工作相关 - 需要仔细分析\n" +
                "- 内容区域布局问题需要考虑响应式设计和用户体验\n" +
                "- 修复完成后，无需编译、运行或测试是必需的，但必须提供清晰的验证指南\n" +
                "- 所有修复必须使用 str-replace-editor 工具实施，而非创建新文件\n" +
                "- 在进行任何编辑之前，请使用 codebase-retrieval 收集待修改代码的详细信息\n" +
                "- 专注于问题修复，不要增加多余的日志打印代码\n");
        prompt8.setRespBeanPrompt("生成支付响应Bean，包含支付状态、交易号等信息。");
        prompt8.setCodeGenPrompt("生成微信支付适配器代码，处理支付流程、回调处理和状态同步。");
        prompt8.setVersion("v1.0");
        prompt8.setOperator("郑十");
        prompt8.setCreateTime(LocalDateTime.now().minusDays(3).format(DATE_FORMAT));
        prompt8.setUpdateTime(LocalDateTime.now().minusDays(1).format(DATE_FORMAT));
        promptVersions.put(prompt8.getId(), prompt8);

        // 多版本示例 - 同一接口的不同版本
        PromptVersionResponse prompt9 = new PromptVersionResponse();
        prompt9.setId(nextId++);
        prompt9.setPlatformInterfaceId(1L);
        prompt9.setSupplierInterfaceId(1L);
        prompt9.setReqBeanPrompt("东航航班搜索v2.0请求Bean，支持更多高级搜索功能。");
        prompt9.setRespBeanPrompt("东航航班搜索v2.0响应Bean，增加了实时座位图和价格趋势。");
        prompt9.setCodeGenPrompt("东航航班搜索v2.0适配器，集成了AI推荐和动态定价。");
        prompt9.setVersion("v2.0");
        prompt9.setOperator("刘十一");
        prompt9.setCreateTime(LocalDateTime.now().minusDays(2).format(DATE_FORMAT));
        prompt9.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        promptVersions.put(prompt9.getId(), prompt9);

        PromptVersionResponse prompt10 = new PromptVersionResponse();
        prompt10.setId(nextId++);
        prompt10.setPlatformInterfaceId(1L);
        prompt10.setSupplierInterfaceId(1L);
        prompt10.setReqBeanPrompt("东航航班搜索v2.1请求Bean，修复了v2.0的日期处理问题。");
        prompt10.setRespBeanPrompt("东航航班搜索v2.1响应Bean，优化了数据结构。");
        prompt10.setCodeGenPrompt("东航航班搜索v2.1适配器，性能优化和错误处理改进。");
        prompt10.setVersion("v2.1");
        prompt10.setOperator("陈十二");
        prompt10.setCreateTime(LocalDateTime.now().minusDays(1).format(DATE_FORMAT));
        prompt10.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        promptVersions.put(prompt10.getId(), prompt10);

        // 添加更多测试数据以覆盖更多接口组合

        // 支付业务域 - 支付宝支付接口
        PromptVersionResponse prompt11 = new PromptVersionResponse();
        prompt11.setId(nextId++);
        prompt11.setPlatformInterfaceId(9L); // 支付接口
        prompt11.setSupplierInterfaceId(8L); // 支付宝接口
        prompt11.setReqBeanPrompt("根据支付宝支付接口文档生成Java Bean类：\n\n```\n接口：支付宝支付\n参数：\n- orderId: 订单号（必填）\n- amount: 支付金额（必填）\n- returnUrl: 返回地址（必填）\n- notifyUrl: 通知地址（必填）\n```\n\n请生成对应的请求Bean类，包含支付宝特定的字段验证。");
        prompt11.setRespBeanPrompt("根据支付宝支付响应结构生成Java Bean类：\n\n```json\n{\n  \"code\": \"10000\",\n  \"msg\": \"Success\",\n  \"data\": {\n    \"tradeNo\": \"2024073022001234567890123\",\n    \"outTradeNo\": \"ORD20240730001\",\n    \"payUrl\": \"https://mapi.alipay.com/gateway.do?...\"\n  }\n}\n```\n\n请生成对应的响应Bean类，处理支付宝的返回格式。");
        prompt11.setCodeGenPrompt("基于以上文档和Bean定义，生成完整的支付宝支付适配器代码，要求：\n\n1. 使用Spring Boot注解\n2. 包含支付宝SDK集成\n3. 异常处理和重试机制\n4. 支付状态回调处理\n5. 安全签名验证\n6. 日志记录和监控\n\n示例适配器应包含完整的支付流程处理。");
        prompt11.setVersion("v1.0");
        prompt11.setOperator("张支付");
        prompt11.setCreateTime(LocalDateTime.now().minusDays(10).format(DATE_FORMAT));
        prompt11.setUpdateTime(LocalDateTime.now().minusDays(7).format(DATE_FORMAT));
        promptVersions.put(prompt11.getId(), prompt11);

        // 支付业务域 - 支付宝支付接口 v1.1
        PromptVersionResponse prompt12 = new PromptVersionResponse();
        prompt12.setId(nextId++);
        prompt12.setPlatformInterfaceId(9L); // 支付接口
        prompt12.setSupplierInterfaceId(8L); // 支付宝接口
        prompt12.setReqBeanPrompt("支付宝支付接口v1.1请求Bean，新增分期付款和优惠券支持。");
        prompt12.setRespBeanPrompt("支付宝支付接口v1.1响应Bean，增加了分期信息和优惠详情。");
        prompt12.setCodeGenPrompt("支付宝支付v1.1适配器代码，支持分期付款、优惠券抵扣和实时风控。");
        prompt12.setVersion("v1.1");
        prompt12.setOperator("李支付");
        prompt12.setCreateTime(LocalDateTime.now().minusDays(5).format(DATE_FORMAT));
        prompt12.setUpdateTime(LocalDateTime.now().minusDays(3).format(DATE_FORMAT));
        promptVersions.put(prompt12.getId(), prompt12);

        // 用户管理业务域 - 权限接口与内部权限服务
        PromptVersionResponse prompt13 = new PromptVersionResponse();
        prompt13.setId(nextId++);
        prompt13.setPlatformInterfaceId(13L); // 用户权限接口
        prompt13.setSupplierInterfaceId(9L); // 内部权限服务
        prompt13.setReqBeanPrompt("根据用户权限查询接口文档生成Java Bean类：\n\n```\n接口：用户权限查询\n参数：\n- userId: 用户ID（必填）\n- resourceType: 资源类型（必填）\n- action: 操作动作（必填）\n```\n\n请生成对应的请求Bean类，包含权限验证逻辑。");
        prompt13.setRespBeanPrompt("生成用户权限查询响应Bean，包含权限列表、角色信息和访问级别。");
        prompt13.setCodeGenPrompt("生成用户权限查询适配器代码，处理权限校验、角色继承和访问控制。");
        prompt13.setVersion("v1.0");
        prompt13.setOperator("王权限");
        prompt13.setCreateTime(LocalDateTime.now().minusDays(12).format(DATE_FORMAT));
        prompt13.setUpdateTime(LocalDateTime.now().minusDays(9).format(DATE_FORMAT));
        promptVersions.put(prompt13.getId(), prompt13);

        // 酒店业务域 - 预订接口与携程预订服务
        PromptVersionResponse prompt14 = new PromptVersionResponse();
        prompt14.setId(nextId++);
        prompt14.setPlatformInterfaceId(8L); // 酒店预订接口
        prompt14.setSupplierInterfaceId(10L); // 携程预订服务
        prompt14.setReqBeanPrompt("根据酒店预订接口文档生成Java Bean类，包含房间选择、入住信息和特殊要求。");
        prompt14.setRespBeanPrompt("生成酒店预订响应Bean，包含预订确认号、房间信息和取消政策。");
        prompt14.setCodeGenPrompt("生成酒店预订适配器代码，处理房态查询、价格计算和预订确认流程。");
        prompt14.setVersion("v1.0");
        prompt14.setOperator("赵酒店");
        prompt14.setCreateTime(LocalDateTime.now().minusDays(8).format(DATE_FORMAT));
        prompt14.setUpdateTime(LocalDateTime.now().minusDays(6).format(DATE_FORMAT));
        promptVersions.put(prompt14.getId(), prompt14);

        // 火车票业务域 - 预订接口与12306服务
        PromptVersionResponse prompt15 = new PromptVersionResponse();
        prompt15.setId(nextId++);
        prompt15.setPlatformInterfaceId(7L); // 火车票预订接口
        prompt15.setSupplierInterfaceId(11L); // 12306预订服务
        prompt15.setReqBeanPrompt("根据火车票预订接口文档生成Java Bean类：\n\n```\n接口：火车票预订\n参数：\n- trainNo: 车次号（必填）\n- seatType: 座位类型（必填）\n- passengers: 乘客信息列表（必填）\n- contactInfo: 联系人信息（必填）\n```\n\n请生成对应的请求Bean类，包含12306特有的验证规则。");
        prompt15.setRespBeanPrompt("生成火车票预订响应Bean，包含订单号、座位信息和取票码。");
        prompt15.setCodeGenPrompt("生成火车票预订适配器代码，处理实名制验证、座位分配和订单管理。");
        prompt15.setVersion("v1.0");
        prompt15.setOperator("孙火车");
        prompt15.setCreateTime(LocalDateTime.now().minusDays(6).format(DATE_FORMAT));
        prompt15.setUpdateTime(LocalDateTime.now().minusDays(4).format(DATE_FORMAT));
        promptVersions.put(prompt15.getId(), prompt15);

        // 支付业务域 - 退款接口与微信退款服务
        PromptVersionResponse prompt16 = new PromptVersionResponse();
        prompt16.setId(nextId++);
        prompt16.setPlatformInterfaceId(10L); // 退款接口
        prompt16.setSupplierInterfaceId(12L); // 微信退款服务
        prompt16.setReqBeanPrompt("根据微信退款接口文档生成Java Bean类，包含原支付信息、退款金额和退款原因。");
        prompt16.setRespBeanPrompt("生成微信退款响应Bean，包含退款单号、退款状态和预计到账时间。");
        prompt16.setCodeGenPrompt("生成微信退款适配器代码，处理退款申请、状态查询和异步通知。");
        prompt16.setVersion("v1.0");
        prompt16.setOperator("周退款");
        prompt16.setCreateTime(LocalDateTime.now().minusDays(4).format(DATE_FORMAT));
        prompt16.setUpdateTime(LocalDateTime.now().minusDays(2).format(DATE_FORMAT));
        promptVersions.put(prompt16.getId(), prompt16);

        // 机票业务域 - 取消接口与南航取消服务
        PromptVersionResponse prompt17 = new PromptVersionResponse();
        prompt17.setId(nextId++);
        prompt17.setPlatformInterfaceId(12L); // 机票取消接口
        prompt17.setSupplierInterfaceId(13L); // 南航取消服务
        prompt17.setReqBeanPrompt("根据南航取消接口文档生成Java Bean类，包含订单信息、取消原因和退票类型。");
        prompt17.setRespBeanPrompt("生成南航取消响应Bean，包含取消确认号、退款金额和手续费明细。");
        prompt17.setCodeGenPrompt("生成南航取消适配器代码，处理取消规则、费用计算和退款流程。");
        prompt17.setVersion("v1.0");
        prompt17.setOperator("吴取消");
        prompt17.setCreateTime(LocalDateTime.now().minusDays(3).format(DATE_FORMAT));
        prompt17.setUpdateTime(LocalDateTime.now().minusDays(1).format(DATE_FORMAT));
        promptVersions.put(prompt17.getId(), prompt17);

        // 酒店业务域 - 评价接口与美团评价服务
        PromptVersionResponse prompt18 = new PromptVersionResponse();
        prompt18.setId(nextId++);
        prompt18.setPlatformInterfaceId(14L); // 酒店评价接口
        prompt18.setSupplierInterfaceId(14L); // 美团评价服务
        prompt18.setReqBeanPrompt("根据美团酒店评价接口文档生成Java Bean类，包含订单ID、评分详情和评价内容。");
        prompt18.setRespBeanPrompt("生成美团评价响应Bean，包含评价ID、审核状态和积分奖励信息。");
        prompt18.setCodeGenPrompt("生成美团评价适配器代码，处理评价提交、内容审核和积分计算。");
        prompt18.setVersion("v1.0");
        prompt18.setOperator("郑评价");
        prompt18.setCreateTime(LocalDateTime.now().minusDays(2).format(DATE_FORMAT));
        prompt18.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        promptVersions.put(prompt18.getId(), prompt18);

        // 机票业务域 - 改签接口与国航改签服务
        PromptVersionResponse prompt19 = new PromptVersionResponse();
        prompt19.setId(nextId++);
        prompt19.setPlatformInterfaceId(15L); // 机票改签接口
        prompt19.setSupplierInterfaceId(15L); // 国航改签服务
        prompt19.setReqBeanPrompt("根据国航改签接口文档生成Java Bean类，包含原订单信息、新航班选择和改签原因。");
        prompt19.setRespBeanPrompt("生成国航改签响应Bean，包含改签确认号、新票号和费用明细。");
        prompt19.setCodeGenPrompt("生成国航改签适配器代码，处理改签规则、航班可用性查询和费用计算。");
        prompt19.setVersion("v1.0");
        prompt19.setOperator("王改签");
        prompt19.setCreateTime(LocalDateTime.now().minusDays(1).format(DATE_FORMAT));
        prompt19.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        promptVersions.put(prompt19.getId(), prompt19);

        // 添加一些多版本的示例
        PromptVersionResponse prompt20 = new PromptVersionResponse();
        prompt20.setId(nextId++);
        prompt20.setPlatformInterfaceId(9L); // 支付接口
        prompt20.setSupplierInterfaceId(8L); // 支付宝接口
        prompt20.setReqBeanPrompt("支付宝支付接口v2.0请求Bean，支持数字人民币和国际支付。");
        prompt20.setRespBeanPrompt("支付宝支付接口v2.0响应Bean，增加了汇率信息和合规报告。");
        prompt20.setCodeGenPrompt("支付宝支付v2.0适配器代码，集成央行数字货币和跨境支付功能。");
        prompt20.setVersion("v2.0");
        prompt20.setOperator("数字支付");
        prompt20.setCreateTime(LocalDateTime.now().format(DATE_FORMAT));
        prompt20.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        promptVersions.put(prompt20.getId(), prompt20);
    }

    /**
     * 4.1 查询Prompt所有版本
     * GET /api/prompt-version/versionList?platformInterfaceId=1&supplierInterfaceId=1
     */
    @GetMapping("/versionList")
    public ApiResponse<List<String>> getVersionList(
            @RequestParam Long platformInterfaceId,
            @RequestParam Long supplierInterfaceId) {
        
        try {
            // 查找匹配的版本
            List<String> versions = promptVersions.values().stream()
                    .filter(p -> p.getPlatformInterfaceId().equals(platformInterfaceId) && 
                               p.getSupplierInterfaceId().equals(supplierInterfaceId))
                    .map(PromptVersionResponse::getVersion)
                    .sorted((a, b) -> {
                        // 自定义版本号排序：v2.1 > v2.0 > v1.1 > v1.0
                        try {
                            String[] aParts = a.replace("v", "").split("\\.");
                            String[] bParts = b.replace("v", "").split("\\.");
                            
                            int majorA = Integer.parseInt(aParts[0]);
                            int majorB = Integer.parseInt(bParts[0]);
                            
                            if (majorA != majorB) {
                                return Integer.compare(majorB, majorA); // 降序
                            }
                            
                            int minorA = aParts.length > 1 ? Integer.parseInt(aParts[1]) : 0;
                            int minorB = bParts.length > 1 ? Integer.parseInt(bParts[1]) : 0;
                            
                            return Integer.compare(minorB, minorA); // 降序
                        } catch (Exception e) {
                            return b.compareTo(a); // 字符串降序作为fallback
                        }
                    })
                    .toList();
            
            return ApiResponse.success(versions);
        } catch (Exception e) {
            return ApiResponse.error("查询Prompt版本列表失败：" + e.getMessage());
        }
    }

    /**
     * 4.2 根据版本号查询Prompt
     * GET /api/prompt-version/{version}?platformInterfaceId=1&supplierInterfaceId=1
     */
    @GetMapping("/{version}")
    public ApiResponse<PromptVersionResponse> getByVersion(
            @PathVariable String version,
            @RequestParam Long platformInterfaceId,
            @RequestParam Long supplierInterfaceId) {
        
        try {
            PromptVersionResponse prompt = promptVersions.values().stream()
                    .filter(p -> p.getPlatformInterfaceId().equals(platformInterfaceId) && 
                               p.getSupplierInterfaceId().equals(supplierInterfaceId) &&
                               p.getVersion().equals(version))
                    .findFirst()
                    .orElse(null);
            
            if (prompt == null) {
                return ApiResponse.error("未找到指定版本的Prompt");
            }
            
            return ApiResponse.success(prompt);
        } catch (Exception e) {
            return ApiResponse.error("查询Prompt失败：" + e.getMessage());
        }
    }

    /**
     * 4.3 创建新Prompt版本
     * POST /api/prompt-version?platformInterfaceId=1&supplierInterfaceId=1
     */
    @PostMapping
    public ApiResponse<PromptVersionResponse> create(
            @RequestParam Long platformInterfaceId,
            @RequestParam Long supplierInterfaceId) {
        
        try {
            // 查找现有版本以生成新版本号
            List<String> existingVersions = promptVersions.values().stream()
                    .filter(p -> p.getPlatformInterfaceId().equals(platformInterfaceId) && 
                               p.getSupplierInterfaceId().equals(supplierInterfaceId))
                    .map(PromptVersionResponse::getVersion)
                    .toList();
            
            // 生成新版本号
            String newVersion = generateNextVersion(existingVersions);
            
            // 创建新Prompt版本
            PromptVersionResponse newPrompt = new PromptVersionResponse();
            newPrompt.setId(nextId++);
            newPrompt.setPlatformInterfaceId(platformInterfaceId);
            newPrompt.setSupplierInterfaceId(supplierInterfaceId);
            newPrompt.setReqBeanPrompt("请根据接口文档生成请求Bean类，包含必要的字段验证和文档注释。");
            newPrompt.setRespBeanPrompt("请根据响应结构生成响应Bean类，处理嵌套对象和集合。");
            newPrompt.setCodeGenPrompt("请生成完整的适配器代码，包含错误处理、日志记录和性能优化。");
            newPrompt.setVersion(newVersion);
            newPrompt.setOperator("系统");
            newPrompt.setCreateTime(LocalDateTime.now().format(DATE_FORMAT));
            newPrompt.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
            
            promptVersions.put(newPrompt.getId(), newPrompt);
            
            return ApiResponse.success(newPrompt);
        } catch (Exception e) {
            return ApiResponse.error("创建Prompt版本失败：" + e.getMessage());
        }
    }

    /**
     * 4.4 根据ID查询Prompt版本
     * GET /api/prompt-version/id/{id}
     */
    @GetMapping("/id/{id}")
    public ApiResponse<PromptVersionResponse> getById(@PathVariable Long id) {
        try {
            PromptVersionResponse prompt = promptVersions.get(id);
            if (prompt == null) {
                return ApiResponse.error("Prompt版本不存在");
            }
            return ApiResponse.success(prompt);
        } catch (Exception e) {
            return ApiResponse.error("查询Prompt版本失败：" + e.getMessage());
        }
    }

    /**
     * 4.5 更新Prompt版本
     * PUT /api/prompt-version/{id}
     */
    @PutMapping("/{id}")
    public ApiResponse<PromptVersionResponse> update(
            @PathVariable Long id, 
            @RequestBody PromptVersionUpdateRequest request) {
        
        try {
            PromptVersionResponse prompt = promptVersions.get(id);
            if (prompt == null) {
                return ApiResponse.error("Prompt版本不存在");
            }
            
            // 更新字段
            if (request.getReqBeanPrompt() != null) {
                prompt.setReqBeanPrompt(request.getReqBeanPrompt());
            }
            if (request.getRespBeanPrompt() != null) {
                prompt.setRespBeanPrompt(request.getRespBeanPrompt());
            }
            if (request.getCodeGenPrompt() != null) {
                prompt.setCodeGenPrompt(request.getCodeGenPrompt());
            }
            if (request.getOperator() != null) {
                prompt.setOperator(request.getOperator());
            }
            
            prompt.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
            
            return ApiResponse.success(prompt);
        } catch (Exception e) {
            return ApiResponse.error("更新Prompt版本失败：" + e.getMessage());
        }
    }

    /**
     * 生成下一个版本号
     */
    private String generateNextVersion(List<String> existingVersions) {
        if (existingVersions.isEmpty()) {
            return "v1.0";
        }
        
        // 找到最大版本号
        String maxVersion = existingVersions.stream()
                .max((a, b) -> {
                    try {
                        String[] aParts = a.replace("v", "").split("\\.");
                        String[] bParts = b.replace("v", "").split("\\.");
                        
                        int majorA = Integer.parseInt(aParts[0]);
                        int majorB = Integer.parseInt(bParts[0]);
                        
                        if (majorA != majorB) {
                            return Integer.compare(majorA, majorB);
                        }
                        
                        int minorA = aParts.length > 1 ? Integer.parseInt(aParts[1]) : 0;
                        int minorB = bParts.length > 1 ? Integer.parseInt(bParts[1]) : 0;
                        
                        return Integer.compare(minorA, minorB);
                    } catch (Exception e) {
                        return a.compareTo(b);
                    }
                })
                .orElse("v1.0");
        
        // 递增次版本号
        try {
            String[] parts = maxVersion.replace("v", "").split("\\.");
            int major = Integer.parseInt(parts[0]);
            int minor = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
            
            return String.format("v%d.%d", major, minor + 1);
        } catch (Exception e) {
            return "v1.0";
        }
    }

    // ===== 内部DTO类定义 =====

    /**
     * Prompt版本响应对象
     */
    public static class PromptVersionResponse {
        private Long id;
        private Long platformInterfaceId;
        private Long supplierInterfaceId;
        private String reqBeanPrompt;
        private String respBeanPrompt;
        private String codeGenPrompt;
        private String version;
        private String operator;
        private String createTime;
        private String updateTime;

        // 构造函数
        public PromptVersionResponse() {}

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public Long getPlatformInterfaceId() { return platformInterfaceId; }
        public void setPlatformInterfaceId(Long platformInterfaceId) { this.platformInterfaceId = platformInterfaceId; }

        public Long getSupplierInterfaceId() { return supplierInterfaceId; }
        public void setSupplierInterfaceId(Long supplierInterfaceId) { this.supplierInterfaceId = supplierInterfaceId; }

        public String getReqBeanPrompt() { return reqBeanPrompt; }
        public void setReqBeanPrompt(String reqBeanPrompt) { this.reqBeanPrompt = reqBeanPrompt; }

        public String getRespBeanPrompt() { return respBeanPrompt; }
        public void setRespBeanPrompt(String respBeanPrompt) { this.respBeanPrompt = respBeanPrompt; }

        public String getCodeGenPrompt() { return codeGenPrompt; }
        public void setCodeGenPrompt(String codeGenPrompt) { this.codeGenPrompt = codeGenPrompt; }

        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }

        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }

        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }

        public String getUpdateTime() { return updateTime; }
        public void setUpdateTime(String updateTime) { this.updateTime = updateTime; }
    }

    /**
     * Prompt版本更新请求对象
     */
    public static class PromptVersionUpdateRequest {
        private String reqBeanPrompt;
        private String respBeanPrompt;
        private String codeGenPrompt;
        private String operator;

        // Getters and Setters
        public String getReqBeanPrompt() { return reqBeanPrompt; }
        public void setReqBeanPrompt(String reqBeanPrompt) { this.reqBeanPrompt = reqBeanPrompt; }

        public String getRespBeanPrompt() { return respBeanPrompt; }
        public void setRespBeanPrompt(String respBeanPrompt) { this.respBeanPrompt = respBeanPrompt; }

        public String getCodeGenPrompt() { return codeGenPrompt; }
        public void setCodeGenPrompt(String codeGenPrompt) { this.codeGenPrompt = codeGenPrompt; }

        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }
    }
}