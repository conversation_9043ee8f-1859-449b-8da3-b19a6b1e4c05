<!-- Thymeleaf Component Fragments -->
<!-- This file contains reusable Thymeleaf fragments and should not have DOCTYPE or html tags -->
<html xmlns:th="http://www.thymeleaf.org">

<!-- Button component -->
<button th:fragment="button(text, type, size, icon, onclick, disabled, loading)" 
        th:class="'btn btn-' + ${type ?: 'primary'} + (${size != null} ? ' btn-' + ${size} : '') + (${loading} ? ' loading' : '')"
        th:onclick="${onclick}"
        th:disabled="${disabled or loading}"
        type="button">
    <svg th:if="${icon != null}" class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" th:switch="${icon}">
        <path th:case="'plus'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        <path th:case="'search'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        <path th:case="'edit'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        <path th:case="'delete'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        <path th:case="'view'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        <path th:case="'save'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3-3-3m3-3v12"></path>
        <path th:case="'cancel'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        <path th:case="'upload'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
    </svg>
    <span th:text="${text}">按钮文本</span>
</button>

<!-- Form input component -->
<div th:fragment="form-input(id, name, label, type, value, placeholder, required, readonly, maxlength)" class="form-group">
    <label th:if="${label != null}" th:for="${id}" th:class="'form-label' + (${required} ? ' required' : '')" th:text="${label}">标签</label>
    <input th:id="${id}" 
           th:name="${name}" 
           th:type="${type ?: 'text'}"
           th:value="${value}"
           th:placeholder="${placeholder}"
           th:required="${required}"
           th:readonly="${readonly}"
           th:maxlength="${maxlength}"
           class="form-control">
    <div class="invalid-feedback"></div>
</div>

<!-- Form select component -->
<div th:fragment="form-select(id, name, label, options, value, placeholder, required)" class="form-group">
    <label th:if="${label != null}" th:for="${id}" th:class="'form-label' + (${required} ? ' required' : '')" th:text="${label}">标签</label>
    <select th:id="${id}" th:name="${name}" th:required="${required}" class="form-control form-select">
        <option value="" th:if="${placeholder != null}" th:text="${placeholder}">请选择</option>
        <option th:each="option : ${options}" 
                th:value="${option.value}" 
                th:text="${option.text}"
                th:selected="${option.value == value}">选项</option>
    </select>
    <div class="invalid-feedback"></div>
</div>

<!-- Form textarea component -->
<div th:fragment="form-textarea(id, name, label, value, placeholder, required, readonly, rows, maxlength)" class="form-group">
    <label th:if="${label != null}" th:for="${id}" th:class="'form-label' + (${required} ? ' required' : '')" th:text="${label}">标签</label>
    <textarea th:id="${id}" 
              th:name="${name}" 
              th:placeholder="${placeholder}"
              th:required="${required}"
              th:readonly="${readonly}"
              th:rows="${rows ?: 4}"
              th:maxlength="${maxlength}"
              class="form-control textarea" th:text="${value}"></textarea>
    <div class="invalid-feedback"></div>
    <div th:if="${maxlength != null}" class="text-xs text-secondary mt-1">
        <span class="char-count">0</span>/<span th:text="${maxlength}">500</span> 字符
    </div>
</div>

<!-- File upload component -->
<div th:fragment="file-upload(id, name, label, accept, required)" class="form-group">
    <label th:if="${label != null}" th:for="${id}" th:class="'form-label' + (${required} ? ' required' : '')" th:text="${label}">文件上传</label>
    <div class="form-file">
        <input th:id="${id}" 
               th:name="${name}" 
               type="file" 
               th:accept="${accept}"
               th:required="${required}"
               class="form-file-input">
        <label th:for="${id}" class="form-file-label">
            <span class="file-placeholder">选择文件...</span>
        </label>
    </div>
    <div class="invalid-feedback"></div>
</div>

<!-- Data table component -->
<div th:fragment="data-table(id, columns, data, actions)" class="data-table-container">
    <table th:id="${id}" class="data-table">
        <thead>
            <tr>
                <th th:each="column : ${columns}" th:text="${column.title}">列标题</th>
                <th th:if="${actions != null and !actions.isEmpty()}">操作</th>
            </tr>
        </thead>
        <tbody>
            <tr th:each="row : ${data}">
                <td th:each="column : ${columns}">
                    <span th:switch="${column.type}">
                        <span th:case="'text'" th:text="${row[column.field]}">文本</span>
                        <span th:case="'date'" th:text="${#temporals.format(row[column.field], 'yyyy-MM-dd HH:mm')}">日期</span>
                        <a th:case="'link'" th:href="${column.linkUrl != null ? column.linkUrl.replace('{id}', row.id) : '#'}" 
                           th:text="${row[column.field]}">链接</a>
                    </span>
                </td>
                <td th:if="${actions != null and !actions.isEmpty()}" class="action-buttons">
                    <button th:each="action : ${actions}"
                            th:class="'btn btn-' + ${action.type} + ' btn-sm'"
                            th:onclick="${action.onclick != null ? action.onclick.replace('{id}', row.id) : ''}"
                            th:text="${action.text}">操作</button>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Pagination component -->
<div th:fragment="pagination(pageResult, baseUrl)" class="pagination" th:if="${pageResult != null}">
    <div class="pagination-info">
        显示第 <span th:text="${pageResult.page * pageResult.size + 1}">1</span> 
        到 <span th:text="${T(java.lang.Math).min((pageResult.page + 1) * pageResult.size, pageResult.totalElements)}">10</span> 
        条，共 <span th:text="${pageResult.totalElements}">100</span> 条记录
    </div>
    
    <div class="pagination-controls">
        <!-- Previous page -->
        <a th:if="${pageResult.hasPrevious}" 
           th:href="${baseUrl + '?page=' + (pageResult.page - 1) + '&size=' + pageResult.size}"
           class="pagination-btn">上一页</a>
        <span th:unless="${pageResult.hasPrevious}" class="pagination-btn disabled">上一页</span>
        
        <!-- Page numbers -->
        <span th:each="i : ${#numbers.sequence(T(java.lang.Math).max(0, pageResult.page - 2), T(java.lang.Math).min(pageResult.totalPages - 1, pageResult.page + 2))}">
            <a th:if="${i != pageResult.page}" 
               th:href="${baseUrl + '?page=' + i + '&size=' + pageResult.size}"
               th:text="${i + 1}"
               class="pagination-btn">1</a>
            <span th:if="${i == pageResult.page}" 
                  th:text="${i + 1}"
                  class="pagination-btn active">1</span>
        </span>
        
        <!-- Next page -->
        <a th:if="${pageResult.hasNext}" 
           th:href="${baseUrl + '?page=' + (pageResult.page + 1) + '&size=' + pageResult.size}"
           class="pagination-btn">下一页</a>
        <span th:unless="${pageResult.hasNext}" class="pagination-btn disabled">下一页</span>
    </div>
</div>

<!-- Modal component -->
<div th:fragment="modal(id, title, size, content)" th:id="${id}" class="modal" th:classappend="${size != null ? 'modal-' + size : ''}">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" th:text="${title}">模态框标题</h3>
                <button type="button" class="modal-close" data-action="hide-modal" th:data-target="${id}">
                    &times;
                </button>
            </div>
            <div class="modal-body">
                <div th:replace="${content}"></div>
            </div>
        </div>
    </div>
</div>

<!-- Alert component -->
<div th:fragment="alert(type, message, dismissible)" th:class="'alert alert-' + ${type} + (${dismissible} ? ' alert-dismissible' : '')" role="alert">
    <span th:text="${message}">警告信息</span>
    <button th:if="${dismissible}" type="button" class="alert-close" onclick="this.parentElement.remove()">
        &times;
    </button>
</div>

<!-- Loading spinner component -->
<div th:fragment="loading-spinner(size)" th:class="'loading-spinner' + (${size != null} ? ' loading-spinner-' + ${size} : '')"></div>

<!-- Empty state component -->
<div th:fragment="empty-state(title, description, actionText, actionUrl)" class="empty-state">
    <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    <h3 class="empty-state-title" th:text="${title}">暂无数据</h3>
    <p class="empty-state-description" th:text="${description}">当前没有可显示的内容</p>
    <a th:if="${actionText != null and actionUrl != null}" 
       th:href="${actionUrl}" 
       th:text="${actionText}"
       class="btn btn-primary">添加数据</a>
</div>

<!-- Document card component -->
<div th:fragment="document-card(document, editable)" class="document-card">
    <div class="document-card-header">
        <span class="document-type" th:text="${document.fileType}">文档类型</span>
        <button th:if="${editable}" class="btn btn-sm btn-ghost" onclick="editDocument(this)">
            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
        </button>
    </div>
    <div class="document-card-body">
        <div class="document-content" th:text="${document.content}">文档内容</div>
    </div>
    <div class="document-actions" th:if="${editable}">
        <button class="btn btn-sm btn-primary" onclick="reuploadDocument(this)" th:data-id="${document.id}">
            重新上传
        </button>
    </div>
</div>

</html>
