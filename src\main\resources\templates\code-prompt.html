<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('CodePrompt 管理')}"></head>
<body>
    <div class="app-layout">
        <div th:replace="~{fragments/layout :: sidebar}"></div>
        <div class="app-main">
            <div th:replace="~{fragments/layout :: header}"></div>
            <main class="app-content">

                <!-- Page Header -->
                <div class="page-header">
                    <h1 class="page-title">CodePrompt 管理</h1>
                    <p class="page-subtitle">管理和编辑代码提示内容</p>
                </div>
                
                <!-- Search and Filter Section -->
                <div class="search-filters">
                    <form id="codePromptSearchForm" onsubmit="return false;">
                        <!-- Business Domain Section -->
                        <div class="filter-section">
<!--                            <div class="filter-section-title">业务领域</div>-->
                            <div class="filter-single">
                                <div th:replace="~{fragments/components :: form-select('businessDomain', 'businessDomain', '业务领域', ${businessDomains}, '', '请选择业务领域', false)}"></div>
                            </div>
                        </div>

                        <!-- Platform and Vendor Groups -->
                        <div class="filter-groups">
                            <!-- Platform Group -->
                            <div class="filter-group platform-group">
                                <div class="filter-group-title">平台端信息</div>
                                <div class="filter-group-content">
                                    <div th:replace="~{fragments/components :: form-select('platformInterface', 'platformInterface', '平台端接口', ${platformInterfaces}, '', '请选择平台端接口', false)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('platformVersion', 'platformVersion', '平台端版本', ${platformVersions}, '', '请选择平台端版本', false)}"></div>
                                </div>
                            </div>

                            <!-- Vendor Group -->
                            <div class="filter-group vendor-group">
                                <div class="filter-group-title">供应商端信息</div>
                                <div class="filter-group-content">
                                    <div th:replace="~{fragments/components :: form-select('vendor', 'vendor', '供应商', ${vendors}, '', '请选择供应商', false)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('vendorInterface', 'vendorInterface', '供应商接口', ${vendorInterfaces}, '', '请选择供应商端接口', false)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('vendorVersion', 'vendorVersion', '供应商版本', ${vendorVersions}, '', '请选择供应商端版本', false)}"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Prompt Version Section -->
                        <div class="filter-section">
<!--                            <div class="filter-section-title">提示版本</div>-->
                            <div class="filter-single">
                                <div th:replace="~{fragments/components :: form-select('promptVersion', 'promptVersion', '提示版本', ${promptVersions}, '', '请选择提示版本', false)}"></div>
                            </div>
                        </div>

                        <div class="filter-actions">
                            <button type="button" class="btn btn-primary" onclick="searchCodePrompt()">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                查询
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- CodePrompt Content -->
                <div id="codePromptContent" style="display: none;">
                    <!-- Action Buttons -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="d-flex justify-content-end gap-2">
                                <button type="button" class="btn btn-warning" id="editButton" onclick="toggleEditMode()">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    编辑
                                </button>
                                <button type="button" class="btn btn-success" id="saveButton" onclick="saveCodePrompt()" style="display: none;">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                              d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3-3-3m3-3v12"></path>
                                    </svg>
                                    保存
                                </button>
                                <button type="button" class="btn btn-secondary" id="cancelButton" onclick="cancelEdit()" style="display: none;">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    取消
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Prompt Content Cards -->
                    <div class="document-cards" id="promptContentCards">
                        <!-- Prompt content cards will be loaded here -->
                    </div>
                </div>
                
                <!-- Empty state for no selection -->
                <div id="noSelectionState" class="empty-state">
                    <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                    <h3 class="empty-state-title">请选择 CodePrompt</h3>
                    <p class="empty-state-description">请在上方选择业务领域、平台端和供应商端信息，然后点击查询按钮</p>
                </div>
                
                <!-- Empty state for no data -->
                <div id="noDataState" class="empty-state" style="display: none;">
                    <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="empty-state-title">暂无 CodePrompt 数据</h3>
                    <p class="empty-state-description">当前条件下没有找到相应的 CodePrompt 内容</p>
                    <a href="/mapping-gen/field-mapping" class="btn btn-primary">
                        返回字段映射管理
                    </a>
                </div>
            </main>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: scripts}"></div>
    <script th:src="@{/js/code-prompt.js}"></script>
</body>
</html>
