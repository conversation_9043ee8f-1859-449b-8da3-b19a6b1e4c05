package com.mappinggen.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * Mapping Association entity for platform-vendor interface mappings
 */
public class MappingAssociation {
    
    private Long id;
    private String businessDomain;
    
    // Platform side
    private String platformInterface;
    private String platformVersion;
    private Long platformKnowledgeBaseId;
    
    // Vendor side
    private String vendor;
    private String vendorInterface;
    private String vendorVersion;
    private Long vendorKnowledgeBaseId;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    // Constructors
    public MappingAssociation() {}
    
    public MappingAssociation(String businessDomain, String platformInterface, String platformVersion,
                             String vendor, String vendorInterface, String vendorVersion) {
        this.businessDomain = businessDomain;
        this.platformInterface = platformInterface;
        this.platformVersion = platformVersion;
        this.vendor = vendor;
        this.vendorInterface = vendorInterface;
        this.vendorVersion = vendorVersion;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getBusinessDomain() { return businessDomain; }
    public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }
    
    public String getPlatformInterface() { return platformInterface; }
    public void setPlatformInterface(String platformInterface) { this.platformInterface = platformInterface; }
    
    public String getPlatformVersion() { return platformVersion; }
    public void setPlatformVersion(String platformVersion) { this.platformVersion = platformVersion; }
    
    public Long getPlatformKnowledgeBaseId() { return platformKnowledgeBaseId; }
    public void setPlatformKnowledgeBaseId(Long platformKnowledgeBaseId) { this.platformKnowledgeBaseId = platformKnowledgeBaseId; }
    
    public String getVendor() { return vendor; }
    public void setVendor(String vendor) { this.vendor = vendor; }
    
    public String getVendorInterface() { return vendorInterface; }
    public void setVendorInterface(String vendorInterface) { this.vendorInterface = vendorInterface; }
    
    public String getVendorVersion() { return vendorVersion; }
    public void setVendorVersion(String vendorVersion) { this.vendorVersion = vendorVersion; }
    
    public Long getVendorKnowledgeBaseId() { return vendorKnowledgeBaseId; }
    public void setVendorKnowledgeBaseId(Long vendorKnowledgeBaseId) { this.vendorKnowledgeBaseId = vendorKnowledgeBaseId; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
    
    // Helper methods for display
    public String getPlatformDisplay() {
        return platformInterface + " v" + platformVersion;
    }
    
    public String getVendorDisplay() {
        return vendor + " - " + vendorInterface + " v" + vendorVersion;
    }
}
