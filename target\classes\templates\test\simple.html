<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Template Rendering Test</h1>
        <p class="success" th:text="${message}">Default message</p>
        <p>If you can see this page, the basic template rendering is working correctly.</p>
        <a href="../">Back to Home</a>
    </div>
</body>
</html>
