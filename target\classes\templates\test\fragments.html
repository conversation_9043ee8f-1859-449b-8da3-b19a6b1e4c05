<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head(${title})}"></head>
<body>
    <div class="app-layout">
        <div th:replace="~{fragments/layout :: sidebar}"></div>
        <div class="app-main">
            <div th:replace="~{fragments/layout :: header}"></div>
            <main class="app-content">
                <div class="page-header">
                    <h1 class="page-title">Fragment Test Page</h1>
                    <p class="page-subtitle">Testing Thymeleaf fragment inclusion</p>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <h3>Fragment Test Results</h3>
                        <p>If you can see the sidebar, header, and this content properly styled, 
                           then the fragment system is working correctly.</p>
                        
                        <div class="alert alert-success">
                            <strong>Success!</strong> All fragments loaded successfully.
                        </div>
                        
                        <a href="../" class="btn btn-primary">Back to Home</a>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: scripts}"></div>
</body>
</html>
