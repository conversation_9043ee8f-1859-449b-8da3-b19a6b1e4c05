# 映射生成器 (Mapping Generator)

企业级映射管理平台，提供完整的接口映射解决方案。

## 项目概述

映射生成器是一个基于 Spring Boot 和 Thymeleaf 的企业级 Web 应用程序，专门用于管理平台端和供应商端接口的映射关系。该应用提供了完整的知识库管理、映射关联建立、字段映射规则配置和代码提示生成功能。

## 主要功能

### 1. 知识库管理
- **平台知识库**：管理平台端接口的文档、提示和模板
- **供应商知识库**：管理供应商端接口的示例和文档
- 支持多种文件类型上传和在线编辑
- 提供文档查看和重新上传功能

### 2. 建立关联
- 创建平台端和供应商端接口的映射关联
- 支持业务领域、接口版本等多维度筛选
- 提供快速导航到字段映射管理的功能

### 3. 字段映射管理
- 详细的字段映射规则配置
- **交互式 JSON 树选择器**：可视化选择 JSON 路径
- 支持通用逻辑和补充逻辑配置
- 自动生成映射规则和代码提示

### 4. CodePrompt 管理
- 管理和编辑代码提示内容
- 支持多种文件类型的代码模板
- 在线编辑器功能
- 版本管理支持

## 技术栈

### 后端
- **Spring Boot 3.2.0**：主要框架
- **Spring Web**：Web 层支持
- **Spring Validation**：数据验证
- **Thymeleaf**：模板引擎
- **Jackson**：JSON 处理
- **Servlet 3.0+ Multipart**：文件上传（内置支持）

### 前端
- **Thymeleaf**：服务端模板渲染
- **CSS3**：现代样式设计
- **CSS 自定义属性**：主题系统
- **原生 JavaScript**：交互功能
- **响应式设计**：移动端适配

### 设计特性
- **企业级 UI**：专业、一致的设计系统
- **可访问性**：符合 WCAG 2.1 指南
- **响应式布局**：全屏设计，无空白
- **模块化组件**：可复用的 Thymeleaf 片段

## 项目结构

```
src/
├── main/
│   ├── java/com/mappinggen/
│   │   ├── MappingGeneratorApplication.java     # 主应用类
│   │   ├── controller/                          # 控制器层
│   │   │   ├── HomeController.java             # 页面路由控制器
│   │   │   ├── ErrorController.java            # 错误处理控制器
│   │   │   └── api/                            # REST API 控制器
│   │   │       ├── KnowledgeBaseApiController.java
│   │   │       ├── MappingAssociationApiController.java
│   │   │       ├── FieldMappingApiController.java
│   │   │       └── CodePromptApiController.java
│   │   ├── model/                              # 数据模型
│   │   │   ├── KnowledgeBase.java
│   │   │   ├── DocumentFile.java
│   │   │   ├── MappingAssociation.java
│   │   │   ├── FieldMapping.java
│   │   │   ├── FieldMappingRule.java
│   │   │   ├── CodePrompt.java
│   │   │   └── PromptFile.java
│   │   └── dto/                                # 数据传输对象
│   │       ├── ApiResponse.java
│   │       └── PageResult.java
│   └── resources/
│       ├── static/                             # 静态资源
│       │   ├── css/                           # 样式文件
│       │   │   ├── variables.css              # CSS 变量
│       │   │   ├── base.css                   # 基础样式
│       │   │   ├── components.css             # 组件样式
│       │   │   ├── layout.css                 # 布局样式
│       │   │   └── main.css                   # 主样式文件
│       │   └── js/                            # JavaScript 文件
│       │       ├── common.js                  # 通用功能
│       │       ├── knowledge-base.js          # 知识库功能
│       │       ├── mapping-association.js     # 映射关联功能
│       │       ├── field-mapping.js           # 字段映射功能
│       │       └── code-prompt.js             # 代码提示功能
│       ├── templates/                          # Thymeleaf 模板
│       │   ├── fragments/                     # 模板片段
│       │   │   ├── layout.html                # 布局片段
│       │   │   └── components.html            # 组件片段
│       │   ├── knowledge-base/                # 知识库模板
│       │   │   ├── platform.html
│       │   │   └── vendor.html
│       │   ├── index.html                     # 首页
│       │   ├── knowledge-base.html            # 知识库主页
│       │   ├── mapping-association.html       # 建立关联页面
│       │   ├── field-mapping.html             # 字段映射页面
│       │   ├── code-prompt.html               # 代码提示页面
│       │   └── error.html                     # 错误页面
│       └── application.yml                     # 应用配置
```

## 快速开始

### 环境要求
- Java 17 或更高版本
- Maven 3.6 或更高版本

### 运行应用

1. **克隆项目**
```bash
git clone <repository-url>
cd mappingGen
```

2. **编译项目**
```bash
mvn clean compile
```

3. **运行应用**
```bash
mvn spring-boot:run
```

4. **访问应用**
打开浏览器访问：http://localhost:8080/mapping-gen

### 构建部署包

```bash
mvn clean package
java -jar target/mapping-generator-1.0.0.jar
```

## API 文档

### 知识库 API
- `GET /api/knowledge-base` - 获取知识库列表
- `POST /api/knowledge-base` - 创建知识库
- `GET /api/knowledge-base/{id}/documents` - 获取知识库文档
- `PUT /api/knowledge-base/documents/{docId}` - 更新文档
- `DELETE /api/knowledge-base/{id}` - 删除知识库

### 映射关联 API
- `GET /api/mapping-association` - 获取映射关联列表
- `POST /api/mapping-association` - 创建映射关联
- `DELETE /api/mapping-association/{id}` - 删除映射关联

### 字段映射 API
- `GET /api/field-mapping` - 获取字段映射
- `POST /api/field-mapping/{id}/generate-mapping` - 生成映射规则
- `POST /api/field-mapping/{id}/generate-code-prompt` - 生成代码提示
- `PUT /api/field-mapping/{id}` - 更新字段映射

### 代码提示 API
- `GET /api/code-prompt` - 获取代码提示
- `PUT /api/code-prompt/{id}` - 更新代码提示

## 特色功能

### 交互式 JSON 树选择器
- 可视化 JSON 结构浏览
- 支持节点搜索和快速定位
- 多选支持，自动生成路径标签
- 数组元素统一索引处理 `[i]`

### 企业级 UI 设计
- 一致的设计系统和组件库
- 响应式布局，支持移动端
- 无障碍访问支持
- 专业的加载状态和错误处理

### 模块化架构
- 可复用的 Thymeleaf 片段
- 组件化的 CSS 样式系统
- 模块化的 JavaScript 功能
- 清晰的前后端分离

## 浏览器兼容性

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 开发说明

### 添加新功能
1. 在 `model` 包中定义数据模型
2. 在 `controller/api` 包中创建 API 控制器
3. 在 `templates` 目录中创建页面模板
4. 在 `static/js` 目录中添加 JavaScript 功能
5. 在 `static/css` 目录中添加样式

### 自定义样式
- 修改 `variables.css` 中的 CSS 自定义属性
- 在 `components.css` 中添加新组件样式
- 使用现有的设计系统保持一致性

### 扩展 API
- 所有 API 返回统一的 `ApiResponse` 格式
- 支持分页的 API 使用 `PageResult` 包装
- 遵循 RESTful 设计原则

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目 Issues：<repository-issues-url>
- 邮箱：<contact-email>
