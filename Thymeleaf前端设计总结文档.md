# 映射生成器 Thymeleaf 前端设计总结文档

## 1. 项目概述

### 1.1 项目简介
映射生成器是一个企业级映射管理平台，基于Spring Boot + Thymeleaf技术栈构建，提供完整的接口映射解决方案。系统主要功能包括知识库管理、映射关联建立、字段映射管理和代码提示管理。

### 1.2 技术栈
- **后端**: Spring Boot 2.x
- **模板引擎**: Thymeleaf 3.x
- **前端**: 原生JavaScript + CSS3
- **样式**: 自定义CSS设计系统
- **构建工具**: Maven

## 2. 样式设计系统

### 2.1 设计理念
采用企业级设计系统，注重用户体验和界面一致性，使用现代化的扁平设计风格。

### 2.2 色彩系统
```css
/* 主色调 */
--primary-color: #1976d2;      /* 主蓝色 */
--primary-dark: #1565c0;       /* 深蓝色 */
--primary-light: #42a5f5;      /* 浅蓝色 */

/* 状态色彩 */
--success-color: #059669;       /* 成功绿色 */
--warning-color: #d97706;      /* 警告橙色 */
--error-color: #dc2626;        /* 错误红色 */
--info-color: #0891b2;         /* 信息蓝色 */

/* 中性色彩 */
--gray-50: #fafafa;            /* 最浅灰 */
--gray-900: #212121;           /* 最深灰 */
```

### 2.3 字体系统
```css
--font-family-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
--font-family-mono: 'Consolas', 'Monaco', 'Courier New', monospace;

--font-size-xs: 0.75rem;       /* 12px */
--font-size-base: 1rem;        /* 16px */
--font-size-xl: 1.25rem;       /* 20px */
--font-size-3xl: 1.875rem;     /* 30px */
```

### 2.4 间距系统
```css
--spacing-xs: 0.25rem;         /* 4px */
--spacing-sm: 0.5rem;          /* 8px */
--spacing-md: 1rem;            /* 16px */
--spacing-lg: 1.5rem;          /* 24px */
--spacing-xl: 2rem;            /* 32px */
```

### 2.5 阴影系统
```css
--shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
```

## 3. 页面布局设计

### 3.1 整体布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    Header (64px)                        │
├─────────────┬───────────────────────────────────────────┤
│             │                                           │
│   Sidebar   │              Main Content                │
│   (280px)   │                                           │
│             │                                           │
│             │                                           │
└─────────────┴───────────────────────────────────────────┘
```

### 3.2 响应式设计
- **桌面端**: 侧边栏固定，主内容区域自适应
- **平板端**: 侧边栏可折叠，主内容区域占满
- **移动端**: 侧边栏隐藏，主内容区域全屏

### 3.3 组件化设计
系统采用高度组件化的设计，主要组件包括：

#### 3.3.1 导航组件
- **侧边栏导航**: 多级菜单结构
- **面包屑导航**: 页面层级导航
- **标签页导航**: 内容分类导航

#### 3.3.2 表单组件
- **输入框**: 支持多种类型和验证
- **下拉选择**: 支持级联选择
- **文件上传**: 支持拖拽上传
- **文本域**: 支持字符计数

#### 3.3.3 数据展示组件
- **数据表格**: 支持排序、分页、搜索
- **卡片组件**: 信息展示卡片
- **状态指示器**: 加载、成功、错误状态

## 4. 前后端交互流程

### 4.1 API接口架构

#### 4.1.1 基础API配置
```javascript
MappingGen.API = {
    BASE_URL: '/mapping-gen',
    
    // 统一API调用方法
    call: async function(endpoint, options = {}) {
        const url = this.BASE_URL + endpoint;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'API call failed');
            }
            
            return data;
        } catch (error) {
            console.error('API call error:', error);
            throw error;
        }
    }
};
```

### 4.2 知识库管理模块

#### 4.2.1 平台知识库接口

**接口1: 获取平台知识库列表**
- **接口地址**: `GET /api/knowledge-base`
- **触发时机**: 页面加载、搜索、分页
- **请求参数**:
  ```javascript
  {
    type: 'PLATFORM',
    businessDomain: 'string',    // 业务领域
    interfaceName: 'string',     // 接口名称
    version: 'string',           // 版本号
    page: 0,                    // 页码
    size: 10                    // 每页大小
  }
  ```
- **返回参数**:
  ```javascript
  {
    success: true,
    data: {
      content: [
        {
          id: 1,
          businessDomain: '航班搜索',
          interfaceName: 'FlightSearch',
          version: 'v1.0',
          createTime: '2024-01-15T10:30:00',
          documents: [...]
        }
      ],
      totalElements: 12,
      totalPages: 2,
      currentPage: 0
    }
  }
  ```

**接口2: 创建平台知识库**
- **接口地址**: `POST /api/knowledge-base`
- **触发时机**: 点击"添加知识"按钮
- **请求参数**:
  ```javascript
  {
    type: 'PLATFORM',
    businessDomain: 'string',
    interfaceName: 'string',
    version: 'string',
    requestPromptFile: File,    // 入参Prompt文件
    responsePromptFile: File,   // 返参Prompt文件
    codePromptFile: File,       // 代码Prompt文件
    codeTemplateFile: File,     // 代码模板文件
    inputDocFile: File,         // 入参文档
    outputDocFile: File,        // 返参文档
    inputExampleFile: File,     // 入参示例
    outputExampleFile: File     // 返参示例
  }
  ```
- **返回参数**:
  ```javascript
  {
    success: true,
    data: {
      id: 1,
      businessDomain: '航班搜索',
      interfaceName: 'FlightSearch',
      version: 'v1.0',
      createTime: '2024-01-15T10:30:00'
    }
  }
  ```

**接口3: 获取知识库文档**
- **接口地址**: `GET /api/knowledge-base/{id}/documents`
- **触发时机**: 点击"查看文档"按钮
- **请求参数**:
  ```javascript
  {
    id: 1,
    type: 'PLATFORM'
  }
  ```
- **返回参数**:
  ```javascript
  {
    success: true,
    data: {
      requestPrompt: '文档内容...',
      responsePrompt: '文档内容...',
      codePrompt: '文档内容...',
      codeTemplate: '文档内容...',
      inputDoc: '文档内容...',
      outputDoc: '文档内容...',
      inputExample: '文档内容...',
      outputExample: '文档内容...'
    }
  }
  ```

#### 4.2.2 供应商知识库接口

**接口1: 获取供应商知识库列表**
- **接口地址**: `GET /api/knowledge-base`
- **触发时机**: 页面加载、搜索、分页
- **请求参数**:
  ```javascript
  {
    type: 'VENDOR',
    businessDomain: 'string',
    interfaceName: 'string',
    version: 'string',
    vendor: 'string',           // 供应商名称
    page: 0,
    size: 10
  }
  ```

**接口2: 创建供应商知识库**
- **接口地址**: `POST /api/knowledge-base`
- **触发时机**: 点击"添加知识"按钮
- **请求参数**:
  ```javascript
  {
    type: 'VENDOR',
    businessDomain: 'string',
    interfaceName: 'string',
    version: 'string',
    vendor: 'string',
    // 其他文件参数同平台知识库
  }
  ```

### 4.3 字段映射管理模块

#### 4.3.1 新API接口

**接口1: 获取字段映射**
- **接口地址**: `GET /api/field-mapping`
- **触发时机**: 页面加载、搜索条件变化
- **请求参数**:
  ```javascript
  {
    platformInterfaceId: 1,
    supplierInterfaceId: 2
  }
  ```
- **返回参数**:
  ```javascript
  {
    success: true,
    data: {
      fieldMappings: [
        {
          id: 1,
          platformInterfaceId: 1,
          supplierInterfaceId: 2,
          platformField: 'flightNo',
          supplierField: 'response.flightList[i].flightNumber',
          supplementaryLogic: '// 航班号标准化处理...',
          mappingInfo: 'SCRIPT'
        }
      ],
      platformFieldList: ['flightNo', 'departureTime', ...],
      supplierFields: '{"type":"object",...}',
      commonLogic: '// 通用处理逻辑...'
    }
  }
  ```

**接口2: 保存字段映射**
- **接口地址**: `POST /api/field-mapping/save`
- **触发时机**: 点击"保存"按钮
- **请求参数**:
  ```javascript
  {
    platformInterfaceId: 1,
    supplierInterfaceId: 2,
    commonLogic: '// 通用逻辑...',
    fieldMappingRequests: [
      {
        platformField: 'flightNo',
        supplierField: 'response.flightList[i].flightNumber',
        mappingInfo: 'SCRIPT',
        supplementaryLogic: '// 处理逻辑...'
      }
    ]
  }
  ```
- **返回参数**:
  ```javascript
  {
    success: true,
    message: '保存成功'
  }
  ```

**接口3: 生成字段映射**
- **接口地址**: `POST /api/field-mapping/generate`
- **触发时机**: 点击"生成映射"按钮
- **请求参数**: 同保存接口
- **返回参数**:
  ```javascript
  {
    success: true,
    data: [
      {
        id: 1,
        platformField: 'flightNo',
        supplierField: 'response.flightList[i].flightNumber',
        supplementaryLogic: '// 自动生成的逻辑...',
        mappingInfo: 'SCRIPT'
      }
    ]
  }
  ```

#### 4.3.2 旧API接口

**接口1: 获取字段映射**
- **接口地址**: `GET /old/api/field-mapping`
- **触发时机**: 页面加载、搜索
- **请求参数**:
  ```javascript
  {
    businessDomain: 'string',
    platformInterface: 'string',
    platformVersion: 'string',
    vendor: 'string',
    vendorInterface: 'string',
    vendorVersion: 'string'
  }
  ```

**接口2: 生成映射规则**
- **接口地址**: `POST /old/api/field-mapping/{id}/generate-mapping`
- **触发时机**: 点击"生成映射"按钮
- **请求参数**:
  ```javascript
  {
    mappingRules: [
      {
        ifsField: 'flightNumber',
        airlineResponse: 'response.flightList[i].flightNo',
        supplementaryLogic: '// 处理逻辑...'
      }
    ]
  }
  ```

### 4.4 映射关联管理模块

**接口1: 获取映射关联列表**
- **接口地址**: `GET /api/mapping-association`
- **触发时机**: 页面加载、搜索、分页
- **请求参数**:
  ```javascript
  {
    businessDomain: 'string',
    platformInterface: 'string',
    vendor: 'string',
    page: 0,
    size: 10
  }
  ```

**接口2: 创建映射关联**
- **接口地址**: `POST /api/mapping-association`
- **触发时机**: 点击"创建关联"按钮
- **请求参数**:
  ```javascript
  {
    businessDomain: 'string',
    platformInterface: 'string',
    platformVersion: 'string',
    vendor: 'string',
    vendorInterface: 'string',
    vendorVersion: 'string'
  }
  ```

### 4.5 代码提示管理模块

**接口1: 获取代码提示列表**
- **接口地址**: `GET /api/code-prompt`
- **触发时机**: 页面加载、搜索、分页
- **请求参数**:
  ```javascript
  {
    businessDomain: 'string',
    interfaceName: 'string',
    version: 'string',
    page: 0,
    size: 10
  }
  ```

**接口2: 创建代码提示**
- **接口地址**: `POST /api/code-prompt`
- **触发时机**: 点击"添加代码提示"按钮
- **请求参数**:
  ```javascript
  {
    businessDomain: 'string',
    interfaceName: 'string',
    version: 'string',
    promptContent: 'string',
    promptFile: File
  }
  ```

## 5. 页面功能模块

### 5.1 首页模块 (`index.html`)
- **功能**: 系统概览和快速导航
- **组件**: 功能卡片、统计信息
- **交互**: 点击卡片跳转到对应功能页面

### 5.2 知识库管理模块 (`knowledge-base.html`)
- **功能**: 管理平台和供应商的知识库
- **子模块**: 
  - 平台知识库 (`platform.html`)
  - 供应商知识库 (`vendor.html`)
- **主要功能**:
  - 知识库查询和筛选
  - 知识库创建和编辑
  - 文档上传和管理
  - 文档查看和编辑

### 5.3 字段映射管理模块 (`field-mapping.html`)
- **功能**: 管理平台端和供应商端的字段映射规则
- **主要功能**:
  - 映射规则查询和筛选
  - 映射规则创建和编辑
  - 自动生成映射规则
  - 代码提示生成

### 5.4 映射关联管理模块 (`mapping-association.html`)
- **功能**: 创建和管理平台端与供应商端的映射关联
- **主要功能**:
  - 关联关系查询和筛选
  - 关联关系创建和编辑
  - 关联关系删除

### 5.5 代码提示管理模块 (`code-prompt.html`)
- **功能**: 管理和编辑代码提示内容
- **主要功能**:
  - 代码提示查询和筛选
  - 代码提示创建和编辑
  - 代码提示删除

## 6. 用户体验设计

### 6.1 交互设计
- **加载状态**: 统一的加载动画和状态指示
- **错误处理**: 友好的错误提示和恢复机制
- **成功反馈**: 操作成功的即时反馈
- **表单验证**: 实时表单验证和错误提示

### 6.2 响应式设计
- **断点设置**:
  - 移动端: < 768px
  - 平板端: 768px - 1024px
  - 桌面端: > 1024px
- **适配策略**: 弹性布局 + 媒体查询

### 6.3 性能优化
- **资源加载**: CSS/JS文件压缩和缓存
- **图片优化**: 使用SVG图标和适当的图片格式
- **代码分割**: 按模块加载JavaScript文件

## 7. 开发规范

### 7.1 文件组织
```
src/main/resources/
├── static/
│   ├── css/
│   │   ├── variables.css      # 设计系统变量
│   │   ├── base.css          # 基础样式
│   │   ├── layout.css        # 布局样式
│   │   ├── components.css    # 组件样式
│   │   └── main.css          # 主样式文件
│   └── js/
│       ├── common.js         # 公共工具
│       ├── knowledge-base.js # 知识库功能
│       ├── field-mapping.js  # 字段映射功能
│       └── ...
└── templates/
    ├── fragments/
    │   ├── layout.html       # 布局模板
    │   └── components.html   # 组件模板
    ├── knowledge-base/
    │   ├── platform.html     # 平台知识库
    │   └── vendor.html       # 供应商知识库
    └── ...
```

### 7.2 命名规范
- **CSS类名**: 使用BEM命名法
- **JavaScript变量**: 使用驼峰命名法
- **API接口**: 使用RESTful风格
- **文件命名**: 使用kebab-case

### 7.3 代码规范
- **HTML**: 语义化标签，无障碍访问
- **CSS**: 模块化组织，避免样式冲突
- **JavaScript**: ES6+语法，模块化开发

## 8. 部署配置

### 8.1 应用配置 (`application.yml`)
```yaml
server:
  port: 8080
  servlet:
    context-path: /mapping-gen

spring:
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
  
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB
```

### 8.2 静态资源配置
- **缓存策略**: 开发环境禁用缓存
- **压缩配置**: 启用Gzip压缩
- **安全配置**: 设置适当的CORS策略

## 9. 总结

映射生成器的Thymeleaf前端设计采用了现代化的企业级设计系统，具有以下特点：

1. **模块化设计**: 高度组件化的架构，便于维护和扩展
2. **响应式布局**: 适配多种设备，提供良好的用户体验
3. **统一的设计语言**: 一致的颜色、字体、间距系统
4. **完善的交互流程**: 清晰的前后端交互接口设计
5. **良好的开发体验**: 规范的代码组织和命名约定

该系统为映射管理提供了完整的前端解决方案，具有良好的可维护性和扩展性。 