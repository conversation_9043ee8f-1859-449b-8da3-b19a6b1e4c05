# FSC Assistant API接口文档

## 概述

FSC Assistant是一个航班供应链助手系统，提供平台接口管理、供应商接口管理、字段映射、Prompt版本管理等功能。本文档详细描述了系统中所有Controller层的API接口。

## 通用响应格式

所有API接口都使用统一的响应格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "code": null
}
```

### 响应字段说明

| 字段名  | 类型    | 说明           |
| ------- | ------- | -------------- |
| success | boolean | 操作是否成功   |
| message | string  | 响应消息       |
| data    | object  | 响应数据       |
| code    | integer | 错误码（可选） |

## 分页响应格式

分页查询接口使用以下响应格式：

```json
{
  "records": [],
  "total": 100,
  "pageNum": 1,
  "pageSize": 10,
  "totalPages": 10,
  "hasNext": true,
  "hasPrevious": false
}
```

### 分页字段说明

| 字段名      | 类型    | 说明         |
| ----------- | ------- | ------------ |
| records     | array   | 数据列表     |
| total       | long    | 总记录数     |
| pageNum     | integer | 当前页码     |
| pageSize    | integer | 每页大小     |
| totalPages  | integer | 总页数       |
| hasNext     | boolean | 是否有下一页 |
| hasPrevious | boolean | 是否有上一页 |

---


## 2. 平台接口控制器 (PlatformInterfaceController)

### 2.1 查询平台接口树形结构

**接口地址：** `GET /api/platform-interface/tree`

**接口描述：** 获取平台接口的树形结构数据，返回业务域-接口-版本的三层结构

**请求参数：** 无

**响应数据：** BusinessDomainTreeResponse列表

**响应数据结构：**

- **BusinessDomainTreeResponse**：业务域树形响应对象
  - `id` (Long): 业务域ID
  - `businessDomain` (String): 业务域名称
  - `interfaces` (List<InterfaceTreeNode>): 接口列表

- **InterfaceTreeNode**：接口树形节点
  - `id` (Long): 接口ID
  - `interfaceName` (String): 接口名称
  - `versions` (List<VersionTreeNode>): 版本列表

- **VersionTreeNode**：版本树形节点
  - `id` (Long): 版本ID
  - `version` (String): 版本号

**请求示例：**

```http
GET /api/platform-interface/tree HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "data": [
    {
      "id": null,
      "businessDomain": "机票业务域",
      "interfaces": [
        {
          "id": null,
          "interfaceName": "搜索接口",
          "versions": [
            {
              "id": 1,
              "version": "v1.0"
            },
            {
              "id": 2,
              "version": "v1.1"
            }
          ]
        }
      ]
    }
  ]
}
```

### 2.2 分页查询平台接口列表

**接口地址：** `GET /api/platform-interface/list`

**接口描述：** 分页查询平台接口列表，支持按业务域、接口名、版本号筛选

**请求参数：**

| 参数名         | 类型    | 必填 | 说明             |
| -------------- | ------- | ---- | ---------------- |
| businessDomain | string  | 否   | 业务域           |
| interfaceName  | string  | 否   | 接口名           |
| version        | string  | 否   | 版本号           |
| pageNum        | integer | 否   | 页码，默认1      |
| pageSize       | integer | 否   | 每页大小，默认10 |

**响应数据：** 分页的平台接口响应对象列表

**请求示例：**

```http
GET /api/platform-interface/list?businessDomain=机票&pageNum=1&pageSize=10 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "businessDomain": "机票",
        "interfaceName": "搜索接口",
        "interfaceVersion": "v1.0",
        "respFields": "{\"fields\":[{\"name\":\"flightNo\",\"type\":\"string\"}]}",
        "respExample": "{\"flightNo\":\"CA1234\"}",
        "reqDoc": "请求参数文档",
        "respDoc": "响应参数文档",
        "reqBeanPrompt": "入参Bean生成提示",
        "respBeanPrompt": "返参Bean生成提示",
        "codeGenPrompt": "代码生成提示",
        "codeTemplateDoc": "代码模板文档",
        "operator": "张三",
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 1,
    "pageNum": 1,
    "pageSize": 10,
    "totalPages": 1,
    "hasNext": false,
    "hasPrevious": false
  }
}
```

### 2.3 根据ID查询平台接口

**接口地址：** `GET /api/platform-interface/{id}`

**接口描述：** 根据ID查询单个平台接口的详细信息

**请求参数：**

| 参数名 | 类型 | 必填 | 说明                   |
| ------ | ---- | ---- | ---------------------- |
| id     | long | 是   | 平台接口ID（路径参数） |

**响应数据：** 平台接口响应对象

**请求示例：**

```http
GET /api/platform-interface/1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "id": 1,
    "businessDomain": "机票",
    "interfaceName": "搜索接口",
    "interfaceVersion": "v1.0",
    "respFields": "{\"fields\":[{\"name\":\"flightNo\",\"type\":\"string\"}]}",
    "respExample": "{\"flightNo\":\"CA1234\"}",
    "reqDoc": "请求参数文档",
    "respDoc": "响应参数文档",
    "reqBeanPrompt": "入参Bean生成提示",
    "respBeanPrompt": "返参Bean生成提示",
    "codeGenPrompt": "代码生成提示",
    "codeTemplateDoc": "代码模板文档",
    "operator": "张三",
    "createTime": "2024-01-01 10:00:00",
    "updateTime": "2024-01-01 10:00:00"
  }
}
```

### 2.4 添加平台接口

**接口地址：** `POST /api/platform-interface`

**接口描述：** 添加新的平台接口，支持文件上传

**请求参数：**

| 参数名           | 类型   | 必填 | 说明                   |
| ---------------- | ------ | ---- | ---------------------- |
| businessDomain   | string | 否   | 业务域                 |
| interfaceName    | string | 否   | 接口名                 |
| interfaceVersion | string | 否   | 接口版本号             |
| operator         | string | 否   | 操作人                 |
| respExample      | file   | 否   | 平台返参示例文件       |
| reqDoc           | file   | 否   | 平台入参文档文件       |
| respDoc          | file   | 否   | 平台返参文档文件       |
| reqBeanPrompt    | file   | 否   | 入参Bean生成Prompt文件 |
| respBeanPrompt   | file   | 否   | 返参Bean生成Prompt文件 |
| codeGenPrompt    | file   | 否   | 代码生成Prompt文件     |
| codeTemplateDoc  | file   | 否   | 代码模板文档文件       |

**响应数据：** 操作结果

**请求示例：**

```http
POST /api/platform-interface HTTP/1.1
Host: localhost:8080
Content-Type: multipart/form-data

--boundary
Content-Disposition: form-data; name="businessDomain"

机票
--boundary
Content-Disposition: form-data; name="interfaceName"

搜索接口
--boundary
Content-Disposition: form-data; name="interfaceVersion"

v1.0
--boundary
Content-Disposition: form-data; name="operator"

张三
--boundary--
```

**响应示例：**

```json
{
  "success": true,
  "message": "添加平台接口成功",
  "data": null
}
```

### 2.5 更新平台接口

**接口地址：** `PUT /api/platform-interface/{id}`

**接口描述：** 更新指定ID的平台接口信息

**请求参数：**

| 参数名  | 类型   | 必填 | 说明                       |
| ------- | ------ | ---- | -------------------------- |
| id      | long   | 是   | 平台接口ID（路径参数）     |
| request | object | 是   | 平台接口请求对象（请求体） |

**请求体结构：**

```json
{
  "businessDomain": "机票",
  "interfaceName": "搜索接口",
  "interfaceVersion": "v1.1",
  "respFields": "{\"fields\":[{\"name\":\"flightNo\",\"type\":\"string\"}]}",
  "respExample": "{\"flightNo\":\"CA1234\"}",
  "reqDoc": "请求参数文档",
  "respDoc": "响应参数文档",
  "reqBeanPrompt": "入参Bean生成提示",
  "respBeanPrompt": "返参Bean生成提示",
  "codeGenPrompt": "代码生成提示",
  "codeTemplateDoc": "代码模板文档",
  "operator": "李四"
}
```

**响应数据：** 操作结果

**请求示例：**

```http
PUT /api/platform-interface/1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "businessDomain": "机票",
  "interfaceName": "搜索接口",
  "interfaceVersion": "v1.1",
  "operator": "李四"
}
```

**响应示例：**

```json
{
  "success": true,
  "message": "更新平台接口成功",
  "data": null
}
```

### 2.6 删除平台接口

**接口地址：** `DELETE /api/platform-interface/{id}`

**接口描述：** 删除指定ID的平台接口

**请求参数：**

| 参数名 | 类型 | 必填 | 说明                   |
| ------ | ---- | ---- | ---------------------- |
| id     | long | 是   | 平台接口ID（路径参数） |

**响应数据：** 操作结果

**请求示例：**

```http
DELETE /api/platform-interface/1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "删除平台接口成功",
  "data": null
}
```

---

## 3. 供应商接口控制器 (SupplierInterfaceController)

### 3.1 查询供应商接口树形结构

**接口地址：** `GET /api/supplier-interface/tree`

**接口描述：** 获取供应商接口的树形结构数据，返回业务域-供应商-接口-版本的四层结构

**请求参数：** 无

**响应数据：** SupplierBusinessDomainTreeResponse列表

**响应数据结构：**

- **SupplierBusinessDomainTreeResponse**：供应商业务域树形响应对象
  - `id` (Long): 业务域ID
  - `businessDomain` (String): 业务域名称
  - `suppliers` (List<SupplierTreeNode>): 供应商列表

- **SupplierTreeNode**：供应商树形节点
  - `id` (Long): 供应商ID
  - `supplierName` (String): 供应商名称
  - `interfaces` (List<SupplierInterfaceTreeNode>): 接口列表

- **SupplierInterfaceTreeNode**：供应商接口树形节点
  - `id` (Long): 接口ID
  - `interfaceName` (String): 接口名称
  - `versions` (List<SupplierVersionTreeNode>): 版本列表

- **SupplierVersionTreeNode**：供应商版本树形节点
  - `id` (Long): 版本ID
  - `version` (String): 版本号

**请求示例：**

```http
GET /api/supplier-interface/tree HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "data": [
    {
      "id": null,
      "businessDomain": "机票业务域",
      "suppliers": [
        {
          "id": null,
          "supplierName": "东航",
          "interfaces": [
            {
              "id": null,
              "interfaceName": "搜索接口",
              "versions": [
                {
                  "id": 1,
                  "version": "v1.0"
                },
                {
                  "id": 2,
                  "version": "v1.1"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 3.2 分页查询供应商接口列表

**接口地址：** `GET /api/supplier-interface/list`

**接口描述：** 分页查询供应商接口列表，支持按业务域、供应商名称、接口名、版本号筛选

**请求参数：**

| 参数名         | 类型    | 必填 | 说明             |
| -------------- | ------- | ---- | ---------------- |
| businessDomain | string  | 否   | 业务域           |
| supplierName   | string  | 否   | 供应商名称       |
| interfaceName  | string  | 否   | 接口名           |
| version        | string  | 否   | 版本号           |
| pageNum        | integer | 否   | 页码，默认1      |
| pageSize       | integer | 否   | 每页大小，默认10 |

**响应数据：** 分页的供应商接口响应对象列表

**请求示例：**

```http
GET /api/supplier-interface/list?supplierName=东航&pageNum=1&pageSize=10 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "businessDomain": "航空",
        "supplierName": "供应商A",
        "interfaceName": "接口名称",
        "version": "1.0",
        "respFields": "字段1,字段2",
        "reqExample": "请求示例内容",
        "respExample": "响应示例内容",
        "reqDoc": "请求文档内容",
        "respDoc": "响应文档内容",
        "operator": "操作员A",
        "createTime": "2023-10-01T12:00:00",
        "updateTime": "2023-10-02T12:00:00"
      }
    ],
    "total": 1,
    "pageNum": 1,
    "pageSize": 10,
    "totalPages": 1,
    "hasNext": false,
    "hasPrevious": false
  }
}
```

### 3.3 根据ID查询供应商接口

**接口地址：** `GET /api/supplier-interface/{id}`

**接口描述：** 根据ID查询单个供应商接口的详细信息

**请求参数：**

| 参数名 | 类型 | 必填 | 说明                     |
| ------ | ---- | ---- | ------------------------ |
| id     | long | 是   | 供应商接口ID（路径参数） |

**响应数据：** 供应商接口响应对象

**请求示例：**

```http
GET /api/supplier-interface/1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "id": 1,
    "businessDomain": "机票",
    "supplierName": "东航",
    "interfaceName": "搜索接口",
    "version": "v1.0",
    "respFields": "{\"fields\":[{\"name\":\"flight_no\",\"type\":\"string\"}]}",
    "reqExample": "{\"departure\":\"PEK\",\"arrival\":\"SHA\"}",
    "respExample": "{\"flight_no\":\"MU1234\"}",
    "reqDoc": "供应商请求参数文档",
    "respDoc": "供应商响应参数文档",
    "operator": "王五",
    "createTime": "2024-01-01 10:00:00",
    "updateTime": "2024-01-01 10:00:00"
  }
}
```

### 3.4 添加供应商接口

**接口地址：** `POST /api/supplier-interface`

**接口描述：** 添加新的供应商接口，支持文件上传

**请求参数：**

| 参数名         | 类型   | 必填 | 说明             |
| -------------- | ------ | ---- | ---------------- |
| businessDomain | string | 否   | 业务域           |
| supplierName   | string | 否   | 供应商名称       |
| interfaceName  | string | 否   | 接口名           |
| version        | string | 否   | 版本号           |
| respFields     | string | 否   | 供应报文解析集合 |
| operator       | string | 否   | 操作人           |
| reqExample     | file   | 否   | 供应入参示例文件 |
| respExample    | file   | 否   | 供应返参示例文件 |
| reqDoc         | file   | 否   | 供应入参文档文件 |
| respDoc        | file   | 否   | 供应返参文档文件 |

**响应数据：** 操作结果

**请求示例：**

```http
POST /api/supplier-interface HTTP/1.1
Host: localhost:8080
Content-Type: multipart/form-data

--boundary
Content-Disposition: form-data; name="businessDomain"

机票
--boundary
Content-Disposition: form-data; name="supplierName"

东航
--boundary
Content-Disposition: form-data; name="interfaceName"

搜索接口
--boundary
Content-Disposition: form-data; name="version"

v1.0
--boundary
Content-Disposition: form-data; name="operator"

王五
--boundary--
```

**响应示例：**

```json
{
  "success": true,
  "message": "添加供应商接口成功",
  "data": null
}
```

### 3.5 更新供应商接口

**接口地址：** `PUT /api/supplier-interface/{id}`

**接口描述：** 更新指定ID的供应商接口信息

**请求参数：**

| 参数名  | 类型   | 必填 | 说明                         |
| ------- | ------ | ---- | ---------------------------- |
| id      | long   | 是   | 供应商接口ID（路径参数）     |
| request | object | 是   | 供应商接口请求对象（请求体） |

**请求体结构：**

```json
{
  "businessDomain": "机票",
  "supplierName": "东航",
  "interfaceName": "搜索接口",
  "version": "v1.1",
  "respFields": "{\"fields\":[{\"name\":\"flight_no\",\"type\":\"string\"}]}",
  "reqExample": "{\"departure\":\"PEK\",\"arrival\":\"SHA\"}",
  "respExample": "{\"flight_no\":\"MU1234\"}",
  "reqDoc": "供应入参文档文件",
  "respDoc": "供应返参文档文件",
  "operator": "赵六"
}
```

**响应数据：** 操作结果

**请求示例：**

```http
PUT /api/supplier-interface/1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "businessDomain": "机票",
  "supplierName": "东航",
  "interfaceName": "搜索接口",
  "version": "v1.1",
  "operator": "赵六"
}
```

**响应示例：**

```json
{
  "success": true,
  "message": "更新供应商接口成功",
  "data": null
}
```

### 3.6 删除供应商接口

**接口地址：** `DELETE /api/supplier-interface/{id}`

**接口描述：** 删除指定ID的供应商接口

**请求参数：**

| 参数名 | 类型 | 必填 | 说明                     |
| ------ | ---- | ---- | ------------------------ |
| id     | long | 是   | 供应商接口ID（路径参数） |

**响应数据：** 操作结果

**请求示例：**

```http
DELETE /api/supplier-interface/1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "删除供应商接口成功",
  "data": null
}
```

---

## 4. Prompt版本管理控制器 (PromptVersionController)

### 4.1 查询Prompt所有版本

**接口地址：** `GET /api/prompt-version/versionList`

**接口描述：** 根据平台接口ID和供应商接口ID查询所有Prompt版本列表

**请求参数：**

| 参数名              | 类型 | 必填 | 说明         |
| ------------------- | ---- | ---- | ------------ |
| platformInterfaceId | long | 是   | 平台接口ID   |
| supplierInterfaceId | long | 是   | 供应商接口ID |

**响应数据：** 版本号字符串列表

**请求示例：**

```http
GET /api/prompt-version/versionList?platformInterfaceId=1&supplierInterfaceId=1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "data": [
    "v1.0",
    "v1.1",
    "v2.0"
  ]
}
```

### 4.2 根据版本号查询Prompt

**接口地址：** `GET /api/prompt-version/{version}`

**接口描述：** 根据平台接口ID、供应商接口ID和版本号查询具体的Prompt版本信息

**请求参数：**

| 参数名              | 类型   | 必填 | 说明               |
| ------------------- | ------ | ---- | ------------------ |
| version             | string | 是   | 版本号（路径参数） |
| platformInterfaceId | long   | 是   | 平台接口ID         |
| supplierInterfaceId | long   | 是   | 供应商接口ID       |

**响应数据：** Prompt版本响应对象

**请求示例：**

```http
GET /api/prompt-version/v1.0?platformInterfaceId=1&supplierInterfaceId=1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "id": 123,
    "platformInterfaceId": 456,
    "supplierInterfaceId": 789,
    "reqBeanPrompt": "请求Bean的提示信息",
    "respBeanPrompt": "响应Bean的提示信息",
    "codeGenPrompt": "代码生成的提示信息",
    "version": "v1.0",
    "operator": "操作人",
    "createTime": "2023-10-01T12:00:00",
    "updateTime": "2023-10-02T15:30:00"
  }
}
```

### 4.3 创建新Prompt版本

**接口地址：** `POST /api/prompt-version`

**接口描述：** 根据平台接口ID和供应商接口ID创建新的Prompt版本

**请求参数：**

| 参数名              | 类型 | 必填 | 说明         |
| ------------------- | ---- | ---- | ------------ |
| platformInterfaceId | long | 是   | 平台接口ID   |
| supplierInterfaceId | long | 是   | 供应商接口ID |

**响应数据：** 创建的Prompt版本响应对象

**请求示例：**

```http
POST /api/prompt-version?platformInterfaceId=1&supplierInterfaceId=1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "创建Prompt版本成功",
  "data": {
    "id": 123,
    "platformInterfaceId": 456,
    "supplierInterfaceId": 789,
    "reqBeanPrompt": "请求Bean的提示信息",
    "respBeanPrompt": "响应Bean的提示信息",
    "codeGenPrompt": "代码生成的提示信息",
    "version": "v1.0",
    "operator": "操作人",
    "createTime": "2023-10-01T12:00:00",
    "updateTime": "2023-10-02T15:30:00"
  }
}
```

### 4.4 更新Prompt版本

**接口地址：** `PUT /api/prompt-version/{id}`

**接口描述：** 更新指定ID的Prompt版本信息

**请求参数：**

| 参数名  | 类型   | 必填 | 说明                         |
| ------- | ------ | ---- | ---------------------------- |
| id      | long   | 是   | Prompt版本ID（路径参数）     |
| request | object | 是   | Prompt版本请求对象（请求体） |

**请求体结构：**

```json
{
  "reqBeanPrompt": "文件类型",
  "respBeanPrompt": "文件类型",
  "codeGenPrompt": "代码prompt文件类型",
  "operator": "操作人"
}
```

**响应数据：** 更新后的Prompt版本响应对象

**请求示例：**

```http
PUT /api/prompt-version/1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "platformInterfaceId": 1,
  "supplierInterfaceId": 1,
  "version": "v1.1",
  "promptContent": "更新后的Prompt内容"
}
```

**响应示例：**

```json
{
  "success": true,
  "message": "更新Prompt版本成功",
  "data": {
    "id": 123,
    "platformInterfaceId": 456,
    "supplierInterfaceId": 789,
    "reqBeanPrompt": "请求Bean的提示信息",
    "respBeanPrompt": "响应Bean的提示信息",
    "codeGenPrompt": "代码生成的提示信息",
    "version": "v1.0",
    "operator": "操作人",
    "createTime": "2023-10-01T12:00:00",
    "updateTime": "2023-10-02T15:30:00"
  }
}
```

---

## 5. 平台供应商映射控制器 (PlatformSupplierMappingController)

### 5.1 分页查询映射关系

**接口地址：** `GET /api/platform-supplier-mapping/search`

**接口描述：** 分页查询平台供应商映射关系，支持按业务域、平台接口名、平台版本、供应商名称、供应商接口名、供应商版本筛选

**请求参数：**

| 参数名                | 类型    | 必填 | 说明             |
| --------------------- | ------- | ---- | ---------------- |
| businessDomain        | string  | 否   | 业务域           |
| platformInterfaceName | string  | 否   | 平台接口名       |
| platformVersion       | string  | 否   | 平台版本         |
| supplierName          | string  | 否   | 供应商名称       |
| supplierInterfaceName | string  | 否   | 供应商接口名     |
| supplierVersion       | string  | 否   | 供应商版本       |
| pageNum               | integer | 否   | 页码，默认1      |
| pageSize              | integer | 否   | 每页大小，默认10 |

**响应数据：** 分页的平台供应商映射响应对象列表

**请求示例：**

```http
GET /api/platform-supplier-mapping/search?businessDomain=机票&pageNum=1&pageSize=10 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "data": {
    "records": [{
      "id": 1,
      "platformAppendInfo": "机票-av-v1",
      "supplierAppendInfo": "机票-icaair-shopping-v1"
    },
      {
        "id": 2,
        "platformAppendInfo": "机票-av-v1",
        "supplierAppendInfo": "机票-icaair-shopping-v2"
      }
    ],
    "total": 1,
    "pageNum": 1,
    "pageSize": 10
  },
  "message": null
}
```

### 5.2 添加映射关系

**接口地址：** `POST /api/platform-supplier-mapping`

**接口描述：** 添加新的平台供应商映射关系

**请求参数：**

| 参数名  | 类型   | 必填 | 说明                             |
| ------- | ------ | ---- | -------------------------------- |
| request | object | 是   | 平台供应商映射请求对象（请求体） |

**请求体结构：**

```json
{
  "platformInterfaceId": 1,
  "supplierInterfaceId": 1
}
```

**响应数据：** 创建的映射关系响应对象

**请求示例：**

```http
POST /api/platform-supplier-mapping HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "platformInterfaceId": 1,
  "supplierInterfaceId": 1
}
```

**响应示例：**

```json
{
  "id": 1,
  "platformInterfaceId": 101,
  "supplierInterfaceId": 202,
  "commonLogic": "Some common logic",
  "operator": "admin",
  "createTime": "2023-10-01T12:00:00",
  "updateTime": "2023-10-02T15:30:00",
  "platformBusinessDomain": "订单",
  "platformInterfaceName": "创建订单",
  "platformVersion": "v1",
  "supplierBusinessDomain": "订单",
  "supplierName": "供应商A",
  "supplierInterfaceName": "订单创建",
  "supplierVersion": "v1"
}
```

### 5.3 删除映射关系

**接口地址：** `DELETE /api/platform-supplier-mapping/{id}`

**接口描述：** 删除指定ID的平台供应商映射关系

**请求参数：**

| 参数名 | 类型 | 必填 | 说明                   |
| ------ | ---- | ---- | ---------------------- |
| id     | long | 是   | 映射关系ID（路径参数） |

**响应数据：** 操作结果

**请求示例：**

```http
DELETE /api/platform-supplier-mapping/1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "删除映射关系成功",
  "data": null
}
```

---

## 6. 字段映射控制器 (FieldMappingController)

### 6.1 查询字段映射

**接口地址：** `GET /api/field-mapping`

**接口描述：** 根据平台接口ID和供应商接口ID查询字段映射关系，返回平台字段结构、供应商字段结构和映射关系

**请求参数：**

| 参数名              | 类型 | 必填 | 说明         |
| ------------------- | ---- | ---- | ------------ |
| platformInterfaceId | long | 是   | 平台接口ID   |
| supplierInterfaceId | long | 是   | 供应商接口ID |

**响应数据：** 字段映射响应对象

**请求示例：**

```http
GET /api/field-mapping?platformInterfaceId=1&supplierInterfaceId=1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "fieldMappings": [
      {
        "id": 12345,
        "platformInterfaceId": 67890,
        "supplierInterfaceId": 54321,
        "platformField": "user.name",
        "supplierField": "customer.fullName",
        "supplementaryLogic": "if (value == null) { value = 'default'; }",
        "mappingInfo": "SCRIPT",
        "operator": "admin",
        "createTime": "2023-10-01T12:00:00",
        "updateTime": "2023-10-02T15:30:00"
      }
    ],
    "platformFieldList": [
      "flightNo",
        "departureTime"
    ],
    "supplierFields": "json字符串",
    "commonLogic": "通用处理逻辑"
  }
}
```

**响应参数部分说明：**
| 参数名              | 类型 | 说明         |
| ------------------- | ---- |------------ |
| supplierFields                   | String |航空公司返回结果树形选择器需要渲染的json字符串 |
| commonLogic                      | String |通用逻辑 |
| fieldMappings.id                   | long |映射ID |
| fieldMappings.platformInterfaceId | long |平台接口ID   |
| fieldMappings.supplierInterfaceId | long |供应商接口ID |
| fieldMappings.platformField | String | IFS字段 |
| fieldMappings.supplierField | String | 航空公司返回结果输入框的值 |
| fieldMappings.supplementaryLogic | String | 补充逻辑 |
| fieldMappings.mappingInfo   | String | 映射规则 |


### 6.2 根据映射ID查询字段映射

**接口地址：** `GET /api/field-mapping/getFieldMappingByMappingId`

**接口描述：** 根据平台供应商映射表ID查询字段映射关系

**请求参数：**

| 参数名             | 类型 | 必填 | 说明               |
| ------------------ | ---- | ---- | ------------------ |
| platformSupplierId | long | 是   | 平台供应商映射表ID |

**响应数据：** 字段映射响应对象

**请求示例：**

```http
GET /api/field-mapping?platformSupplierId=1 HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

**响应示例：**

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "fieldMappings": [
      {
        "id": 12345,
        "platformInterfaceId": 67890,
        "supplierInterfaceId": 54321,
        "platformField": "user.name",
        "supplierField": "customer.fullName",
        "supplementaryLogic": "if (value == null) { value = 'default'; }",
        "mappingInfo": "SCRIPT",
        "operator": "admin",
        "createTime": "2023-10-01T12:00:00",
        "updateTime": "2023-10-02T15:30:00"
      }
    ],
    "platformFieldList": [
      "flightNo",
      "departureTime"
    ],
    "supplierFields": "json字符串",
    "commonLogic": "通用处理逻辑"
  }
}
```

### 6.3 暂存字段映射关系

**接口地址：** `POST /api/field-mapping/save`

**接口描述：** 暂存字段映射关系，不进行最终提交

**请求参数：**

| 参数名               | 类型   | 必填 | 说明                       |
| -------------------- | ------ | ---- | -------------------------- |
| platformInterfaceId  | long   | 是   | 平台接口ID                 |
| supplierInterfaceId  | long   | 是   | 供应商接口ID               |
| commonLogic          | string | 是   | 公共逻辑                   |
| fieldMappingRequests | array  | 是   | 字段映射请求列表（请求体） |

**请求体结构：**

```json
[
  {
    "platformField": "flightNo",
    "supplierField": "flight_no",
    "mappingInfo": "DIRECT",
    "supplementaryLogic": null
  },
  {
    "platformField": "departureTime",
    "supplierField": "dep_time",
    "mappingInfo": "SCRIPT",
    "supplementaryLogic": "格式转换脚本"
  }
]
```

**响应数据：** 操作结果

**请求示例：**

```http
POST /api/field-mapping/save?platformInterfaceId=1&supplierInterfaceId=1&commonLogic=通用处理逻辑 HTTP/1.1
Host: localhost:8080
Content-Type: application/json

[
  {
    "platformField": "flightNo",
    "supplierField": "flight_no",
    "mappingInfo": "DIRECT"
  }
]
```

**响应示例：**

```json
{
  "success": true,
  "message": "暂存字段映射关系成功",
  "data": null
}
```

### 6.4 生成字段映射关系

**接口地址：** `POST /api/field-mapping/generate`

**接口描述：** 生成字段映射关系，进行最终提交

**请求参数：**

| 参数名               | 类型   | 必填 | 说明                       |
| -------------------- | ------ | ---- | -------------------------- |
| platformInterfaceId  | long   | 是   | 平台接口ID                 |
| supplierInterfaceId  | long   | 是   | 供应商接口ID               |
| commonLogic          | string | 是   | 公共逻辑                   |
| fieldMappingRequests | array  | 是   | 字段映射请求列表（请求体） |

**请求体结构：**

```json
[
  {
    "platformField": "flightNo",
    "supplierField": "flight_no",
    "mappingInfo": "DIRECT",
    "supplementaryLogic": null
  },
  {
    "platformField": "departureTime",
    "supplierField": "dep_time",
    "mappingInfo": "SCRIPT",
    "supplementaryLogic": "格式转换脚本"
  }
]
```

**响应数据：** 生成的字段映射详情列表

**请求示例：**

```http
POST /api/field-mapping/generate?platformInterfaceId=1&supplierInterfaceId=1&commonLogic=通用处理逻辑 HTTP/1.1
Host: localhost:8080
Content-Type: application/json

[
  {
    "platformField": "flightNo",
    "supplierField": "flight_no",
    "mappingInfo": "DIRECT"
  }
]
```

**响应示例：**

```json
{
  "success": true,
  "message": "生成字段映射关系成功",
  "data": [
    {
      "id": 1,
      "platformInterfaceId": 1001,
      "supplierInterfaceId": 2002,
      "platformField": "platform_field_example",
      "supplierField": "supplier_field_example",
      "supplementaryLogic": "some_logic",
      "mappingInfo": "DIRECT",
      "operator": "John Doe",
      "createTime": "2023-03-15T10:00:00",
      "updateTime": "2023-03-15T12:00:00"
    }
  ]
}
```

---

## 数据库字段说明

根据 `init.sql` 文件，以下是各表的字段含义：

### 平台接口表 (platform_interface)

| 字段名            | 中文含义           | 类型         | 说明                   |
| ----------------- | ------------------ | ------------ | ---------------------- |
| id                | 主键ID             | BIGINT       | 自增主键               |
| business_domain   | 业务域             | VARCHAR(255) | 如：机票、酒店等       |
| interface_name    | 接口名             | VARCHAR(255) | 接口的名称             |
| interface_version | 版本号             | VARCHAR(50)  | 接口版本               |
| resp_fields       | 平台字段集合       | TEXT         | JSON格式的字段定义     |
| resp_example      | 平台返参示例       | TEXT         | 响应示例               |
| req_doc           | 平台入参文档       | TEXT         | 请求参数文档           |
| resp_doc          | 平台返参文档       | TEXT         | 响应参数文档           |
| req_bean_prompt   | 入参Bean生成Prompt | TEXT         | 用于生成请求Bean的提示 |
| resp_bean_prompt  | 返参Bean生成Prompt | TEXT         | 用于生成响应Bean的提示 |
| code_gen_prompt   | 代码生成Prompt     | TEXT         | 用于代码生成的提示     |
| code_template_doc | 代码模板文档       | TEXT         | 代码模板文档           |
| operator          | 操作人             | VARCHAR(100) | 操作员姓名             |
| create_time       | 创建时间           | TIMESTAMP    | 记录创建时间           |
| update_time       | 更新时间           | TIMESTAMP    | 记录更新时间           |

### 供应接口表 (supplier_interface)

| 字段名          | 中文含义         | 类型         | 说明                    |
| --------------- | ---------------- | ------------ | ----------------------- |
| id              | 主键ID           | BIGINT       | 自增主键                |
| business_domain | 业务域           | VARCHAR(255) | 如：机票、酒店等        |
| supplier_name   | 供应商           | VARCHAR(255) | 供应商名称              |
| interface_name  | 接口名           | VARCHAR(255) | 接口的名称              |
| version         | 版本号           | VARCHAR(50)  | 接口版本                |
| resp_fields     | 供应报文解析集合 | TEXT         | 供应商报文解析逻辑/脚本 |
| req_example     | 供应入参示例     | TEXT         | 供应商请求示例          |
| resp_example    | 供应返参示例     | TEXT         | 供应商响应示例          |
| req_doc         | 供应入参文档     | TEXT         | 供应商请求参数文档      |
| resp_doc        | 供应返参文档     | TEXT         | 供应商响应参数文档      |
| operator        | 操作人           | VARCHAR(100) | 操作员姓名              |
| create_time     | 创建时间         | TIMESTAMP    | 记录创建时间            |
| update_time     | 更新时间         | TIMESTAMP    | 记录更新时间            |

### 平台供应接口映射表 (platform_supplier_mapping)

| 字段名                | 中文含义   | 类型         | 说明             |
| --------------------- | ---------- | ------------ | ---------------- |
| id                    | 主键ID     | BIGINT       | 自增主键         |
| platform_interface_id | 平台接口ID | BIGINT       | 关联平台接口表   |
| supplier_interface_id | 供应接口ID | BIGINT       | 关联供应接口表   |
| common_logic          | 公共逻辑   | TEXT         | 公共处理逻辑脚本 |
| operator              | 操作人     | VARCHAR(100) | 操作员姓名       |
| create_time           | 创建时间   | TIMESTAMP    | 记录创建时间     |
| update_time           | 更新时间   | TIMESTAMP    | 记录更新时间     |

### 字段映射关系表 (field_mapping)

| 字段名                | 中文含义     | 类型         | 说明                                |
| --------------------- | ------------ | ------------ | ----------------------------------- |
| id                    | 主键ID       | BIGINT       | 自增主键                            |
| platform_interface_id | 平台接口ID   | BIGINT       | 关联平台接口表                      |
| supplier_interface_id | 供应接口ID   | BIGINT       | 关联供应接口表                      |
| platform_field        | 平台字段     | VARCHAR(255) | 平台字段名，如：user.name           |
| supplier_field        | 供应字段     | VARCHAR(255) | 供应商字段名，如：customer.fullName |
| supplementary_logic   | 补充逻辑     | TEXT         | 补充逻辑，如：默认值、格式转换脚本  |
| mapping_type          | 映射关系类型 | VARCHAR(100) | 如：DIRECT、CONSTANT、SCRIPT        |
| operator              | 操作人       | VARCHAR(100) | 操作员姓名                          |
| create_time           | 创建时间     | TIMESTAMP    | 记录创建时间                        |
| update_time           | 更新时间     | TIMESTAMP    | 记录更新时间                        |

### Prompt版本管理表 (prompt_version)

| 字段名                | 中文含义           | 类型         | 说明                     |
| --------------------- | ------------------ | ------------ | ------------------------ |
| id                    | 主键ID             | BIGINT       | 自增主键                 |
| platform_interface_id | 平台接口ID         | BIGINT       | 关联平台接口表           |
| supplier_interface_id | 供应接口ID         | BIGINT       | 关联供应接口表（可为空） |
| req_bean_prompt       | 入参Bean生成Prompt | TEXT         | 入参Bean生成提示         |
| resp_bean_prompt      | 返参Bean生成Prompt | TEXT         | 返参Bean生成提示         |
| code_gen_prompt       | 代码生成Prompt     | TEXT         | 代码生成提示             |
| version               | Prompt版本号       | VARCHAR(50)  | Prompt版本               |
| operator              | 操作人             | VARCHAR(100) | 操作员姓名               |
| create_time           | 创建时间           | TIMESTAMP    | 记录创建时间             |
| update_time           | 更新时间           | TIMESTAMP    | 记录更新时间             |

---

## 错误码说明

系统使用HTTP状态码和自定义错误信息来表示不同的错误情况：

- **200 OK**: 请求成功
- **400 Bad Request**: 请求参数错误
- **404 Not Found**: 资源不存在
- **500 Internal Server Error**: 服务器内部错误

错误响应格式：

```json
{
  "success": false,
  "message": "具体错误信息",
  "data": null,
  "code": -1
}
```

---

## 注意事项

1. 所有接口都需要进行适当的参数验证
2. 文件上传接口支持多种文件格式，建议限制文件大小
3. 分页查询默认每页10条记录，最大不超过100条
4. 所有时间字段都使用ISO 8601格式
5. JSON字段需要进行格式验证
6. 删除操作需要检查关联关系，避免数据不一致
7. 版本管理采用递增策略，不支持版本回退
8. 字段映射支持多种映射类型：DIRECT（直接映射）、CONSTANT（常量映射）、SCRIPT（脚本映射）

---