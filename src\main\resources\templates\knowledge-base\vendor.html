<!-- Vendor Knowledge Base Content Fragment -->
<!-- This file contains Thymeleaf fragments and should not have DOCTYPE or html tags -->
<div th:fragment="content">
    <!-- Search and Filter Section -->
    <div class="search-filters">
        <form id="vendorSearchForm" onsubmit="return false;">
            <div class="filter-row">
                <div th:replace="~{fragments/components :: form-select('vendorBusinessDomain', 'businessDomain', '业务领域', ${businessDomains}, '', '请选择业务领域', false)}"></div>
                <div th:replace="~{fragments/components :: form-select('vendorName', 'vendor', '供应商', ${vendors}, '', '请选择供应商', false)}"></div>
                <div th:replace="~{fragments/components :: form-select('vendorInterface', 'interfaceName', '接口', ${interfaces}, '', '请选择接口', false)}"></div>
                <div th:replace="~{fragments/components :: form-select('vendorVersion', 'version', '版本', ${versions}, '', '请选择版本', false)}"></div>
            </div>
            <div class="filter-actions">
                <button type="button" class="btn btn-primary" onclick="searchVendorKnowledge()">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    查询
                </button>
                <button type="button" class="btn btn-success" data-action="show-modal" data-target="addVendorKnowledgeModal">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    添加知识
                </button>
            </div>
        </form>
    </div>
    
    <!-- Data Table -->
    <div class="data-table-container">
        <table class="data-table" id="vendorKnowledgeTable">
            <thead>
                <tr>
                    <th>业务领域</th>
                    <th>供应商</th>
                    <th>接口</th>
                    <th>版本</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="vendorKnowledgeTableBody">
                <!-- Data will be loaded via JavaScript -->
            </tbody>
        </table>
        
        <!-- Empty state -->
        <div id="vendorEmptyState" class="empty-state" style="display: none;">
            <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="empty-state-title">暂无供应商知识库数据</h3>
            <p class="empty-state-description">当前没有符合条件的供应商知识库记录</p>
            <button class="btn btn-primary" data-action="show-modal" data-target="addVendorKnowledgeModal">
                添加第一个知识库
            </button>
        </div>
        
        <!-- Pagination -->
        <div class="pagination" id="vendorPagination" style="display: none;">
            <div class="pagination-info" id="vendorPaginationInfo"></div>
            <div class="pagination-controls" id="vendorPaginationControls"></div>
        </div>
    </div>
</div>

<!-- Modals -->
<div th:fragment="modals">
    <!-- Add Vendor Knowledge Modal -->
    <div id="addVendorKnowledgeModal" class="modal modal-lg">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">添加供应商知识</h3>
                    <button type="button" class="modal-close" data-action="hide-modal" data-target="addVendorKnowledgeModal">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addVendorKnowledgeForm">
                        <div class="filter-row">
                            <div th:replace="~{fragments/components :: form-input('addVendorBusinessDomain', 'businessDomain', '业务领域', 'text', '', '请输入业务领域', true, false, 100)}"></div>
                            <div th:replace="~{fragments/components :: form-input('addVendorName', 'vendor', '供应商', 'text', '', '请输入供应商名称', true, false, 100)}"></div>
                        </div>
                        
                        <div class="filter-row">
                            <div th:replace="~{fragments/components :: form-input('addVendorInterface', 'interfaceName', '接口', 'text', '', '请输入接口名称', true, false, 100)}"></div>
                            <div th:replace="~{fragments/components :: form-input('addVendorVersion', 'version', '版本', 'text', '', '请输入版本号', true, false, 50)}"></div>
                        </div>
                        
                        <div class="filter-row">
                            <div th:replace="~{fragments/components :: file-upload('inputExampleFile', 'inputExampleFile', '输入示例文件', '.txt,.json,.xml', false)}"></div>
                            <div th:replace="~{fragments/components :: file-upload('outputExampleFile', 'outputExampleFile', '输出示例文件', '.txt,.json,.xml', false)}"></div>
                        </div>
                        
                        <div class="filter-row">
                            <div th:replace="~{fragments/components :: file-upload('inputDocumentFile', 'inputDocumentFile', '输入文档文件', '.txt,.md,.pdf', false)}"></div>
                            <div th:replace="~{fragments/components :: file-upload('outputDocumentFile', 'outputDocumentFile', '输出文档文件', '.txt,.md,.pdf', false)}"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-action="hide-modal" data-target="addVendorKnowledgeModal">
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveVendorKnowledge()">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- View Vendor Documents Modal -->
    <div id="viewVendorDocumentsModal" class="modal modal-xl">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">查看供应商文档</h3>
                    <button type="button" class="modal-close" data-action="hide-modal" data-target="viewVendorDocumentsModal">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Knowledge base info -->
                    <div class="filter-row mb-4">
                        <div class="form-group">
                            <label class="form-label">业务领域</label>
                            <div class="form-control" id="viewVendorBusinessDomain" readonly></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">供应商</label>
                            <div class="form-control" id="viewVendorName" readonly></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">接口</label>
                            <div class="form-control" id="viewVendorInterface" readonly></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">版本</label>
                            <div class="form-control" id="viewVendorVersion" readonly></div>
                        </div>
                    </div>
                    
                    <!-- Document cards -->
                    <div class="document-cards" id="vendorDocumentCards">
                        <!-- Document cards will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-action="hide-modal" data-target="viewVendorDocumentsModal">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Confirm Delete Modal -->
    <div id="confirmDeleteVendorModal" class="modal modal-sm">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">确认删除</h3>
                    <button type="button" class="modal-close" data-action="hide-modal" data-target="confirmDeleteVendorModal">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                    <p>确定要删除这个供应商知识库记录吗？此操作不可撤销。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-action="hide-modal" data-target="confirmDeleteVendorModal">
                        取消
                    </button>
                    <button type="button" class="btn btn-error" onclick="confirmDeleteVendorKnowledge()">
                        确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
