package com.mappinggen.controller.api;

import com.mappinggen.dto.ApiResponse;
import com.mappinggen.model.CodePrompt;
import com.mappinggen.model.PromptFile;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * REST API controller for CodePrompt operations
 */
@RestController
@RequestMapping("/api/code-prompt")
public class CodePromptApiController {

    // Mock data storage
    private static final Map<Long, CodePrompt> codePromptStorage = new HashMap<>();
    private static Long nextId = 1L;
    private static Long nextFileId = 1L;

    static {
        initializeMockData();
    }

    private static void initializeMockData() {
        CodePrompt prompt1 = new CodePrompt(1L, "航班搜索");
        prompt1.setId(nextId++);
        prompt1.setPlatformInterface("FlightSearch");
        prompt1.setPlatformVersion("v1.0");
        prompt1.setVendor("东方航空");
        prompt1.setVendorInterface("FlightSearch");
        prompt1.setVendorVersion("v1.0");
        prompt1.setPromptVersion("v1.0");

        List<PromptFile> files1 = Arrays.asList(
            new PromptFile("request_prompt.txt", "请求提示", 
                "// 航班搜索请求提示\n" +
                "public class FlightSearchRequest {\n" +
                "    private String departureAirport;\n" +
                "    private String arrivalAirport;\n" +
                "    private LocalDate departureDate;\n" +
                "    \n" +
                "    // 构造函数和getter/setter方法\n" +
                "}", true),
            new PromptFile("response_prompt.txt", "响应提示",
                "// 航班搜索响应提示\n" +
                "public class FlightSearchResponse {\n" +
                "    private List<Flight> flights;\n" +
                "    private int total;\n" +
                "    \n" +
                "    // 构造函数和getter/setter方法\n" +
                "}", true),
            new PromptFile("mapping_code.java", "映射代码",
                "// 字段映射代码\n" +
                "public class FlightMapper {\n" +
                "    public Flight mapToFlight(Map<String, Object> vendorData) {\n" +
                "        Flight flight = new Flight();\n" +
                "        flight.setFlightNumber((String) vendorData.get(\"flightNumber\"));\n" +
                "        \n" +
                "        Map<String, Object> departure = (Map<String, Object>) vendorData.get(\"departure\");\n" +
                "        flight.setDepartureAirport((String) departure.get(\"airport\"));\n" +
                "        flight.setDepartureTime(parseDateTime((String) departure.get(\"time\")));\n" +
                "        \n" +
                "        Map<String, Object> arrival = (Map<String, Object>) vendorData.get(\"arrival\");\n" +
                "        flight.setArrivalAirport((String) arrival.get(\"airport\"));\n" +
                "        flight.setArrivalTime(parseDateTime((String) arrival.get(\"time\")));\n" +
                "        \n" +
                "        Map<String, Object> price = (Map<String, Object>) vendorData.get(\"price\");\n" +
                "        flight.setPrice(((Number) price.get(\"amount\")).doubleValue());\n" +
                "        flight.setCurrency((String) price.get(\"currency\"));\n" +
                "        \n" +
                "        return flight;\n" +
                "    }\n" +
                "    \n" +
                "    private LocalDateTime parseDateTime(String dateTimeStr) {\n" +
                "        // 时间解析逻辑\n" +
                "        return LocalDateTime.parse(dateTimeStr);\n" +
                "    }\n" +
                "}", true)
        );

        // Set IDs for prompt files
        setPromptFileIds(files1);
        prompt1.setPromptFiles(files1);
        codePromptStorage.put(prompt1.getId(), prompt1);

        // Add another example
        CodePrompt prompt2 = new CodePrompt(2L, "订单管理");
        prompt2.setId(nextId++);
        prompt2.setPlatformInterface("OrderManagement");
        prompt2.setPlatformVersion("v2.1");
        prompt2.setVendor("东方航空");
        prompt2.setVendorInterface("OrderProcess");
        prompt2.setVendorVersion("v1.5");
        prompt2.setPromptVersion("v1.0");

        List<PromptFile> files2 = Arrays.asList(
            new PromptFile("order_mapping.java", "订单映射代码",
                "// 订单映射代码\n" +
                "public class OrderMapper {\n" +
                "    public Order mapToOrder(Map<String, Object> vendorData) {\n" +
                "        Order order = new Order();\n" +
                "        order.setOrderId((String) vendorData.get(\"orderId\"));\n" +
                "        order.setStatus((String) vendorData.get(\"status\"));\n" +
                "        order.setTotalAmount(((Number) vendorData.get(\"totalAmount\")).doubleValue());\n" +
                "        return order;\n" +
                "    }\n" +
                "}", true)
        );

        setPromptFileIds(files2);
        prompt2.setPromptFiles(files2);
        prompt2.setCreateTime(LocalDateTime.now().minusDays(6));
        codePromptStorage.put(prompt2.getId(), prompt2);

        // Payment Processing - 东方航空 CodePrompt
        CodePrompt prompt3 = new CodePrompt(7L, "支付处理");
        prompt3.setId(nextId++);
        prompt3.setPlatformInterface("PaymentProcess");
        prompt3.setPlatformVersion("v1.0");
        prompt3.setVendor("东方航空");
        prompt3.setVendorInterface("PaymentGateway");
        prompt3.setVendorVersion("v2.0");
        prompt3.setPromptVersion("v1.0");

        List<PromptFile> files3 = Arrays.asList(
            new PromptFile("payment_request.java", "支付请求映射",
                "/**\n" +
                " * 东方航空支付处理请求映射\n" +
                " */\n" +
                "@Component\n" +
                "public class ChinaEasternPaymentMapper {\n" +
                "    \n" +
                "    public ChinaEasternPaymentRequest mapPaymentRequest(PaymentRequest platformRequest) {\n" +
                "        ChinaEasternPaymentRequest request = new ChinaEasternPaymentRequest();\n" +
                "        \n" +
                "        request.setOrderId(platformRequest.getOrderId());\n" +
                "        request.setAmount(platformRequest.getAmount().multiply(new BigDecimal(\"100\")));\n" +
                "        request.setCurrency(platformRequest.getCurrency());\n" +
                "        request.setPaymentMethod(mapPaymentMethod(platformRequest.getPaymentMethod()));\n" +
                "        \n" +
                "        // 东航特有字段\n" +
                "        request.setMerchantId(\"CE_MERCHANT_001\");\n" +
                "        request.setNotifyUrl(\"https://platform.ifs.com/payment/notify\");\n" +
                "        request.setReturnUrl(\"https://platform.ifs.com/payment/return\");\n" +
                "        \n" +
                "        return request;\n" +
                "    }\n" +
                "    \n" +
                "    private String mapPaymentMethod(String platformMethod) {\n" +
                "        Map<String, String> methodMap = Map.of(\n" +
                "            \"CREDIT_CARD\", \"CC\",\n" +
                "            \"ALIPAY\", \"ALIPAY\",\n" +
                "            \"WECHAT_PAY\", \"WXPAY\",\n" +
                "            \"BANK_TRANSFER\", \"BANK\"\n" +
                "        );\n" +
                "        return methodMap.getOrDefault(platformMethod, \"CC\");\n" +
                "    }\n" +
                "}", true),

            new PromptFile("payment_response.java", "支付响应映射",
                "/**\n" +
                " * 东方航空支付处理响应映射\n" +
                " */\n" +
                "@Component\n" +
                "public class ChinaEasternPaymentResponseMapper {\n" +
                "    \n" +
                "    public PaymentResponse mapPaymentResponse(ChinaEasternPaymentResponse ceResponse) {\n" +
                "        PaymentResponse response = new PaymentResponse();\n" +
                "        \n" +
                "        response.setSuccess(\"SUCCESS\".equals(ceResponse.getStatus()));\n" +
                "        response.setTransactionId(ceResponse.getTransactionId());\n" +
                "        response.setPaymentStatus(mapPaymentStatus(ceResponse.getPaymentStatus()));\n" +
                "        response.setAmount(ceResponse.getAmount().divide(new BigDecimal(\"100\")));\n" +
                "        response.setCurrency(ceResponse.getCurrency());\n" +
                "        \n" +
                "        if (ceResponse.getErrorInfo() != null) {\n" +
                "            response.setErrorCode(ceResponse.getErrorInfo().getCode());\n" +
                "            response.setErrorMessage(ceResponse.getErrorInfo().getMessage());\n" +
                "        }\n" +
                "        \n" +
                "        return response;\n" +
                "    }\n" +
                "    \n" +
                "    private String mapPaymentStatus(String ceStatus) {\n" +
                "        Map<String, String> statusMap = Map.of(\n" +
                "            \"PAID\", \"SUCCESS\",\n" +
                "            \"PENDING\", \"PENDING\",\n" +
                "            \"FAILED\", \"FAILED\",\n" +
                "            \"CANCELLED\", \"CANCELLED\"\n" +
                "        );\n" +
                "        return statusMap.getOrDefault(ceStatus, \"UNKNOWN\");\n" +
                "    }\n" +
                "}", true),

            new PromptFile("payment_models.java", "支付数据模型",
                "/**\n" +
                " * 东方航空支付相关数据模型\n" +
                " */\n" +
                "public class ChinaEasternPaymentRequest {\n" +
                "    private String orderId;\n" +
                "    private BigDecimal amount;  // 以分为单位\n" +
                "    private String currency;\n" +
                "    private String paymentMethod;\n" +
                "    private String merchantId;\n" +
                "    private String notifyUrl;\n" +
                "    private String returnUrl;\n" +
                "    \n" +
                "    // getters and setters\n" +
                "}\n" +
                "\n" +
                "public class ChinaEasternPaymentResponse {\n" +
                "    private String status;\n" +
                "    private String transactionId;\n" +
                "    private String paymentStatus;\n" +
                "    private BigDecimal amount;\n" +
                "    private String currency;\n" +
                "    private ErrorInfo errorInfo;\n" +
                "    \n" +
                "    public static class ErrorInfo {\n" +
                "        private String code;\n" +
                "        private String message;\n" +
                "    }\n" +
                "}", true)
        );

        setPromptFileIds(files3);
        prompt3.setPromptFiles(files3);
        prompt3.setCreateTime(LocalDateTime.now().minusDays(3));
        codePromptStorage.put(prompt3.getId(), prompt3);

        // User Management - 春秋航空 CodePrompt
        CodePrompt prompt4 = new CodePrompt(15L, "用户管理");
        prompt4.setId(nextId++);
        prompt4.setPlatformInterface("UserManagement");
        prompt4.setPlatformVersion("v1.2");
        prompt4.setVendor("春秋航空");
        prompt4.setVendorInterface("UserService");
        prompt4.setVendorVersion("v1.0");
        prompt4.setPromptVersion("v1.0");

        List<PromptFile> files4 = Arrays.asList(
            new PromptFile("user_sync.java", "用户同步映射",
                "/**\n" +
                " * 春秋航空用户管理同步映射\n" +
                " */\n" +
                "@Component\n" +
                "public class SpringAirlinesUserMapper {\n" +
                "    \n" +
                "    public SpringAirlinesUserRequest mapUserSyncRequest(UserSyncRequest platformRequest) {\n" +
                "        SpringAirlinesUserRequest request = new SpringAirlinesUserRequest();\n" +
                "        \n" +
                "        request.setUserId(platformRequest.getUserId());\n" +
                "        request.setUserType(mapUserType(platformRequest.getUserType()));\n" +
                "        request.setPersonalInfo(mapPersonalInfo(platformRequest.getPersonalInfo()));\n" +
                "        request.setContactInfo(mapContactInfo(platformRequest.getContactInfo()));\n" +
                "        \n" +
                "        // 春秋航空特有字段\n" +
                "        request.setMembershipLevel(determineMembershipLevel(platformRequest));\n" +
                "        request.setPreferences(mapPreferences(platformRequest.getPreferences()));\n" +
                "        \n" +
                "        return request;\n" +
                "    }\n" +
                "    \n" +
                "    private String mapUserType(String platformType) {\n" +
                "        Map<String, String> typeMap = Map.of(\n" +
                "            \"INDIVIDUAL\", \"PERSONAL\",\n" +
                "            \"CORPORATE\", \"BUSINESS\",\n" +
                "            \"VIP\", \"PREMIUM\"\n" +
                "        );\n" +
                "        return typeMap.getOrDefault(platformType, \"PERSONAL\");\n" +
                "    }\n" +
                "    \n" +
                "    private String determineMembershipLevel(UserSyncRequest request) {\n" +
                "        // 根据用户信息确定会员等级\n" +
                "        if (request.getLoyaltyPoints() != null) {\n" +
                "            if (request.getLoyaltyPoints() > 50000) return \"GOLD\";\n" +
                "            if (request.getLoyaltyPoints() > 20000) return \"SILVER\";\n" +
                "        }\n" +
                "        return \"BRONZE\";\n" +
                "    }\n" +
                "}", true)
        );

        setPromptFileIds(files4);
        prompt4.setPromptFiles(files4);
        prompt4.setCreateTime(LocalDateTime.now().minusDays(1));
        codePromptStorage.put(prompt4.getId(), prompt4);

        // Add more test data for better demonstration
        CodePrompt prompt5 = new CodePrompt(5L, "支付处理");
        prompt5.setId(nextId++);
        prompt5.setPlatformInterface("PaymentProcess");
        prompt5.setPlatformVersion("v2.0");
        prompt5.setVendor("南方航空");
        prompt5.setVendorInterface("PaymentGateway");
        prompt5.setVendorVersion("v1.2");
        prompt5.setPromptVersion("v1.1");

        List<PromptFile> files5 = Arrays.asList(
            new PromptFile("payment_request.java", "支付请求映射",
                "/**\n" +
                " * 南方航空支付处理请求映射\n" +
                " */\n" +
                "@Component\n" +
                "public class ChinaSouthernPaymentMapper {\n" +
                "    \n" +
                "    public ChinaSouthernPaymentRequest mapPaymentRequest(PaymentRequest platformRequest) {\n" +
                "        ChinaSouthernPaymentRequest request = new ChinaSouthernPaymentRequest();\n" +
                "        \n" +
                "        request.setOrderId(platformRequest.getOrderId());\n" +
                "        request.setAmount(platformRequest.getAmount());\n" +
                "        request.setCurrency(platformRequest.getCurrency());\n" +
                "        request.setPaymentMethod(mapPaymentMethod(platformRequest.getPaymentMethod()));\n" +
                "        \n" +
                "        // 南方航空特有字段\n" +
                "        request.setMerchantId(\"CSN_MERCHANT_001\");\n" +
                "        request.setChannelCode(\"CSN_PAY\");\n" +
                "        \n" +
                "        return request;\n" +
                "    }\n" +
                "    \n" +
                "    private String mapPaymentMethod(String platformMethod) {\n" +
                "        switch (platformMethod) {\n" +
                "            case \"CREDIT_CARD\": return \"CC\";\n" +
                "            case \"ALIPAY\": return \"AP\";\n" +
                "            case \"WECHAT_PAY\": return \"WX\";\n" +
                "            default: return \"CC\";\n" +
                "        }\n" +
                "    }\n" +
                "}", true),
            new PromptFile("payment_response.java", "支付响应映射",
                "/**\n" +
                " * 南方航空支付响应映射\n" +
                " */\n" +
                "public PaymentResponse mapPaymentResponse(ChinaSouthernPaymentResponse vendorResponse) {\n" +
                "    PaymentResponse response = new PaymentResponse();\n" +
                "    \n" +
                "    response.setTransactionId(vendorResponse.getTransactionId());\n" +
                "    response.setStatus(mapPaymentStatus(vendorResponse.getStatus()));\n" +
                "    response.setAmount(vendorResponse.getAmount());\n" +
                "    response.setTimestamp(vendorResponse.getPaymentTime());\n" +
                "    \n" +
                "    if (vendorResponse.getErrorCode() != null) {\n" +
                "        response.setErrorCode(vendorResponse.getErrorCode());\n" +
                "        response.setErrorMessage(vendorResponse.getErrorMessage());\n" +
                "    }\n" +
                "    \n" +
                "    return response;\n" +
                "}\n" +
                "\n" +
                "private String mapPaymentStatus(String vendorStatus) {\n" +
                "    switch (vendorStatus) {\n" +
                "        case \"SUCCESS\": return \"COMPLETED\";\n" +
                "        case \"FAILED\": return \"FAILED\";\n" +
                "        case \"PENDING\": return \"PROCESSING\";\n" +
                "        default: return \"UNKNOWN\";\n" +
                "    }\n" +
                "}", true)
        );

        setPromptFileIds(files5);
        prompt5.setPromptFiles(files5);
        prompt5.setCreateTime(LocalDateTime.now().minusDays(2));
        codePromptStorage.put(prompt5.getId(), prompt5);

        CodePrompt prompt6 = new CodePrompt(6L, "航班搜索");
        prompt6.setId(nextId++);
        prompt6.setPlatformInterface("FlightSearch");
        prompt6.setPlatformVersion("v2.1");
        prompt6.setVendor("海南航空");
        prompt6.setVendorInterface("FlightSearch");
        prompt6.setVendorVersion("v1.3");
        prompt6.setPromptVersion("v2.0");

        List<PromptFile> files6 = Arrays.asList(
            new PromptFile("flight_search_advanced.java", "高级航班搜索映射",
                "/**\n" +
                " * 海南航空高级航班搜索映射 - v2.0版本\n" +
                " * 支持多段行程和复杂搜索条件\n" +
                " */\n" +
                "@Component\n" +
                "public class HainanAirlinesAdvancedSearchMapper {\n" +
                "    \n" +
                "    public HainanFlightSearchRequest mapAdvancedSearchRequest(FlightSearchRequest platformRequest) {\n" +
                "        HainanFlightSearchRequest request = new HainanFlightSearchRequest();\n" +
                "        \n" +
                "        // 基础搜索条件\n" +
                "        request.setOrigin(platformRequest.getDepartureAirport());\n" +
                "        request.setDestination(platformRequest.getArrivalAirport());\n" +
                "        request.setDepartureDate(platformRequest.getDepartureDate());\n" +
                "        \n" +
                "        // 高级搜索选项\n" +
                "        if (platformRequest.getReturnDate() != null) {\n" +
                "            request.setReturnDate(platformRequest.getReturnDate());\n" +
                "            request.setTripType(\"ROUND_TRIP\");\n" +
                "        } else {\n" +
                "            request.setTripType(\"ONE_WAY\");\n" +
                "        }\n" +
                "        \n" +
                "        // 乘客信息\n" +
                "        request.setAdultCount(platformRequest.getPassengerCounts().getAdults());\n" +
                "        request.setChildCount(platformRequest.getPassengerCounts().getChildren());\n" +
                "        request.setInfantCount(platformRequest.getPassengerCounts().getInfants());\n" +
                "        \n" +
                "        // 海南航空特有参数\n" +
                "        request.setCabinClass(mapCabinClass(platformRequest.getCabinClass()));\n" +
                "        request.setFlexibleDates(platformRequest.isFlexibleDates());\n" +
                "        request.setDirectFlightsOnly(platformRequest.isDirectFlightsOnly());\n" +
                "        \n" +
                "        return request;\n" +
                "    }\n" +
                "    \n" +
                "    private String mapCabinClass(String platformCabin) {\n" +
                "        switch (platformCabin) {\n" +
                "            case \"ECONOMY\": return \"Y\";\n" +
                "            case \"PREMIUM_ECONOMY\": return \"W\";\n" +
                "            case \"BUSINESS\": return \"C\";\n" +
                "            case \"FIRST\": return \"F\";\n" +
                "            default: return \"Y\";\n" +
                "        }\n" +
                "    }\n" +
                "}", true),
            new PromptFile("flight_response_advanced.java", "高级航班响应映射",
                "/**\n" +
                " * 海南航空航班搜索响应映射 - v2.0版本\n" +
                " */\n" +
                "public FlightSearchResponse mapAdvancedSearchResponse(HainanFlightSearchResponse vendorResponse) {\n" +
                "    FlightSearchResponse response = new FlightSearchResponse();\n" +
                "    \n" +
                "    List<Flight> flights = vendorResponse.getFlights().stream()\n" +
                "        .map(this::mapFlight)\n" +
                "        .collect(Collectors.toList());\n" +
                "    \n" +
                "    response.setFlights(flights);\n" +
                "    response.setTotal(vendorResponse.getTotalCount());\n" +
                "    response.setSearchId(vendorResponse.getSearchId());\n" +
                "    \n" +
                "    // 添加搜索元数据\n" +
                "    SearchMetadata metadata = new SearchMetadata();\n" +
                "    metadata.setSearchTime(vendorResponse.getSearchTime());\n" +
                "    metadata.setResultCount(flights.size());\n" +
                "    metadata.setHasMoreResults(vendorResponse.hasMoreResults());\n" +
                "    response.setMetadata(metadata);\n" +
                "    \n" +
                "    return response;\n" +
                "}\n" +
                "\n" +
                "private Flight mapFlight(HainanFlight vendorFlight) {\n" +
                "    Flight flight = new Flight();\n" +
                "    \n" +
                "    flight.setFlightNumber(vendorFlight.getFlightNumber());\n" +
                "    flight.setAirline(\"海南航空\");\n" +
                "    flight.setAirlineCode(\"HU\");\n" +
                "    \n" +
                "    // 航班时间信息\n" +
                "    flight.setDepartureTime(vendorFlight.getDepartureTime());\n" +
                "    flight.setArrivalTime(vendorFlight.getArrivalTime());\n" +
                "    flight.setDuration(vendorFlight.getFlightDuration());\n" +
                "    \n" +
                "    // 机场信息\n" +
                "    flight.setDepartureAirport(vendorFlight.getOriginAirport());\n" +
                "    flight.setArrivalAirport(vendorFlight.getDestinationAirport());\n" +
                "    \n" +
                "    // 价格和舱位信息\n" +
                "    flight.setPrice(vendorFlight.getBasePrice());\n" +
                "    flight.setTaxes(vendorFlight.getTaxes());\n" +
                "    flight.setAvailableSeats(vendorFlight.getAvailableSeats());\n" +
                "    \n" +
                "    return flight;\n" +
                "}", true)
        );

        setPromptFileIds(files6);
        prompt6.setPromptFiles(files6);
        prompt6.setCreateTime(LocalDateTime.now().minusHours(6));
        codePromptStorage.put(prompt6.getId(), prompt6);
    }

    // Dropdown endpoints removed - now using unified /api/init-data endpoint

    @GetMapping("/versions")
    public ApiResponse<List<String>> getPromptVersions(
            @RequestParam(required = false) String businessDomain,
            @RequestParam(required = false) String platformInterface,
            @RequestParam(required = false) String vendor) {

        // Get available prompt versions based on search criteria
        Set<String> versions = new HashSet<>();

        for (CodePrompt prompt : codePromptStorage.values()) {
            boolean matches = true;

            if (businessDomain != null && !businessDomain.equals(prompt.getBusinessDomain())) {
                matches = false;
            }
            if (platformInterface != null && !platformInterface.equals(prompt.getPlatformInterface())) {
                matches = false;
            }
            if (vendor != null && !vendor.equals(prompt.getVendor())) {
                matches = false;
            }

            if (matches && prompt.getPromptVersion() != null) {
                versions.add(prompt.getPromptVersion());
            }
        }

        // Sort versions in descending order (latest first)
        List<String> sortedVersions = versions.stream()
            .sorted((v1, v2) -> v2.compareTo(v1))
            .collect(Collectors.toList());

        return ApiResponse.success(sortedVersions);
    }

    @GetMapping("/latest")
    public ApiResponse<CodePrompt> getLatestCodePrompt(
            @RequestParam(required = false) String businessDomain,
            @RequestParam(required = false) String platformInterface,
            @RequestParam(required = false) String platformVersion,
            @RequestParam(required = false) String vendor,
            @RequestParam(required = false) String vendorInterface,
            @RequestParam(required = false) String vendorVersion) {

        // Find the latest version for the given criteria
        CodePrompt latestPrompt = null;
        String latestVersion = null;

        for (CodePrompt prompt : codePromptStorage.values()) {
            boolean matches = true;

            if (businessDomain != null && !businessDomain.equals(prompt.getBusinessDomain())) {
                matches = false;
            }
            if (platformInterface != null && !platformInterface.equals(prompt.getPlatformInterface())) {
                matches = false;
            }
            if (platformVersion != null && !platformVersion.equals(prompt.getPlatformVersion())) {
                matches = false;
            }
            if (vendor != null && !vendor.equals(prompt.getVendor())) {
                matches = false;
            }
            if (vendorInterface != null && !vendorInterface.equals(prompt.getVendorInterface())) {
                matches = false;
            }
            if (vendorVersion != null && !vendorVersion.equals(prompt.getVendorVersion())) {
                matches = false;
            }

            if (matches) {
                String currentVersion = prompt.getPromptVersion();
                if (latestVersion == null || (currentVersion != null && currentVersion.compareTo(latestVersion) > 0)) {
                    latestVersion = currentVersion;
                    latestPrompt = prompt;
                }
            }
        }

        if (latestPrompt == null) {
            return ApiResponse.error("未找到匹配的 CodePrompt");
        }

        return ApiResponse.success(latestPrompt);
    }

    @GetMapping
    public ApiResponse<CodePrompt> getCodePrompt(
            @RequestParam(required = false) String businessDomain,
            @RequestParam(required = false) String platformInterface,
            @RequestParam(required = false) String platformVersion,
            @RequestParam(required = false) String vendor,
            @RequestParam(required = false) String vendorInterface,
            @RequestParam(required = false) String vendorVersion,
            @RequestParam(required = false) String promptVersion) {

        // Find matching code prompt
        CodePrompt prompt = codePromptStorage.values().stream()
                .filter(cp -> businessDomain == null || businessDomain.equals(cp.getBusinessDomain()))
                .filter(cp -> platformInterface == null || platformInterface.equals(cp.getPlatformInterface()))
                .filter(cp -> platformVersion == null || platformVersion.equals(cp.getPlatformVersion()))
                .filter(cp -> vendor == null || vendor.equals(cp.getVendor()))
                .filter(cp -> vendorInterface == null || vendorInterface.equals(cp.getVendorInterface()))
                .filter(cp -> vendorVersion == null || vendorVersion.equals(cp.getVendorVersion()))
                .filter(cp -> promptVersion == null || promptVersion.equals(cp.getPromptVersion()))
                .findFirst()
                .orElse(null);

        if (prompt == null) {
            return ApiResponse.error("未找到匹配的 CodePrompt");
        }

        return ApiResponse.success(prompt);
    }

    @GetMapping("/{id}")
    public ApiResponse<CodePrompt> getCodePromptById(@PathVariable Long id) {
        CodePrompt prompt = codePromptStorage.get(id);
        if (prompt == null) {
            return ApiResponse.error("CodePrompt 不存在");
        }
        return ApiResponse.success(prompt);
    }

    @PutMapping("/{id}")
    public ApiResponse<Void> updateCodePrompt(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        CodePrompt prompt = codePromptStorage.get(id);
        if (prompt == null) {
            return ApiResponse.error("CodePrompt 不存在");
        }

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> promptFiles = (List<Map<String, Object>>) request.get("promptFiles");

        if (promptFiles != null && !promptFiles.isEmpty()) {
            List<PromptFile> currentFiles = prompt.getPromptFiles();

            // Update prompt files content
            for (int i = 0; i < promptFiles.size() && i < currentFiles.size(); i++) {
                Map<String, Object> fileData = promptFiles.get(i);
                String newContent = (String) fileData.get("content");

                if (newContent != null) {
                    PromptFile currentFile = currentFiles.get(i);

                    // Try to match by ID first (if available)
                    Object idObj = fileData.get("id");
                    boolean updated = false;

                    if (idObj != null && idObj instanceof Number) {
                        Long fileId = ((Number) idObj).longValue();
                        // Find and update by ID
                        for (PromptFile file : currentFiles) {
                            if (file.getId() != null && file.getId().equals(fileId)) {
                                file.setContent(newContent);
                                file.setUpdateTime(LocalDateTime.now());
                                updated = true;
                                break;
                            }
                        }
                    }

                    // Fallback: update by index if ID matching failed
                    if (!updated) {
                        currentFile.setContent(newContent);
                        currentFile.setUpdateTime(LocalDateTime.now());
                    }
                }
            }
        }

        prompt.setUpdateTime(LocalDateTime.now());

        return ApiResponse.success("CodePrompt 更新成功");
    }

    @PostMapping
    public ApiResponse<CodePrompt> createCodePrompt(@RequestBody CodePrompt codePrompt) {
        codePrompt.setId(nextId++);
        codePrompt.setCreateTime(LocalDateTime.now());
        codePrompt.setUpdateTime(LocalDateTime.now());

        // Initialize prompt files if not provided
        if (codePrompt.getPromptFiles() == null || codePrompt.getPromptFiles().isEmpty()) {
            List<PromptFile> defaultFiles = createDefaultPromptFiles(codePrompt.getBusinessDomain());
            codePrompt.setPromptFiles(defaultFiles);
        } else {
            // Set IDs for existing files that don't have them
            setPromptFileIds(codePrompt.getPromptFiles());
        }

        codePromptStorage.put(codePrompt.getId(), codePrompt);

        return ApiResponse.success("CodePrompt 创建成功", codePrompt);
    }

    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteCodePrompt(@PathVariable Long id) {
        if (codePromptStorage.remove(id) != null) {
            return ApiResponse.success("删除成功");
        }
        return ApiResponse.error("CodePrompt 不存在");
    }

    private List<PromptFile> createDefaultPromptFiles(String businessDomain) {
        List<PromptFile> files = new ArrayList<>();

        if ("航班搜索".equals(businessDomain)) {
            files.add(new PromptFile("request_prompt.txt", "请求提示", "// 请求提示内容", true));
            files.add(new PromptFile("response_prompt.txt", "响应提示", "// 响应提示内容", true));
            files.add(new PromptFile("mapping_code.java", "映射代码", "// 映射代码内容", true));
        } else if ("订单管理".equals(businessDomain)) {
            files.add(new PromptFile("order_mapping.java", "订单映射代码", "// 订单映射代码内容", true));
            files.add(new PromptFile("order_validation.java", "订单验证代码", "// 订单验证代码内容", true));
        } else {
            files.add(new PromptFile("default_mapping.java", "默认映射代码", "// 默认映射代码内容", true));
        }

        // Set IDs for the created files
        setPromptFileIds(files);

        return files;
    }

    /**
     * Helper method to set IDs for PromptFile objects
     */
    private static void setPromptFileIds(List<PromptFile> files) {
        if (files != null) {
            for (PromptFile file : files) {
                if (file.getId() == null) {
                    file.setId(nextFileId++);
                }
            }
        }
    }
}
