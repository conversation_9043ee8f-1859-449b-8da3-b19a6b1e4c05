package com.mappinggen.exception;

import com.mappinggen.dto.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@ControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 处理验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleValidationException(
            MethodArgumentNotValidException ex) {
        
        logger.warn("数据验证失败: {}", ex.getMessage());
        
        Map<String, Object> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        
        return ResponseEntity.badRequest()
            .body(ApiResponse.error("数据验证失败", errors));
    }
    
    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiResponse<Map<String, Object>>> handleBindException(BindException ex) {
        logger.warn("数据绑定失败: {}", ex.getMessage());
        
        Map<String, Object> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        
        return ResponseEntity.badRequest()
            .body(ApiResponse.error("数据绑定失败", errors));
    }
    
    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiResponse<String>> handleMaxUploadSizeExceededException(
            MaxUploadSizeExceededException ex) {
        
        logger.warn("文件上传大小超限: {}", ex.getMessage());
        
        return ResponseEntity.badRequest()
            .body(ApiResponse.error("文件大小超过限制，最大支持10MB"));
    }
    
    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiResponse<String>> handleIllegalArgumentException(
            IllegalArgumentException ex) {
        
        logger.warn("非法参数: {}", ex.getMessage());
        
        return ResponseEntity.badRequest()
            .body(ApiResponse.error("参数错误: " + ex.getMessage()));
    }
    
    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleNullPointerException(
            NullPointerException ex, HttpServletRequest request) {
        
        logger.error("空指针异常 - URL: {}", request.getRequestURL(), ex);
        
        return ResponseEntity.internalServerError()
            .body(ApiResponse.error("系统内部错误，请稍后重试"));
    }
    
    /**
     * 处理通用运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiResponse<String>> handleRuntimeException(
            RuntimeException ex, HttpServletRequest request) {
        
        logger.error("运行时异常 - URL: {}", request.getRequestURL(), ex);
        
        return ResponseEntity.internalServerError()
            .body(ApiResponse.error("系统错误: " + ex.getMessage()));
    }
    
    /**
     * 处理静态资源未找到异常（特别处理Chrome DevTools请求）
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public ResponseEntity<Void> handleNoResourceFoundException(
            NoResourceFoundException ex, HttpServletRequest request) {

        String requestURI = request.getRequestURI();

        // 静默处理Chrome DevTools相关的资源请求
        if (requestURI.contains(".well-known/appspecific/com.chrome.devtools") ||
            requestURI.contains("favicon.ico") ||
            requestURI.contains("robots.txt") ||
            requestURI.contains("sitemap.xml")) {

            // 对于这些常见的浏览器/工具自动请求，只记录debug级别日志
            logger.debug("忽略浏览器/工具自动请求: {}", requestURI);
            return ResponseEntity.notFound().build();
        }

        // 对于其他静态资源请求，记录警告日志
        logger.warn("静态资源未找到: {} - {}", requestURI, ex.getMessage());
        return ResponseEntity.notFound().build();
    }

    /**
     * 处理通用异常（统一处理API和页面请求）
     */
    @ExceptionHandler(Exception.class)
    public Object handleGenericException(Exception ex, HttpServletRequest request) {
        logger.error("未处理的异常 - URL: {}", request.getRequestURL(), ex);

        // 判断是否为API请求
        String requestURI = request.getRequestURI();
        String acceptHeader = request.getHeader("Accept");
        String xRequestedWith = request.getHeader("X-Requested-With");

        boolean isApiRequest = requestURI.contains("/api/") ||
                              (acceptHeader != null && acceptHeader.contains("application/json")) ||
                              "XMLHttpRequest".equals(xRequestedWith);

        if (isApiRequest) {
            // API请求，返回JSON响应
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("系统发生未知错误，请联系管理员"));
        } else {
            // 页面请求，返回错误页面
            ModelAndView modelAndView = new ModelAndView("error/500");
            modelAndView.addObject("pageTitle", "系统错误");
            modelAndView.addObject("errorMessage", "系统发生错误，请稍后重试");
            modelAndView.addObject("errorDetails", ex.getMessage());
            modelAndView.addObject("requestUrl", request.getRequestURL().toString());
            return modelAndView;
        }
    }
}
