package com.mappinggen.dto;

import java.util.ArrayList;
import java.util.List;

public class XmlTreeNode {
    
    private String name;
    private String fullPath;
    private String value;
    private boolean hasChildren;
    private boolean expanded;
    private boolean selected;
    private int level;
    private List<XmlTreeNode> children;
    
    // Constructors
    public XmlTreeNode() {
        this.children = new ArrayList<>();
        this.expanded = false;
        this.selected = false;
    }
    
    public XmlTreeNode(String name, String fullPath, int level) {
        this();
        this.name = name;
        this.fullPath = fullPath;
        this.level = level;
    }
    
    // Helper methods
    public void addChild(XmlTreeNode child) {
        this.children.add(child);
        this.hasChildren = true;
    }
    
    public boolean isLeaf() {
        return children.isEmpty();
    }
    
    // Getters and Setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getFullPath() { return fullPath; }
    public void setFullPath(String fullPath) { this.fullPath = fullPath; }
    
    public String getValue() { return value; }
    public void setValue(String value) { this.value = value; }
    
    public boolean isHasChildren() { return hasChildren; }
    public void setHasChildren(boolean hasChildren) { this.hasChildren = hasChildren; }
    
    public boolean isExpanded() { return expanded; }
    public void setExpanded(boolean expanded) { this.expanded = expanded; }
    
    public boolean isSelected() { return selected; }
    public void setSelected(boolean selected) { this.selected = selected; }
    
    public int getLevel() { return level; }
    public void setLevel(int level) { this.level = level; }
    
    public List<XmlTreeNode> getChildren() { return children; }
    public void setChildren(List<XmlTreeNode> children) { 
        this.children = children;
        this.hasChildren = !children.isEmpty();
    }
}
