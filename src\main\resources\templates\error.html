<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('错误页面')}"></head>
<body>
    <div class="app-layout">
        <div th:replace="~{fragments/layout :: sidebar}"></div>
        <div class="app-main">
            <div th:replace="~{fragments/layout :: header}"></div>
            <main class="app-content">
                <div class="error-page">
                    <div class="error-content">
                        <svg class="error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        
                        <h1 class="error-title">
                            <span th:text="${status ?: '500'}">500</span>
                        </h1>
                        
                        <h2 class="error-subtitle" th:text="${error ?: '服务器错误'}">服务器错误</h2>
                        
                        <p class="error-message" th:text="${message ?: '抱歉，服务器遇到了一个错误'}">
                            抱歉，服务器遇到了一个错误
                        </p>
                        
                        <div class="error-actions">
                            <a href="/mapping-gen/" class="btn btn-primary">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                                </svg>
                                返回首页
                            </a>
                            <button type="button" class="btn btn-secondary" onclick="history.back()">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                </svg>
                                返回上页
                            </button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: scripts}"></div>
    
    <style>
        .error-page {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 60vh;
            text-align: center;
        }
        
        .error-content {
            max-width: 500px;
            padding: var(--spacing-xl);
        }
        
        .error-icon {
            width: 80px;
            height: 80px;
            color: var(--error-color);
            margin: 0 auto var(--spacing-lg);
        }
        
        .error-title {
            font-size: 4rem;
            font-weight: var(--font-weight-bold);
            color: var(--error-color);
            margin-bottom: var(--spacing-sm);
        }
        
        .error-subtitle {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-800);
            margin-bottom: var(--spacing-md);
        }
        
        .error-message {
            font-size: var(--font-size-lg);
            color: var(--gray-600);
            margin-bottom: var(--spacing-xl);
            line-height: var(--line-height-relaxed);
        }
        
        .error-actions {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
            flex-wrap: wrap;
        }
        
        @media (max-width: 480px) {
            .error-title {
                font-size: 3rem;
            }
            
            .error-subtitle {
                font-size: var(--font-size-xl);
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .error-actions .btn {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</body>
</html>
