package com.mappinggen.controller.api;

import com.mappinggen.dto.ApiResponse;
import com.mappinggen.dto.PageResult;
import com.mappinggen.model.DocumentFile;
import com.mappinggen.model.KnowledgeBase;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * REST API controller for Knowledge Base operations
 */
@RestController
@RequestMapping("/api/knowledge-base")
public class KnowledgeBaseApiController {

    // Mock data storage
    private static final Map<Long, KnowledgeBase> knowledgeBaseStorage = new HashMap<>();
    private static final Map<Long, List<DocumentFile>> documentsStorage = new HashMap<>();
    private static Long nextId = 1L;
    private static Long nextDocId = 1L;

    static {
        initializeMockData();
    }

    private static void initializeMockData() {
        // Enhanced Platform knowledge base data
        KnowledgeBase platform1 = new KnowledgeBase("航班搜索", "FlightSearch", "v1.0", "PLATFORM");
        platform1.setId(nextId++);
        platform1.setCreateTime(LocalDateTime.now().minusDays(15));
        knowledgeBaseStorage.put(platform1.getId(), platform1);

        KnowledgeBase platform2 = new KnowledgeBase("航班搜索", "FlightSearch", "v1.1", "PLATFORM");
        platform2.setId(nextId++);
        platform2.setCreateTime(LocalDateTime.now().minusDays(10));
        knowledgeBaseStorage.put(platform2.getId(), platform2);

        KnowledgeBase platform3 = new KnowledgeBase("订单管理", "OrderManagement", "v2.0", "PLATFORM");
        platform3.setId(nextId++);
        platform3.setCreateTime(LocalDateTime.now().minusDays(8));
        knowledgeBaseStorage.put(platform3.getId(), platform3);

        KnowledgeBase platform4 = new KnowledgeBase("订单管理", "OrderManagement", "v2.1", "PLATFORM");
        platform4.setId(nextId++);
        platform4.setCreateTime(LocalDateTime.now().minusDays(5));
        knowledgeBaseStorage.put(platform4.getId(), platform4);

        KnowledgeBase platform5 = new KnowledgeBase("支付处理", "PaymentProcess", "v1.0", "PLATFORM");
        platform5.setId(nextId++);
        platform5.setCreateTime(LocalDateTime.now().minusDays(3));
        knowledgeBaseStorage.put(platform5.getId(), platform5);

        KnowledgeBase platform6 = new KnowledgeBase("用户管理", "UserManagement", "v1.2", "PLATFORM");
        platform6.setId(nextId++);
        platform6.setCreateTime(LocalDateTime.now().minusDays(2));
        knowledgeBaseStorage.put(platform6.getId(), platform6);

        // Enhanced Vendor knowledge base data
        KnowledgeBase vendor1 = new KnowledgeBase("航班搜索", "FlightSearch", "v1.0", "VENDOR");
        vendor1.setId(nextId++);
        vendor1.setVendor("东方航空");
        vendor1.setCreateTime(LocalDateTime.now().minusDays(12));
        knowledgeBaseStorage.put(vendor1.getId(), vendor1);

        KnowledgeBase vendor2 = new KnowledgeBase("航班搜索", "FlightSearch", "v1.0", "VENDOR");
        vendor2.setId(nextId++);
        vendor2.setVendor("南方航空");
        vendor2.setCreateTime(LocalDateTime.now().minusDays(11));
        knowledgeBaseStorage.put(vendor2.getId(), vendor2);

        KnowledgeBase vendor3 = new KnowledgeBase("航班搜索", "FlightSearch", "v1.1", "VENDOR");
        vendor3.setId(nextId++);
        vendor3.setVendor("国际航空");
        vendor3.setCreateTime(LocalDateTime.now().minusDays(9));
        knowledgeBaseStorage.put(vendor3.getId(), vendor3);

        KnowledgeBase vendor4 = new KnowledgeBase("航班搜索", "FlightSearch", "v1.0", "VENDOR");
        vendor4.setId(nextId++);
        vendor4.setVendor("春秋航空");
        vendor4.setCreateTime(LocalDateTime.now().minusDays(7));
        knowledgeBaseStorage.put(vendor4.getId(), vendor4);

        KnowledgeBase vendor5 = new KnowledgeBase("订单管理", "OrderProcess", "v1.5", "VENDOR");
        vendor5.setId(nextId++);
        vendor5.setVendor("东方航空");
        vendor5.setCreateTime(LocalDateTime.now().minusDays(6));
        knowledgeBaseStorage.put(vendor5.getId(), vendor5);

        KnowledgeBase vendor6 = new KnowledgeBase("订单管理", "OrderProcess", "v1.3", "VENDOR");
        vendor6.setId(nextId++);
        vendor6.setVendor("南方航空");
        vendor6.setCreateTime(LocalDateTime.now().minusDays(4));
        knowledgeBaseStorage.put(vendor6.getId(), vendor6);

        KnowledgeBase vendor7 = new KnowledgeBase("支付处理", "PaymentGateway", "v2.0", "VENDOR");
        vendor7.setId(nextId++);
        vendor7.setVendor("东方航空");
        vendor7.setCreateTime(LocalDateTime.now().minusDays(3));
        knowledgeBaseStorage.put(vendor7.getId(), vendor7);

        KnowledgeBase vendor8 = new KnowledgeBase("支付处理", "PaymentGateway", "v1.8", "VENDOR");
        vendor8.setId(nextId++);
        vendor8.setVendor("国际航空");
        vendor8.setCreateTime(LocalDateTime.now().minusDays(2));
        knowledgeBaseStorage.put(vendor8.getId(), vendor8);

        KnowledgeBase vendor9 = new KnowledgeBase("用户管理", "UserService", "v1.0", "VENDOR");
        vendor9.setId(nextId++);
        vendor9.setVendor("春秋航空");
        vendor9.setCreateTime(LocalDateTime.now().minusDays(1));
        knowledgeBaseStorage.put(vendor9.getId(), vendor9);

        // Platform documents - Using correct field names for 7 fixed fields
        
        // Platform 1 - FlightSearch v1.0 documents
        List<DocumentFile> platform1Docs = Arrays.asList(
            new DocumentFile("respExample", "平台返参示例文件", "{\n  \"code\": \"200\",\n  \"message\": \"success\",\n  \"data\": {\n    \"flights\": [\n      {\n        \"flightNumber\": \"MU123\",\n        \"departureTime\": \"08:30\",\n        \"arrivalTime\": \"10:45\",\n        \"price\": 850,\n        \"availableSeats\": 45\n      }\n    ]\n  }\n}"),
            new DocumentFile("reqDoc", "平台入参文档文件", "## 航班搜索API入参文档\n\n### 接口说明\n- 接口名称：航班搜索\n- 请求方式：POST\n- 内容类型：application/json\n\n### 请求参数\n| 参数名 | 类型 | 必填 | 说明 |\n|--------|------|------|------|\n| origin | String | 是 | 出发城市代码 |\n| destination | String | 是 | 到达城市代码 |\n| departureDate | String | 是 | 出发日期 |"),
            new DocumentFile("respDoc", "平台返参文档文件", "## 航班搜索API返参文档\n\n### 响应说明\n- 响应格式：JSON\n- 字符编码：UTF-8\n\n### 响应参数\n| 参数名 | 类型 | 说明 |\n|--------|------|------|\n| code | String | 响应码 |\n| message | String | 响应消息 |\n| data | Object | 响应数据 |"),
            new DocumentFile("reqBeanPrompt", "入参Bean生成Prompt文件", "根据以下接口文档生成Java Bean类：\n\n```\n接口：航班搜索\n参数：\n- origin: 出发城市代码（必填）\n- destination: 到达城市代码（必填）\n- departureDate: 出发日期（必填）\n```\n\n请生成对应的请求Bean类，包含：\n1. 字段定义\n2. getter/setter方法\n3. 参数校验注解"),
            new DocumentFile("respBeanPrompt", "返参Bean生成Prompt文件", "根据以下响应结构生成Java Bean类：\n\n```json\n{\n  \"code\": \"200\",\n  \"message\": \"success\",\n  \"data\": {\n    \"flights\": []\n  }\n}\n```\n\n请生成对应的响应Bean类，包含嵌套对象处理。"),
            new DocumentFile("codeGenPrompt", "代码生成Prompt文件", "基于以上文档和Bean定义，生成完整的Controller代码，要求：\n\n1. 使用Spring Boot注解\n2. 包含参数校验\n3. 异常处理\n4. 返回统一响应格式\n5. 添加接口文档注解\n\n示例控制器方法应包含@PostMapping、@RequestBody等注解。"),
            new DocumentFile("codeTemplateDoc", "代码模板文档文件", "# 航班搜索代码模板\n\n## Controller模板\n```java\n@RestController\n@RequestMapping(\"/api/flight\")\npublic class FlightController {\n    \n    @PostMapping(\"/search\")\n    public ApiResponse<FlightSearchResponse> search(@RequestBody @Valid FlightSearchRequest request) {\n        // 业务逻辑处理\n        return ApiResponse.success(result);\n    }\n}\n```")
        );
        platform1Docs.forEach(doc -> doc.setId(nextDocId++));
        documentsStorage.put(platform1.getId(), platform1Docs);

        // Platform 2 - FlightSearch v1.1 documents
        List<DocumentFile> platform2Docs = Arrays.asList(
            new DocumentFile("respExample", "平台返参示例文件", "{\n  \"code\": \"200\",\n  \"message\": \"success\",\n  \"data\": {\n    \"totalResults\": 25,\n    \"flights\": [\n      {\n        \"flightNumber\": \"MU123\",\n        \"airline\": \"东方航空\",\n        \"price\": 850,\n        \"availableSeats\": 45\n      }\n    ]\n  }\n}"),
            new DocumentFile("reqDoc", "平台入参文档文件", "## 航班搜索API v1.1入参文档\n\n### 新增功能\n- 多程航班支持\n- 高级筛选功能\n\n### 请求参数\n| 参数名 | 类型 | 必填 | 说明 |\n|--------|------|------|------|\n| tripType | String | 否 | 行程类型 |\n| filters | Object | 否 | 筛选条件 |"),
            new DocumentFile("respDoc", "平台返参文档文件", "## 航班搜索API v1.1返参文档\n\n### 新增字段\n| 参数名 | 类型 | 说明 |\n|--------|------|------|\n| totalResults | Integer | 总结果数 |\n| filters | Object | 可用筛选选项 |"),
            new DocumentFile("reqBeanPrompt", "入参Bean生成Prompt文件", "航班搜索v1.1请求Bean生成提示"),
            new DocumentFile("respBeanPrompt", "返参Bean生成Prompt文件", "航班搜索v1.1响应Bean生成提示"),
            new DocumentFile("codeGenPrompt", "代码生成Prompt文件", "航班搜索v1.1代码生成提示"),
            new DocumentFile("codeTemplateDoc", "代码模板文档文件", "航班搜索v1.1代码模板")
        );
        platform2Docs.forEach(doc -> doc.setId(nextDocId++));
        documentsStorage.put(platform2.getId(), platform2Docs);

        // Platform 3 - OrderManagement v2.0 documents  
        List<DocumentFile> platform3Docs = Arrays.asList(
            new DocumentFile("respExample", "平台返参示例文件", "{\n  \"code\": \"200\",\n  \"message\": \"success\",\n  \"data\": {\n    \"orderId\": \"ORD001\",\n    \"status\": \"CONFIRMED\",\n    \"totalAmount\": 850.00\n  }\n}"),
            new DocumentFile("reqDoc", "平台入参文档文件", "## 订单管理API入参文档\n\n### 主要接口\n1. 创建订单\n2. 查询订单\n3. 更新订单状态"),
            new DocumentFile("respDoc", "平台返参文档文件", "## 订单管理API返参文档\n\n### 订单状态定义\n- PENDING: 待支付\n- CONFIRMED: 已确认\n- COMPLETED: 已完成"),
            new DocumentFile("reqBeanPrompt", "入参Bean生成Prompt文件", "订单管理请求Bean生成提示"),
            new DocumentFile("respBeanPrompt", "返参Bean生成Prompt文件", "订单管理响应Bean生成提示"),
            new DocumentFile("codeGenPrompt", "代码生成Prompt文件", "订单管理代码生成提示"),
            new DocumentFile("codeTemplateDoc", "代码模板文档文件", "订单管理代码模板")
        );
        platform3Docs.forEach(doc -> doc.setId(nextDocId++));
        documentsStorage.put(platform3.getId(), platform3Docs);

        // Platform 4 - OrderManagement v2.1 documents
        List<DocumentFile> platform4Docs = Arrays.asList(
            new DocumentFile("respExample", "平台返参示例文件", "{\n  \"code\": \"200\",\n  \"message\": \"success\",\n  \"data\": {\n    \"orderId\": \"ORD002\",\n    \"version\": \"v2.1\",\n    \"status\": \"PROCESSING\"\n  }\n}"),
            new DocumentFile("reqDoc", "平台入参文档文件", "订单管理v2.1入参文档"),
            new DocumentFile("respDoc", "平台返参文档文件", "订单管理v2.1返参文档"),  
            new DocumentFile("reqBeanPrompt", "入参Bean生成Prompt文件", "订单管理v2.1请求Bean生成提示"),
            new DocumentFile("respBeanPrompt", "返参Bean生成Prompt文件", "订单管理v2.1响应Bean生成提示"),
            new DocumentFile("codeGenPrompt", "代码生成Prompt文件", "订单管理v2.1代码生成提示"),
            new DocumentFile("codeTemplateDoc", "代码模板文档文件", "订单管理v2.1代码模板")
        );
        platform4Docs.forEach(doc -> doc.setId(nextDocId++));
        documentsStorage.put(platform4.getId(), platform4Docs);

        // Platform 5 - PaymentProcess v1.0 documents
        List<DocumentFile> platform5Docs = Arrays.asList(
            new DocumentFile("respExample", "平台返参示例文件", "{\n  \"code\": \"200\",\n  \"message\": \"success\",\n  \"data\": {\n    \"paymentId\": \"PAY001\",\n    \"status\": \"SUCCESS\",\n    \"amount\": 850.00\n  }\n}"),
            new DocumentFile("reqDoc", "平台入参文档文件", "## 支付处理API入参文档\n\n### 支付流程\n1. 创建支付订单\n2. 用户支付\n3. 支付回调\n4. 支付确认"),
            new DocumentFile("respDoc", "平台返参文档文件", "## 支付处理API返参文档\n\n### 支付状态\n- PENDING: 待支付\n- SUCCESS: 支付成功\n- FAILED: 支付失败"),
            new DocumentFile("reqBeanPrompt", "入参Bean生成Prompt文件", "支付处理请求Bean生成提示"),
            new DocumentFile("respBeanPrompt", "返参Bean生成Prompt文件", "支付处理响应Bean生成提示"),
            new DocumentFile("codeGenPrompt", "代码生成Prompt文件", "支付处理代码生成提示"),
            new DocumentFile("codeTemplateDoc", "代码模板文档文件", "支付处理代码模板")
        );
        platform5Docs.forEach(doc -> doc.setId(nextDocId++));
        documentsStorage.put(platform5.getId(), platform5Docs);

        // Platform 6 - UserManagement v1.2 documents
        List<DocumentFile> platform6Docs = Arrays.asList(
            new DocumentFile("respExample", "平台返参示例文件", "{\n  \"code\": \"200\",\n  \"message\": \"success\",\n  \"data\": {\n    \"userId\": \"U001\",\n    \"username\": \"user123\",\n    \"status\": \"ACTIVE\"\n  }\n}"),
            new DocumentFile("reqDoc", "平台入参文档文件", "## 用户管理API入参文档\n\n### 主要功能\n- 用户注册\n- 用户登录\n- 信息管理"),
            new DocumentFile("respDoc", "平台返参文档文件", "## 用户管理API返参文档\n\n### 用户状态\n- ACTIVE: 活跃\n- INACTIVE: 非活跃\n- BLOCKED: 已封禁"),
            new DocumentFile("reqBeanPrompt", "入参Bean生成Prompt文件", "用户管理请求Bean生成提示"),
            new DocumentFile("respBeanPrompt", "返参Bean生成Prompt文件", "用户管理响应Bean生成提示"),
            new DocumentFile("codeGenPrompt", "代码生成Prompt文件", "用户管理代码生成提示"),
            new DocumentFile("codeTemplateDoc", "代码模板文档文件", "用户管理代码模板")
        );
        platform6Docs.forEach(doc -> doc.setId(nextDocId++));
        documentsStorage.put(platform6.getId(), platform6Docs);

        // Vendor 1 - 东方航空 FlightSearch v1.0 documents
        List<DocumentFile> vendor1Docs = Arrays.asList(
            new DocumentFile("input_example.json", "输入示例", "{\n  \"departure\": \"PEK\",\n  \"arrival\": \"SHA\",\n  \"departureDate\": \"2024-08-01\",\n  \"passengerCount\": 1,\n  \"cabinClass\": \"ECONOMY\"\n}"),
            new DocumentFile("output_example.json", "输出示例", "{\n  \"flights\": [\n    {\n      \"flightNumber\": \"MU123\",\n      \"departureTime\": \"08:30\",\n      \"arrivalTime\": \"10:45\",\n      \"price\": 850,\n      \"availableSeats\": 45\n    }\n  ]\n}"),
            new DocumentFile("error_codes.json", "错误码说明", "{\n  \"errorCodes\": [\n    {\n      \"code\": \"E001\",\n      \"message\": \"出发城市代码无效\",\n      \"description\": \"请检查出发城市代码是否正确\"\n    },\n    {\n      \"code\": \"E002\",\n      \"message\": \"到达城市代码无效\",\n      \"description\": \"请检查到达城市代码是否正确\"\n    },\n    {\n      \"code\": \"E003\",\n      \"message\": \"日期格式错误\",\n      \"description\": \"日期格式应为YYYY-MM-DD\"\n    }\n  ]\n}")
        );
        vendor1Docs.forEach(doc -> doc.setId(nextDocId++));
        documentsStorage.put(vendor1.getId(), vendor1Docs);

        // Vendor 2 - 南方航空 FlightSearch v1.0 documents
        List<DocumentFile> vendor2Docs = Arrays.asList(
            new DocumentFile("api_guide.md", "接口指南", "# 南方航空接口指南\n\n## 接口概述\n南方航空提供标准的航班搜索接口，支持国内外航线查询。\n\n## 认证方式\n使用API Key进行认证，请在请求头中添加：\n```\nAuthorization: Bearer YOUR_API_KEY\n```\n\n## 请求限制\n- 每分钟最多100次请求\n- 每天最多10000次请求\n\n## 支持的航线\n- 国内航线：覆盖全国主要城市\n- 国际航线：覆盖亚洲、欧洲、北美主要城市"),
            new DocumentFile("request_format.json", "请求格式", "{\n  \"origin\": \"CAN\",\n  \"destination\": \"PEK\",\n  \"departureDate\": \"2024-08-01\",\n  \"returnDate\": \"2024-08-05\",\n  \"passengers\": {\n    \"adults\": 1,\n    \"children\": 0,\n    \"infants\": 0\n  },\n  \"cabinClass\": \"ECONOMY\",\n  \"directFlightOnly\": false\n}"),
            new DocumentFile("response_format.json", "响应格式", "{\n  \"requestId\": \"REQ20240801001\",\n  \"flights\": [\n    {\n      \"flightNumber\": \"CZ123\",\n      \"aircraft\": \"A330\",\n      \"departure\": {\n        \"airport\": \"CAN\",\n        \"terminal\": \"T2\",\n        \"time\": \"09:15\"\n      },\n      \"arrival\": {\n        \"airport\": \"PEK\",\n        \"terminal\": \"T3\",\n        \"time\": \"12:30\"\n      },\n      \"duration\": \"3h15m\",\n      \"price\": {\n        \"adult\": 980,\n        \"currency\": \"CNY\"\n      },\n      \"availableSeats\": 28\n    }\n  ]\n}")
        );
        vendor2Docs.forEach(doc -> doc.setId(nextDocId++));
        documentsStorage.put(vendor2.getId(), vendor2Docs);

        // Vendor 5 - 东方航空 OrderProcess v1.5 documents
        List<DocumentFile> vendor5Docs = Arrays.asList(
            new DocumentFile("order_integration.md", "订单集成指南", "# 东方航空订单处理集成指南\n\n## 集成概述\n东方航空订单处理系统提供完整的订单管理功能，包括预订、支付、出票等环节。\n\n## 集成步骤\n1. **获取API凭证**\n   - 申请API Key和Secret\n   - 配置回调地址\n\n2. **订单创建**\n   - 调用创建订单接口\n   - 获取订单号和支付信息\n\n3. **支付处理**\n   - 引导用户完成支付\n   - 处理支付回调\n\n4. **出票确认**\n   - 接收出票通知\n   - 获取电子票号"),
            new DocumentFile("order_request.json", "订单请求", "{\n  \"passengers\": [\n    {\n      \"name\": \"张三\",\n      \"idType\": \"ID_CARD\",\n      \"idNumber\": \"110101199001011234\",\n      \"phone\": \"13800138000\",\n      \"email\": \"<EMAIL>\"\n    }\n  ],\n  \"flights\": [\n    {\n      \"flightNumber\": \"MU123\",\n      \"departureDate\": \"2024-08-01\",\n      \"cabinClass\": \"ECONOMY\",\n      \"fareType\": \"STANDARD\"\n    }\n  ],\n  \"contact\": {\n    \"name\": \"张三\",\n    \"phone\": \"13800138000\",\n    \"email\": \"<EMAIL>\"\n  }\n}"),
            new DocumentFile("order_response.json", "订单响应", "{\n  \"orderId\": \"MU20240801001\",\n  \"status\": \"PENDING_PAYMENT\",\n  \"totalAmount\": 850.00,\n  \"currency\": \"CNY\",\n  \"passengers\": [\n    {\n      \"passengerId\": \"P001\",\n      \"name\": \"张三\",\n      \"ticketNumber\": null\n    }\n  ],\n  \"payment\": {\n    \"paymentId\": \"PAY20240801001\",\n    \"amount\": 850.00,\n    \"expireTime\": \"2024-08-01T10:30:00Z\"\n  },\n  \"createTime\": \"2024-08-01T09:30:00Z\"\n}")
        );
        vendor5Docs.forEach(doc -> doc.setId(nextDocId++));
        documentsStorage.put(vendor5.getId(), vendor5Docs);

        // Vendor 7 - 东方航空 PaymentGateway v2.0 documents
        List<DocumentFile> vendor7Docs = Arrays.asList(
            new DocumentFile("payment_integration.md", "支付集成文档", "# 东方航空支付网关集成文档\n\n## 支付网关概述\n东方航空支付网关v2.0支持多种支付方式，提供安全可靠的支付处理服务。\n\n## 支持的支付方式\n- 支付宝\n- 微信支付\n- 银联卡支付\n- 信用卡支付\n- 东航钱包\n\n## 安全特性\n- SSL加密传输\n- 数字签名验证\n- 风险控制系统\n- 实时监控\n\n## 集成流程\n1. 商户注册和认证\n2. 获取支付凭证\n3. 集成支付接口\n4. 测试和上线"),
            new DocumentFile("payment_methods.json", "支付方式配置", "{\n  \"paymentMethods\": [\n    {\n      \"code\": \"ALIPAY\",\n      \"name\": \"支付宝\",\n      \"enabled\": true,\n      \"minAmount\": 0.01,\n      \"maxAmount\": 50000.00,\n      \"fee\": 0.006\n    },\n    {\n      \"code\": \"WECHAT\",\n      \"name\": \"微信支付\",\n      \"enabled\": true,\n      \"minAmount\": 0.01,\n      \"maxAmount\": 50000.00,\n      \"fee\": 0.006\n    },\n    {\n      \"code\": \"UNIONPAY\",\n      \"name\": \"银联支付\",\n      \"enabled\": true,\n      \"minAmount\": 1.00,\n      \"maxAmount\": 100000.00,\n      \"fee\": 0.008\n    }\n  ]\n}")
        );
        vendor7Docs.forEach(doc -> doc.setId(nextDocId++));
        documentsStorage.put(vendor7.getId(), vendor7Docs);

        // Vendor 9 - 春秋航空 UserService v1.0 documents
        List<DocumentFile> vendor9Docs = Arrays.asList(
            new DocumentFile("user_api.md", "用户服务API", "# 春秋航空用户服务API v1.0\n\n## 服务概述\n用户服务提供用户注册、登录、信息管理等功能。\n\n## 主要功能\n- 用户注册\n- 用户登录\n- 用户信息查询和更新\n- 密码管理\n- 会员等级管理\n\n## 认证机制\n使用JWT Token进行用户认证，Token有效期为24小时。\n\n## 用户等级\n- 普通会员：基础服务\n- 银卡会员：优先值机\n- 金卡会员：贵宾休息室\n- 白金会员：全方位VIP服务"),
            new DocumentFile("user_registration.json", "用户注册", "{\n  \"username\": \"user123\",\n  \"password\": \"password123\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"13800138000\",\n  \"realName\": \"张三\",\n  \"idCard\": \"110101199001011234\",\n  \"birthday\": \"1990-01-01\",\n  \"gender\": \"MALE\"\n}"),
            new DocumentFile("user_profile.json", "用户信息", "{\n  \"userId\": \"U20240801001\",\n  \"username\": \"user123\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"13800138000\",\n  \"realName\": \"张三\",\n  \"memberLevel\": \"SILVER\",\n  \"points\": 15800,\n  \"registerTime\": \"2024-01-01T00:00:00Z\",\n  \"lastLoginTime\": \"2024-08-01T09:30:00Z\",\n  \"status\": \"ACTIVE\"\n}")
        );
        vendor9Docs.forEach(doc -> doc.setId(nextDocId++));
        documentsStorage.put(vendor9.getId(), vendor9Docs);
    }

    // Dropdown endpoints removed - now using unified /api/init-data endpoint

    @GetMapping
    public ApiResponse<PageResult<KnowledgeBase>> getKnowledgeBases(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String businessDomain,
            @RequestParam(required = false) String interfaceName,
            @RequestParam(required = false) String version,
            @RequestParam(required = false) String vendor,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        List<KnowledgeBase> filtered = knowledgeBaseStorage.values().stream()
                .filter(kb -> type == null || type.equals(kb.getType()))
                .filter(kb -> businessDomain == null || businessDomain.equals(kb.getBusinessDomain()))
                .filter(kb -> interfaceName == null || interfaceName.equals(kb.getInterfaceName()))
                .filter(kb -> version == null || version.equals(kb.getVersion()))
                .filter(kb -> vendor == null || vendor.equals(kb.getVendor()))
                .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                .collect(Collectors.toList());

        int start = page * size;
        int end = Math.min(start + size, filtered.size());
        List<KnowledgeBase> pageContent = start < filtered.size() ? filtered.subList(start, end) : new ArrayList<>();

        PageResult<KnowledgeBase> pageResult = PageResult.of(pageContent, page, size, filtered.size());
        return ApiResponse.success(pageResult);
    }

    @PostMapping
    public ApiResponse<KnowledgeBase> createKnowledgeBase(
            @RequestParam String type,
            @RequestParam String businessDomain,
            @RequestParam String interfaceName,
            @RequestParam String version,
            @RequestParam(required = false) String vendor,
            @RequestParam(required = false) MultipartFile requestPromptFile,
            @RequestParam(required = false) MultipartFile responsePromptFile,
            @RequestParam(required = false) MultipartFile codePromptFile,
            @RequestParam(required = false) MultipartFile codeTemplateFile,
            @RequestParam(required = false) MultipartFile inputDocFile,
            @RequestParam(required = false) MultipartFile outputDocFile,
            @RequestParam(required = false) MultipartFile inputExampleFile,
            @RequestParam(required = false) MultipartFile outputExampleFile,
            @RequestParam(required = false) MultipartFile inputDocumentFile,
            @RequestParam(required = false) MultipartFile outputDocumentFile) {

        KnowledgeBase kb = new KnowledgeBase(businessDomain, interfaceName, version, type);
        kb.setId(nextId++);
        if (vendor != null) {
            kb.setVendor(vendor);
        }

        knowledgeBaseStorage.put(kb.getId(), kb);

        // Process uploaded files
        List<DocumentFile> documents = new ArrayList<>();
        processFile(requestPromptFile, "请求提示", documents);
        processFile(responsePromptFile, "响应提示", documents);
        processFile(codePromptFile, "代码提示", documents);
        processFile(codeTemplateFile, "代码模板", documents);
        processFile(inputDocFile, "输入文档", documents);
        processFile(outputDocFile, "输出文档", documents);
        processFile(inputExampleFile, "输入示例", documents);
        processFile(outputExampleFile, "输出示例", documents);
        processFile(inputDocumentFile, "输入文档", documents);
        processFile(outputDocumentFile, "输出文档", documents);

        if (!documents.isEmpty()) {
            documentsStorage.put(kb.getId(), documents);
        }

        return ApiResponse.success("知识库创建成功", kb);
    }

    @GetMapping("/{id}/documents")
    public ApiResponse<Map<String, Object>> getKnowledgeBaseDocuments(
            @PathVariable Long id,
            @RequestParam String type) {

        KnowledgeBase kb = knowledgeBaseStorage.get(id);
        if (kb == null) {
            return ApiResponse.error("知识库不存在");
        }

        List<DocumentFile> documents = documentsStorage.getOrDefault(id, new ArrayList<>());

        Map<String, Object> result = new HashMap<>();
        result.put("id", kb.getId());
        result.put("businessDomain", kb.getBusinessDomain());
        result.put("interfaceName", kb.getInterfaceName());
        result.put("version", kb.getVersion());
        result.put("vendor", kb.getVendor());
        result.put("type", kb.getType());

        // For platform type, return the 7 fixed fields instead of dynamic documents
        if ("PLATFORM".equals(type)) {
            // Create a map with the 7 fixed fields with test data for easy verification
            Map<String, String> fieldsMap = new HashMap<>();

            // Initialize all fields with meaningful test content for testing text card display
            fieldsMap.put("respExample", "{\n  \"code\": \"200\",\n  \"message\": \"success\",\n  \"data\": {\n    \"flights\": [\n      {\n        \"flightNumber\": \"MU123\",\n        \"departureTime\": \"08:30\",\n        \"arrivalTime\": \"10:45\",\n        \"price\": 850,\n        \"availableSeats\": 45\n      }\n    ]\n  }\n}");
            fieldsMap.put("reqDoc", "## 航班搜索API入参文档\n\n### 接口说明\n- 接口名称：航班搜索\n- 请求方式：POST\n- 内容类型：application/json\n\n### 请求参数\n| 参数名 | 类型 | 必填 | 说明 |\n|--------|------|------|------|\n| origin | String | 是 | 出发城市代码 |\n| destination | String | 是 | 到达城市代码 |\n| departureDate | String | 是 | 出发日期 |");
            fieldsMap.put("respDoc", "## 航班搜索API返参文档\n\n### 响应说明\n- 响应格式：JSON\n- 字符编码：UTF-8\n\n### 响应参数\n| 参数名 | 类型 | 说明 |\n|--------|------|------|\n| code | String | 响应码 |\n| message | String | 响应消息 |\n| data | Object | 响应数据 |");
            fieldsMap.put("reqBeanPrompt", "根据以下接口文档生成Java Bean类：\n\n```\n接口：航班搜索\n参数：\n- origin: 出发城市代码（必填）\n- destination: 到达城市代码（必填）\n- departureDate: 出发日期（必填）\n```\n\n请生成对应的请求Bean类，包含：\n1. 字段定义\n2. getter/setter方法\n3. 参数校验注解");
            fieldsMap.put("respBeanPrompt", "根据以下响应结构生成Java Bean类：\n\n```json\n{\n  \"code\": \"200\",\n  \"message\": \"success\",\n  \"data\": {\n    \"flights\": []\n  }\n}\n```\n\n请生成对应的响应Bean类，包含嵌套对象处理。");
            fieldsMap.put("codeGenPrompt", "基于以上文档和Bean定义，生成完整的Controller代码，要求：\n\n1. 使用Spring Boot注解\n2. 包含参数校验\n3. 异常处理\n4. 返回统一响应格式\n5. 添加接口文档注解\n\n示例控制器方法应包含@PostMapping、@RequestBody等注解。");
            fieldsMap.put("codeTemplateDoc", "# 航班搜索代码模板\n\n## Controller模板\n```java\n@RestController\n@RequestMapping(\"/api/flight\")\npublic class FlightController {\n    \n    @PostMapping(\"/search\")\n    public ApiResponse<FlightSearchResponse> search(@RequestBody @Valid FlightSearchRequest request) {\n        // 业务逻辑处理\n        return ApiResponse.success(result);\n    }\n}\n```\n\n## 使用说明\n1. 替换相应的类名和方法名\n2. 根据实际业务调整参数和返回值\n3. 添加必要的业务逻辑");

            // Fill in content from documents if available (override test data if real data exists)
            for (DocumentFile doc : documents) {
                String fieldName = getFieldNameFromDocument(doc);
                if (fieldName != null && fieldsMap.containsKey(fieldName)) {
                    fieldsMap.put(fieldName, doc.getContent() != null ? doc.getContent() : fieldsMap.get(fieldName));
                }
            }

            // Add the fields to result
            result.putAll(fieldsMap);
        } else {
            // For vendor type, keep the original documents structure
            result.put("documents", documents);
        }

        return ApiResponse.success(result);
    }

    private String getFieldNameFromDocument(DocumentFile doc) {
        // Map document file names/types to field names
        String fileName = doc.getFileName();
        String fileType = doc.getFileType();

        if ("respExample".equals(fileName) || "平台返参示例文件".equals(fileType)) {
            return "respExample";
        } else if ("reqDoc".equals(fileName) || "平台入参文档文件".equals(fileType)) {
            return "reqDoc";
        } else if ("respDoc".equals(fileName) || "平台返参文档文件".equals(fileType)) {
            return "respDoc";
        } else if ("reqBeanPrompt".equals(fileName) || "入参Bean生成Prompt文件".equals(fileType)) {
            return "reqBeanPrompt";
        } else if ("respBeanPrompt".equals(fileName) || "返参Bean生成Prompt文件".equals(fileType)) {
            return "respBeanPrompt";
        } else if ("codeGenPrompt".equals(fileName) || "代码生成Prompt文件".equals(fileType)) {
            return "codeGenPrompt";
        } else if ("codeTemplateDoc".equals(fileName) || "代码模板文档文件".equals(fileType)) {
            return "codeTemplateDoc";
        }

        return null;
    }

    @PutMapping("/documents/{docId}")
    public ApiResponse<Void> updateDocument(@PathVariable Long docId, @RequestBody Map<String, String> request) {
        String newContent = request.get("content");

        for (List<DocumentFile> docs : documentsStorage.values()) {
            for (DocumentFile doc : docs) {
                if (doc.getId().equals(docId)) {
                    doc.setContent(newContent);
                    return ApiResponse.success("文档更新成功");
                }
            }
        }

        return ApiResponse.error("文档不存在");
    }

    @PostMapping("/{id}/update-field")
    public ApiResponse<Void> updateKnowledgeBaseField(
            @PathVariable Long id,
            @RequestParam String knowledgeBaseId,
            @RequestParam(required = false) MultipartFile respExample,
            @RequestParam(required = false) MultipartFile reqDoc,
            @RequestParam(required = false) MultipartFile respDoc,
            @RequestParam(required = false) MultipartFile reqBeanPrompt,
            @RequestParam(required = false) MultipartFile respBeanPrompt,
            @RequestParam(required = false) MultipartFile codeGenPrompt,
            @RequestParam(required = false) MultipartFile codeTemplateDoc) {

        try {
            // Process the uploaded file and update the corresponding field
            if (respExample != null) {
                processFileUpdate(Long.parseLong(knowledgeBaseId), "respExample", respExample);
            }
            if (reqDoc != null) {
                processFileUpdate(Long.parseLong(knowledgeBaseId), "reqDoc", reqDoc);
            }
            if (respDoc != null) {
                processFileUpdate(Long.parseLong(knowledgeBaseId), "respDoc", respDoc);
            }
            if (reqBeanPrompt != null) {
                processFileUpdate(Long.parseLong(knowledgeBaseId), "reqBeanPrompt", reqBeanPrompt);
            }
            if (respBeanPrompt != null) {
                processFileUpdate(Long.parseLong(knowledgeBaseId), "respBeanPrompt", respBeanPrompt);
            }
            if (codeGenPrompt != null) {
                processFileUpdate(Long.parseLong(knowledgeBaseId), "codeGenPrompt", codeGenPrompt);
            }
            if (codeTemplateDoc != null) {
                processFileUpdate(Long.parseLong(knowledgeBaseId), "codeTemplateDoc", codeTemplateDoc);
            }

            return ApiResponse.success("字段更新成功");
        } catch (Exception e) {
            return ApiResponse.error("字段更新失败：" + e.getMessage());
        }
    }

    private void processFileUpdate(Long knowledgeBaseId, String fieldName, MultipartFile file) throws Exception {
        if (file != null && !file.isEmpty()) {
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);

            // Update or create document for this field
            List<DocumentFile> docs = documentsStorage.getOrDefault(knowledgeBaseId, new ArrayList<>());

            // Find existing document for this field or create new one
            DocumentFile existingDoc = docs.stream()
                .filter(doc -> fieldName.equals(doc.getFileName()))
                .findFirst()
                .orElse(null);

            if (existingDoc != null) {
                existingDoc.setContent(content);
            } else {
                DocumentFile newDoc = new DocumentFile(fieldName, getFieldDisplayName(fieldName), content);
                newDoc.setId(nextDocId++);
                docs.add(newDoc);
            }

            documentsStorage.put(knowledgeBaseId, docs);
        }
    }

    private String getFieldDisplayName(String fieldName) {
        switch (fieldName) {
            case "respExample": return "平台返参示例文件";
            case "reqDoc": return "平台入参文档文件";
            case "respDoc": return "平台返参文档文件";
            case "reqBeanPrompt": return "入参Bean生成Prompt文件";
            case "respBeanPrompt": return "返参Bean生成Prompt文件";
            case "codeGenPrompt": return "代码生成Prompt文件";
            case "codeTemplateDoc": return "代码模板文档文件";
            default: return fieldName;
        }
    }

    @PostMapping("/documents/{docId}/reupload")
    public ApiResponse<Void> reuploadDocument(@PathVariable Long docId, @RequestParam MultipartFile file) {
        try {
            String content = new String(file.getBytes());

            for (List<DocumentFile> docs : documentsStorage.values()) {
                for (DocumentFile doc : docs) {
                    if (doc.getId().equals(docId)) {
                        doc.setContent(content);
                        doc.setFileName(file.getOriginalFilename());
                        return ApiResponse.success("文档重新上传成功");
                    }
                }
            }

            return ApiResponse.error("文档不存在");
        } catch (Exception e) {
            return ApiResponse.error("文件上传失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteKnowledgeBase(@PathVariable Long id, @RequestParam String type) {
        if (knowledgeBaseStorage.remove(id) != null) {
            documentsStorage.remove(id);
            return ApiResponse.success("删除成功");
        }
        return ApiResponse.error("知识库不存在");
    }

    private void processFile(MultipartFile file, String fileType, List<DocumentFile> documents) {
        if (file != null && !file.isEmpty()) {
            try {
                String content = new String(file.getBytes());
                DocumentFile doc = new DocumentFile(file.getOriginalFilename(), fileType, content);
                doc.setId(nextDocId++);
                documents.add(doc);
            } catch (Exception e) {
                // Log error but continue processing other files
                System.err.println("Error processing file " + file.getOriginalFilename() + ": " + e.getMessage());
            }
        }
    }
}
