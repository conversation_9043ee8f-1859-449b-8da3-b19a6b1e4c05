package com.mappinggen.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Knowledge Base entity representing both platform and vendor knowledge bases
 */
public class KnowledgeBase {
    
    private Long id;
    
    @NotBlank(message = "业务领域不能为空")
    @Size(max = 100, message = "业务领域长度不能超过100个字符")
    private String businessDomain;
    
    @NotBlank(message = "接口不能为空")
    @Size(max = 100, message = "接口长度不能超过100个字符")
    private String interfaceName;
    
    @NotBlank(message = "版本不能为空")
    @Size(max = 50, message = "版本长度不能超过50个字符")
    private String version;
    
    // For vendor knowledge base only
    private String vendor;
    
    // Platform or Vendor indicator
    private String type; // "PLATFORM" or "VENDOR"
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    // Document files
    private List<DocumentFile> documents;
    
    // Constructors
    public KnowledgeBase() {}
    
    public KnowledgeBase(String businessDomain, String interfaceName, String version, String type) {
        this.businessDomain = businessDomain;
        this.interfaceName = interfaceName;
        this.version = version;
        this.type = type;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getBusinessDomain() { return businessDomain; }
    public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }
    
    public String getInterfaceName() { return interfaceName; }
    public void setInterfaceName(String interfaceName) { this.interfaceName = interfaceName; }
    
    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }
    
    public String getVendor() { return vendor; }
    public void setVendor(String vendor) { this.vendor = vendor; }
    
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
    
    public List<DocumentFile> getDocuments() { return documents; }
    public void setDocuments(List<DocumentFile> documents) { this.documents = documents; }
    
    @Override
    public String toString() {
        return "KnowledgeBase{" +
                "id=" + id +
                ", businessDomain='" + businessDomain + '\'' +
                ", interfaceName='" + interfaceName + '\'' +
                ", version='" + version + '\'' +
                ", vendor='" + vendor + '\'' +
                ", type='" + type + '\'' +
                '}';
    }
}
