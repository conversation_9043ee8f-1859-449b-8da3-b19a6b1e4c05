package com.mappinggen.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Field Mapping entity for managing field mappings between platform and vendor
 */
public class FieldMapping {
    
    private Long id;
    private Long mappingAssociationId;
    
    // Business context
    private String businessDomain;
    private String platformInterface;
    private String platformVersion;
    private String vendor;
    private String vendorInterface;
    private String vendorVersion;
    
    // General logic
    @Size(max = 2000, message = "通用逻辑长度不能超过2000个字符")
    private String generalLogic;
    
    // Field mapping rules
    private List<FieldMappingRule> mappingRules;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    // Constructors
    public FieldMapping() {}
    
    public FieldMapping(Long mappingAssociationId, String businessDomain) {
        this.mappingAssociationId = mappingAssociationId;
        this.businessDomain = businessDomain;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Long getMappingAssociationId() { return mappingAssociationId; }
    public void setMappingAssociationId(Long mappingAssociationId) { this.mappingAssociationId = mappingAssociationId; }
    
    public String getBusinessDomain() { return businessDomain; }
    public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }
    
    public String getPlatformInterface() { return platformInterface; }
    public void setPlatformInterface(String platformInterface) { this.platformInterface = platformInterface; }
    
    public String getPlatformVersion() { return platformVersion; }
    public void setPlatformVersion(String platformVersion) { this.platformVersion = platformVersion; }
    
    public String getVendor() { return vendor; }
    public void setVendor(String vendor) { this.vendor = vendor; }
    
    public String getVendorInterface() { return vendorInterface; }
    public void setVendorInterface(String vendorInterface) { this.vendorInterface = vendorInterface; }
    
    public String getVendorVersion() { return vendorVersion; }
    public void setVendorVersion(String vendorVersion) { this.vendorVersion = vendorVersion; }
    
    public String getGeneralLogic() { return generalLogic; }
    public void setGeneralLogic(String generalLogic) { this.generalLogic = generalLogic; }
    
    public List<FieldMappingRule> getMappingRules() { return mappingRules; }
    public void setMappingRules(List<FieldMappingRule> mappingRules) { this.mappingRules = mappingRules; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
}
