<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<!-- Head fragment -->
<head th:fragment="head(title)">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title th:text="${title} + ' - 映射生成器'">映射生成器</title>

    <!-- Favicon - Use a data URI to avoid 404 errors -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23007bff'%3E%3Cpath d='M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1'/%3E%3C/svg%3E">
    
    <!-- CSS -->
    <link rel="stylesheet" th:href="@{/css/main.css}">
    
    <!-- Additional CSS can be added by pages -->
    <th:block th:fragment="additional-css"></th:block>
</head>

<!-- Header fragment -->
<header th:fragment="header" class="app-header">
    <div class="app-header-left">
        <button class="sidebar-toggle" type="button" aria-label="切换侧边栏">
            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>
        <h1 class="app-title">映射生成器</h1>
    </div>
    <div class="app-header-right">
        <span class="text-sm text-secondary">企业级映射管理平台</span>
    </div>
</header>

<!-- Sidebar fragment -->
<aside th:fragment="sidebar" class="app-sidebar">
    <div class="sidebar-header">
        <a href="#" class="sidebar-logo" th:href="@{/}">
            映射生成器
        </a>
    </div>
    
    <nav class="sidebar-nav">
        <ul class="nav-list">
            <!-- Knowledge Base -->
            <li class="nav-item">
                <a href="#" class="nav-link" th:href="@{/knowledge-base}"
                   th:classappend="${currentPage == 'knowledge-base'} ? 'active' : ''">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <span class="nav-text">知识库</span>
                    <button class="nav-toggle" type="button">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </a>
                <ul class="nav-submenu">
                    <li class="nav-item">
                        <a href="#" class="nav-link" th:href="@{/knowledge-base?type=platform}"
                           th:classappend="${param.type != null and param.type[0] == 'platform'} ? 'active' : ''">
                            <span class="nav-text">平台知识库</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" th:href="@{/knowledge-base?type=vendor}"
                           th:classappend="${param.type != null and param.type[0] == 'vendor'} ? 'active' : ''">
                            <span class="nav-text">供应商知识库</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <!-- Mapping Association -->
            <li class="nav-item">
                <a href="#" class="nav-link" th:href="@{/mapping-association}"
                   th:classappend="${currentPage == 'mapping-association'} ? 'active' : ''">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                    <span class="nav-text">建立关联</span>
                </a>
            </li>
            
            <!-- Field Mapping -->
            <li class="nav-item">
                <a href="#" class="nav-link" th:href="@{/field-mapping}"
                   th:classappend="${currentPage == 'field-mapping'} ? 'active' : ''">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                    <span class="nav-text">字段映射管理</span>
                </a>
            </li>
            
            <!-- Code Prompt -->
            <li class="nav-item">
                <a href="#" class="nav-link" th:href="@{/code-prompt}"
                   th:classappend="${currentPage == 'code-prompt'} ? 'active' : ''">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                    <span class="nav-text">CodePrompt 管理</span>
                </a>
            </li>
        </ul>
    </nav>
</aside>

<!-- Main content wrapper -->
<div th:fragment="main-wrapper" class="app-main">
    <div th:replace="~{fragments/layout :: header}"></div>
    <main class="app-content">

        
        <!-- Page header -->
        <div th:fragment="page-header(title, subtitle, actions)" class="page-header">
            <h1 class="page-title" th:text="${title}">页面标题</h1>
            <p class="page-subtitle" th:if="${subtitle != null}" th:text="${subtitle}">页面副标题</p>
            <div class="page-actions" th:if="${actions != null}" th:utext="${actions}">
                <!-- Actions will be inserted here -->
            </div>
        </div>
        
        <!-- Content will be inserted here by pages -->
        <th:block th:fragment="content"></th:block>
    </main>
</div>

<!-- Footer fragment -->
<footer th:fragment="footer" class="app-footer">
    <!-- Footer content if needed -->
</footer>

<!-- Scripts fragment -->
<div th:fragment="scripts">
    <script th:src="@{/js/common.js}"></script>
    <script th:src="@{/js/init-data.js}"></script>
    <!-- Additional scripts can be added by pages -->
    <th:block th:fragment="additional-scripts"></th:block>
</div>

<!-- Complete layout template fragment -->
<div th:fragment="layout(title, content)">
    <!-- This fragment is designed to be used with th:replace in complete HTML documents -->
    <!-- It should not contain html/head/body tags as those should be in the including template -->
    <div class="app-layout">
        <div th:replace="~{fragments/layout :: sidebar}"></div>
        <div class="app-main">
            <div th:replace="~{fragments/layout :: header}"></div>
            <main class="app-content">
                <div th:replace="${content}"></div>
            </main>
        </div>
    </div>
    <div th:replace="~{fragments/layout :: scripts}"></div>
</div>

</html>
