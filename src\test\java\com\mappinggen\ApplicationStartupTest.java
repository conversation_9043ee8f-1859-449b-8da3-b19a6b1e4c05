package com.mappinggen;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * 测试应用程序是否能够正常启动
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
public class ApplicationStartupTest {

    @Test
    public void contextLoads() {
        // 如果Spring上下文能够成功加载，这个测试就会通过
        // 这意味着所有的配置都是正确的，包括Thymeleaf模板
    }
}
