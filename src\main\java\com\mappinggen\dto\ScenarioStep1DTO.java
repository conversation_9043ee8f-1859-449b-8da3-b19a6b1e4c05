package com.mappinggen.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

public class ScenarioStep1DTO {
    
    @NotBlank(message = "业务域不能为空")
    private String businessDomain;
    
    @NotBlank(message = "航司不能为空")
    private String airline;
    
    @NotBlank(message = "接口不能为空")
    private String interfaceType;
    
    @NotBlank(message = "接口版本不能为空")
    @Pattern(regexp = "^V\\d+\\.\\d+$", message = "接口版本格式应为 V2.0")
    private String interfaceVersion;
    
    // Constructors
    public ScenarioStep1DTO() {}
    
    public ScenarioStep1DTO(String businessDomain, String airline, String interfaceType, String interfaceVersion) {
        this.businessDomain = businessDomain;
        this.airline = airline;
        this.interfaceType = interfaceType;
        this.interfaceVersion = interfaceVersion;
    }
    
    // Getters and Setters
    public String getBusinessDomain() { return businessDomain; }
    public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }
    
    public String getAirline() { return airline; }
    public void setAirline(String airline) { this.airline = airline; }
    
    public String getInterfaceType() { return interfaceType; }
    public void setInterfaceType(String interfaceType) { this.interfaceType = interfaceType; }
    
    public String getInterfaceVersion() { return interfaceVersion; }
    public void setInterfaceVersion(String interfaceVersion) { this.interfaceVersion = interfaceVersion; }
}
