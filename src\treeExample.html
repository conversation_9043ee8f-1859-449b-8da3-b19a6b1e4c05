<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版JSON路径选择器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e7f1 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            color: #2c3e50;
        }

        .container {
            width: 100%;
            max-width: 900px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.2rem;
            margin-bottom: 12px;
            color: #2c3e50;
            font-weight: 700;
        }

        .header p {
            font-size: 1rem;
            color: #7f8c8d;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .card {
            background: white;
            border-radius: 14px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .card-header {
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-header i {
            font-size: 1.6rem;
        }

        .card-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .card-content {
            padding: 25px;
        }

        .path-selector {
            margin-bottom: 25px;
        }

        .input-label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            min-height: 50px;
            border: 1px solid #dce1e6;
            border-radius: 10px;
            padding: 15px;
            background: #f8fafc;
            transition: all 0.3s ease;
        }

        .tags-container:focus-within {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        .path-tag {
            display: inline-flex;
            align-items: center;
            background: #e3f2fd;
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 14px;
            color: #1565c0;
            animation: fadeIn 0.3s ease;
        }

        .tag-text {
            margin-right: 8px;
        }

        .tag-remove {
            background: none;
            border: none;
            color: #1565c0;
            cursor: pointer;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s;
        }

        .tag-remove:hover {
            background: rgba(21, 101, 192, 0.1);
        }

        .clear-all {
            align-self: center;
            margin-left: auto;
            background: none;
            border: none;
            color: #e74c3c;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: color 0.2s;
        }

        .clear-all:hover {
            color: #c0392b;
        }

        .btn-group {
            display: flex;
            gap: 12px;
            margin-top: 15px;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #3498db;
            color: #3498db;
        }

        .btn-outline:hover {
            background: rgba(52, 152, 219, 0.1);
        }

        .json-preview {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            max-height: 250px;
            overflow: auto;
            white-space: pre-wrap;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        /* 紧凑型JSON树浮窗 */
        .json-popup {
            position: absolute;
            background: white;
            border-radius: 12px;
            width: 500px;
            max-height: 400px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            z-index: 1000;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            visibility: hidden;
        }

        .json-popup.active {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .popup-header {
            background: linear-gradient(135deg, #2c3e50 0%, #1a2530 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
        }

        .popup-title {
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .popup-title i {
            color: #3498db;
        }

        .close-popup {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s;
        }

        .close-popup:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .popup-body {
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            background: #f9fbfd;
            flex-grow: 1;
            overflow: hidden;
        }

        .search-container {
            position: relative;
        }

        .search-container i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 16px;
        }

        .search-input {
            width: 100%;
            padding: 12px 15px 12px 40px;
            border-radius: 8px;
            border: 1px solid #dce1e6;
            font-size: 14px;
            background: white;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        .tree-container {
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            padding: 10px;
            overflow: auto;
            flex-grow: 1;
            max-height: 300px;
        }

        .json-tree {
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        .tree-node {
            margin-left: 20px;
            position: relative;
        }

        .node-item {
            display: flex;
            align-items: center;
            padding: 6px 0;
            position: relative;
        }

        .node-toggle {
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-right: 6px;
            color: #3498db;
            font-size: 12px;
            border-radius: 4px;
            transition: background 0.2s;
        }

        .node-toggle:hover {
            background: rgba(52, 152, 219, 0.1);
        }

        .node-toggle i {
            transition: transform 0.3s ease;
        }

        .node-toggle.collapsed i {
            transform: rotate(-90deg);
        }

        .node-checkbox {
            margin-right: 8px;
            width: 16px;
            height: 16px;
            cursor: pointer;
            accent-color: #3498db;
        }

        .node-key {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .node-type {
            color: #7f8c8d;
            font-size: 0.8em;
            margin-left: 6px;
            font-weight: normal;
            font-style: italic;
        }

        .children-container {
            margin-left: 28px;
            overflow: hidden;
            transition: height 0.3s ease;
        }

        .instructions {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .instructions h3 {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 1.2rem;
        }

        .instructions h3 i {
            color: #3498db;
        }

        .instruction-list {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .instruction-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .instruction-item i {
            font-size: 24px;
            color: #3498db;
        }

        .instruction-text h4 {
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .instruction-text p {
            color: #7f8c8d;
            font-size: 14px;
            line-height: 1.5;
        }

        .highlight {
            background: rgba(52, 152, 219, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            color: #2c3e50;
        }

        .resize-handle {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 15px;
            height: 15px;
            background: #3498db;
            cursor: nwse-resize;
            z-index: 10;
            border-top-left-radius: 5px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.8rem;
            }

            .header p {
                font-size: 0.95rem;
            }

            .btn-group {
                flex-direction: column;
            }

            .json-popup {
                width: 95%;
                max-height: 60vh;
                left: 2.5% !important;
                right: auto !important;
            }

            .instruction-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>增强版JSON路径选择器</h1>
        <p>标签式输入框设计，智能节点回填，优化数组元素展示</p>
    </div>

    <div class="card">
        <div class="card-header">
            <i class="fas fa-code-branch"></i>
            <h2>JSON字段路径选择</h2>
        </div>
        <div class="card-content">
            <div class="path-selector">
                <label class="input-label">
                    <i class="fas fa-tags"></i>
                    已选字段路径
                </label>
                <div class="tags-container" id="tags-container">
                    <!-- 标签将通过JS动态生成 -->
                </div>
                <div class="btn-group">
                    <button id="open-tree" class="btn">
                        <i class="fas fa-tree"></i> 选择路径
                    </button>
                    <button id="clear-all" class="btn btn-outline">
                        <i class="fas fa-trash-alt"></i> 清除所有
                    </button>
                </div>
            </div>

            <div class="json-preview" id="json-preview">
                {
                "user": {
                "id": 12345,
                "name": "张三",
                "contact": {
                "email": "<EMAIL>",
                "phone": "13800138000"
                }
                },
                "orders": [
                {
                "orderId": "ORD2023001",
                "products": [
                {
                "id": "P1001",
                "name": "智能手机",
                "price": 3999
                },
                {
                "id": "P1002",
                "name": "蓝牙耳机",
                "price": 599
                }
                ]
                },
                {
                "orderId": "ORD2023002",
                "products": [
                {
                "id": "P2001",
                "name": "平板电脑",
                "price": 5299
                }
                ]
                }
                ],
                "preferences": {
                "theme": "dark",
                "notifications": true
                }
                }
            </div>
        </div>
    </div>

    <div class="instructions">
        <h3><i class="fas fa-info-circle"></i> 使用说明</h3>
        <ul class="instruction-list">
            <li class="instruction-item">
                <i class="fas fa-tag"></i>
                <div class="instruction-text">
                    <h4>标签式输入框</h4>
                    <p>选择的路径以标签形式展示，每个标签带删除按钮</p>
                </div>
            </li>
            <li class="instruction-item">
                <i class="fas fa-sync-alt"></i>
                <div class="instruction-text">
                    <h4>智能回填</h4>
                    <p>打开选择器时自动回填已选路径对应的节点</p>
                </div>
            </li>
            <li class="instruction-item">
                <i class="fas fa-layer-group"></i>
                <div class="instruction-text">
                    <h4>数组优化</h4>
                    <p>数组元素作为普通节点展示，不显示索引</p>
                </div>
            </li>
            <li class="instruction-item">
                <i class="fas fa-expand-alt"></i>
                <div class="instruction-text">
                    <h4>交互优化</h4>
                    <p>可拖动浮窗，调整大小，搜索定位节点</p>
                </div>
            </li>
        </ul>
    </div>
</div>

<!-- 紧凑型JSON树浮窗 -->
<div class="json-popup" id="json-popup">
    <div class="popup-header" id="popup-header">
        <div class="popup-title">
            <i class="fas fa-sitemap"></i>
            <span>字段路径选择器</span>
        </div>
        <button class="close-popup" id="close-popup">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div class="popup-body">
        <div class="search-container">
            <i class="fas fa-search"></i>
            <input type="text" class="search-input" id="search-tree" placeholder="搜索节点...">
        </div>

        <div class="tree-container">
            <div id="json-tree" class="json-tree"></div>
        </div>
    </div>
    <div class="resize-handle"></div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tagsContainer = document.getElementById('tags-container');
        const openTreeBtn = document.getElementById('open-tree');
        const clearAllBtn = document.getElementById('clear-all');
        const closePopupBtn = document.getElementById('close-popup');
        const jsonPopup = document.getElementById('json-popup');
        const popupHeader = document.getElementById('popup-header');
        const searchInput = document.getElementById('search-tree');
        const jsonTreeContainer = document.getElementById('json-tree');

        // 存储所有路径
        let selectedPaths = [];

        // 示例JSON数据
        const jsonData = {
            user: {
                id: 12345,
                name: "张三",
                contact: {
                    email: "<EMAIL>",
                    phone: "13800138000"
                }
            },
            orders: [
                {
                    orderId: "ORD2023001",
                    products: [
                        {
                            id: "P1001",
                            name: "智能手机",
                            price: 3999
                        },
                        {
                            id: "P1002",
                            name: "蓝牙耳机",
                            price: 599
                        }
                    ]
                },
                {
                    orderId: "ORD2023002",
                    products: [
                        {
                            id: "P2001",
                            name: "平板电脑",
                            price: 5299
                        }
                    ]
                }
            ],
            preferences: {
                theme: "dark",
                notifications: true
            }
        };

        // 初始化 - 如果有存储的路径，则加载
        loadSelectedPaths();

        // 打开JSON树浮窗
        openTreeBtn.addEventListener('click', function(e) {
            // 生成JSON树
            renderJsonTree(jsonData);

            // 显示浮窗
            jsonPopup.classList.add('active');

            // 定位浮窗在按钮右下方
            const buttonRect = openTreeBtn.getBoundingClientRect();
            const popupWidth = 500;

            // 计算位置（确保不会超出视口）
            let left = buttonRect.right + 10;
            let top = buttonRect.top;

            // 如果右侧空间不足，显示在左侧
            if (left + popupWidth > window.innerWidth) {
                left = buttonRect.left - popupWidth - 10;
            }

            // 如果下方空间不足，显示在上方
            if (top + 400 > window.innerHeight) {
                top = window.innerHeight - 420;
            }

            jsonPopup.style.left = left + 'px';
            jsonPopup.style.top = top + 'px';
        });

        // 关闭浮窗
        function closePopup() {
            jsonPopup.classList.remove('active');
        }

        closePopupBtn.addEventListener('click', closePopup);

        // 清除所有标签
        clearAllBtn.addEventListener('click', function() {
            selectedPaths = [];
            saveSelectedPaths();
            renderTags();
        });

        // 保存路径到本地存储
        function saveSelectedPaths() {
            localStorage.setItem('jsonPathSelectorPaths', JSON.stringify(selectedPaths));
        }

        // 从本地存储加载路径
        function loadSelectedPaths() {
            const savedPaths = localStorage.getItem('jsonPathSelectorPaths');
            if (savedPaths) {
                selectedPaths = JSON.parse(savedPaths);
            }
            renderTags();
        }

        // 渲染标签
        function renderTags() {
            tagsContainer.innerHTML = '';

            if (selectedPaths.length === 0) {
                const placeholder = document.createElement('div');
                placeholder.className = 'placeholder';
                placeholder.textContent = '请选择字段路径...';
                placeholder.style.color = '#a0aec0';
                placeholder.style.fontStyle = 'italic';
                tagsContainer.appendChild(placeholder);
                return;
            }

            selectedPaths.forEach(path => {
                const tag = document.createElement('div');
                tag.className = 'path-tag';

                const textSpan = document.createElement('span');
                textSpan.className = 'tag-text';
                textSpan.textContent = path;

                const removeBtn = document.createElement('button');
                removeBtn.className = 'tag-remove';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.dataset.path = path;
                removeBtn.addEventListener('click', function() {
                    const pathToRemove = this.dataset.path;
                    selectedPaths = selectedPaths.filter(p => p !== pathToRemove);
                    saveSelectedPaths();
                    renderTags();
                });

                tag.appendChild(textSpan);
                tag.appendChild(removeBtn);
                tagsContainer.appendChild(tag);
            });

            // 添加清除所有按钮
            const clearBtn = document.createElement('button');
            clearBtn.className = 'clear-all';
            clearBtn.innerHTML = '<i class="fas fa-times-circle"></i> 清除全部';
            clearBtn.addEventListener('click', function() {
                selectedPaths = [];
                saveSelectedPaths();
                renderTags();
            });

            tagsContainer.appendChild(clearBtn);
        }

        // 渲染JSON树
        function renderJsonTree(data) {
            jsonTreeContainer.innerHTML = '';

            // 创建根节点
            const rootNode = document.createElement('div');
            rootNode.className = 'tree-node';

            // 递归构建树
            buildTree(rootNode, data, 'root');

            jsonTreeContainer.appendChild(rootNode);

            // 添加折叠/展开事件
            addToggleEvents();

            // 添加复选框事件
            addCheckboxEvents();

            // 回填已选路径
            checkSelectedPaths();
        }

        // 递归构建树结构
        function buildTree(container, data, path, isRoot = false) {
            // 创建当前节点
            const nodeItem = document.createElement('div');
            nodeItem.className = 'node-item';

            // 添加折叠图标（如果有子节点）
            if (data !== null && typeof data === 'object' && Object.keys(data).length > 0) {
                const toggle = document.createElement('div');
                toggle.className = 'node-toggle';
                toggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
                nodeItem.appendChild(toggle);
            } else {
                // 占位空间
                const spacer = document.createElement('div');
                spacer.style.width = '22px';
                spacer.style.marginRight = '6px';
                nodeItem.appendChild(spacer);
            }

            // 添加复选框
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'node-checkbox';
            checkbox.dataset.path = path;
            nodeItem.appendChild(checkbox);

            // 添加键名
            const keySpan = document.createElement('span');
            keySpan.className = 'node-key';
            keySpan.textContent = isRoot ? 'root' : path.split('.').pop();
            nodeItem.appendChild(keySpan);

            // 添加类型
            const typeSpan = document.createElement('span');
            typeSpan.className = 'node-type';
            typeSpan.textContent = getType(data);
            nodeItem.appendChild(typeSpan);

            container.appendChild(nodeItem);

            // 处理子节点
            if (data !== null && typeof data === 'object') {
                const childrenContainer = document.createElement('div');
                childrenContainer.className = 'children-container';

                // 对象处理
                if (!Array.isArray(data)) {
                    for (const key in data) {
                        const childPath = isRoot ? key : `${path}.${key}`;

                        const childNode = document.createElement('div');
                        childNode.className = 'tree-node';
                        buildTree(childNode, data[key], childPath);
                        childrenContainer.appendChild(childNode);
                    }
                }
                // 数组处理 - 不显示索引
                else {
                    // 对于数组，我们只取第一个元素展示结构（假设结构相同）
                    if (data.length > 0) {
                        const sampleItem = data[0];

                        for (const key in sampleItem) {
                            const childPath = `${path}.${key}`;

                            const childNode = document.createElement('div');
                            childNode.className = 'tree-node';

                            if (sampleItem[key] !== null && typeof sampleItem[key] === 'object') {
                                buildTree(childNode, sampleItem[key], childPath);
                            } else {
                                // 原始值节点
                                buildTree(childNode, null, childPath);
                            }

                            childrenContainer.appendChild(childNode);
                        }
                    }
                }

                container.appendChild(childrenContainer);
            }
        }

        // 获取数据类型
        function getType(value) {
            if (value === null) return 'null';
            if (Array.isArray(value)) return 'array';
            return typeof value;
        }

        // 添加折叠/展开事件
        function addToggleEvents() {
            const toggles = document.querySelectorAll('.node-toggle');
            toggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const childrenContainer = this.parentElement.nextElementSibling;
                    if (childrenContainer) {
                        const isCollapsed = this.classList.contains('collapsed');

                        if (isCollapsed) {
                            // 展开
                            childrenContainer.style.height = childrenContainer.scrollHeight + 'px';
                            this.classList.remove('collapsed');

                            // 动画结束后移除高度限制
                            setTimeout(() => {
                                childrenContainer.style.height = '';
                            }, 300);
                        } else {
                            // 折叠
                            childrenContainer.style.height = childrenContainer.scrollHeight + 'px';
                            setTimeout(() => {
                                childrenContainer.style.height = '0';
                                this.classList.add('collapsed');
                            }, 10);
                        }
                    }
                });
            });
        }

        // 添加复选框事件
        function addCheckboxEvents() {
            const checkboxes = document.querySelectorAll('.node-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const path = this.dataset.path;

                    if (this.checked) {
                        // 添加路径
                        if (!selectedPaths.includes(path)) {
                            selectedPaths.push(path);
                        }
                    } else {
                        // 移除路径
                        selectedPaths = selectedPaths.filter(p => p !== path);
                    }

                    // 保存并更新UI
                    saveSelectedPaths();
                    renderTags();
                });
            });
        }

        // 回填已选路径
        function checkSelectedPaths() {
            const checkboxes = document.querySelectorAll('.node-checkbox');
            checkboxes.forEach(checkbox => {
                const path = checkbox.dataset.path;
                checkbox.checked = selectedPaths.includes(path);
            });
        }

        // 搜索功能
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();

            if (!searchTerm) {
                // 清空搜索
                document.querySelectorAll('.node-key').forEach(el => {
                    el.style.backgroundColor = '';
                });
                return;
            }

            // 高亮匹配的节点
            let found = false;
            document.querySelectorAll('.node-key').forEach(el => {
                const key = el.textContent.toLowerCase();
                if (key.includes(searchTerm)) {
                    el.style.backgroundColor = 'rgba(255, 215, 0, 0.3)';
                    // 滚动到可见区域
                    if (!found) {
                        el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        found = true;
                    }
                } else {
                    el.style.backgroundColor = '';
                }
            });

            if (!found) {
                // 没有找到匹配项
                searchInput.style.borderColor = '#e74c3c';
                setTimeout(() => {
                    searchInput.style.borderColor = '';
                }, 1000);
            }
        });

        // 浮窗拖动功能
        let isDragging = false;
        let offsetX, offsetY;

        popupHeader.addEventListener('mousedown', function(e) {
            isDragging = true;
            offsetX = e.clientX - jsonPopup.getBoundingClientRect().left;
            offsetY = e.clientY - jsonPopup.getBoundingClientRect().top;
            jsonPopup.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', function(e) {
            if (isDragging) {
                const x = e.clientX - offsetX;
                const y = e.clientY - offsetY;

                // 确保浮窗不会移出视口
                const maxX = window.innerWidth - jsonPopup.offsetWidth;
                const maxY = window.innerHeight - jsonPopup.offsetHeight;

                jsonPopup.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
                jsonPopup.style.top = Math.max(0, Math.min(y, maxY)) + 'px';
            }
        });

        document.addEventListener('mouseup', function() {
            isDragging = false;
            jsonPopup.style.cursor = '';
        });

        // 浮窗调整大小
        let isResizing = false;

        const resizeHandle = document.querySelector('.resize-handle');
        resizeHandle.addEventListener('mousedown', function(e) {
            e.preventDefault();
            isResizing = true;
            document.body.style.cursor = 'nwse-resize';
        });

        document.addEventListener('mousemove', function(e) {
            if (isResizing) {
                const rect = jsonPopup.getBoundingClientRect();
                const newWidth = e.clientX - rect.left;
                const newHeight = e.clientY - rect.top;

                // 设置最小尺寸
                if (newWidth > 300 && newWidth < window.innerWidth - rect.left) {
                    jsonPopup.style.width = newWidth + 'px';
                }

                if (newHeight > 200 && newHeight < window.innerHeight - rect.top) {
                    jsonPopup.style.height = newHeight + 'px';
                }
            }
        });

        document.addEventListener('mouseup', function() {
            if (isResizing) {
                isResizing = false;
                document.body.style.cursor = '';
            }
        });
    });
</script>
</body>
</html>