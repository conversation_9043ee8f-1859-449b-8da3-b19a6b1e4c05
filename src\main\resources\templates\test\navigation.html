<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head(${title})}"></head>
<body>
    <div class="app-layout">
        <div th:replace="~{fragments/layout :: sidebar}"></div>
        <div class="app-main">
            <div th:replace="~{fragments/layout :: header}"></div>
            <main class="app-content">
                <div class="page-header">
                    <h1 class="page-title">Navigation Test Page</h1>
                    <p class="page-subtitle">Testing navigation highlighting and SpringEL expressions</p>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <h3>Navigation Test Results</h3>
                        <p>This page tests the navigation highlighting functionality.</p>
                        
                        <div class="alert alert-info">
                            <strong>Current Page:</strong>
                            <code th:text="${currentPage}">N/A</code>
                        </div>

                        <div class="alert alert-info">
                            <strong>Page Title:</strong>
                            <code th:text="${title}">N/A</code>
                        </div>
                        
                        <h4>Navigation Tests:</h4>
                        <ul>
                            <li>Knowledge Base active:
                                <span th:text="${currentPage == 'knowledge-base'}">false</span>
                            </li>
                            <li>Mapping Association active:
                                <span th:text="${currentPage == 'mapping-association'}">false</span>
                            </li>
                            <li>Field Mapping active:
                                <span th:text="${currentPage == 'field-mapping'}">false</span>
                            </li>
                            <li>Code Prompt active:
                                <span th:text="${currentPage == 'code-prompt'}">false</span>
                            </li>
                            <li>Test page active:
                                <span th:text="${currentPage == 'test'}">false</span>
                            </li>
                        </ul>
                        
                        <div class="alert alert-success">
                            <strong>Success!</strong> If you can see this page with proper navigation,
                            the navigation system is working correctly without using deprecated #request expressions.
                        </div>
                        
                        <a href="../" class="btn btn-primary">Back to Home</a>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: scripts}"></div>
</body>
</html>
