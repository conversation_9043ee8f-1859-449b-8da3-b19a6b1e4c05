/* Layout Styles */

/* Main Layout */
.app-layout {
  display: flex;
  min-height: 100vh;
  background-color: var(--gray-50);
}

.app-sidebar {
  width: var(--sidebar-width);
  background: linear-gradient(180deg, var(--white) 0%, #fafbfc 100%);
  border-right: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: var(--z-fixed);
  backdrop-filter: blur(10px);
}

.app-sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.app-main {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin-left var(--transition-normal);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-sidebar.collapsed + .app-main {
  margin-left: var(--sidebar-collapsed-width);
}

/* Header */
.app-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: 0 var(--spacing-lg);
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.app-header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.app-header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.app-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin: 0;
}

.sidebar-toggle {
  background: none;
  border: none;
  padding: var(--spacing-sm);
  cursor: pointer;
  border-radius: var(--border-radius-md);
  transition: background-color var(--transition-fast);
}

.sidebar-toggle:hover {
  background-color: var(--gray-100);
}

/* Content Area */
.app-content {
  flex: 1;
  padding: var(--spacing-lg);
  max-width: calc(100vw - var(--sidebar-width) - 2 * var(--spacing-lg));
  margin: 0 auto;
  width: 100%;
}

/* Enhanced content area for better space utilization */
@media (min-width: 1400px) {
  .app-content {
    max-width: calc(100vw - var(--sidebar-width) - 4 * var(--spacing-lg));
  }
}

@media (min-width: 1600px) {
  .app-content {
    max-width: calc(100vw - var(--sidebar-width) - 6 * var(--spacing-lg));
  }
}

/* Sidebar Navigation */
.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  text-align: center;
}

.sidebar-logo {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  text-decoration: none;
  display: block;
}

.sidebar-nav {
  padding: var(--spacing-md) 0;
}

/* 移除导航列表的默认样式 */
.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: var(--spacing-xs);
  list-style: none;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--gray-700);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-radius: var(--border-radius-md);
  position: relative;
  margin: 0 var(--spacing-sm);
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-link:hover {
  background: var(--nav-link-hover-bg);
  color: var(--primary-color);
  text-decoration: none;
  transform: translateX(4px);
  box-shadow: var(--shadow-sm);
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link.active {
  background: linear-gradient(135deg, var(--nav-link-active-bg) 0%, var(--primary-50) 100%);
  color: var(--primary-color);
  border-right: 3px solid var(--nav-link-active-border);
  box-shadow: var(--shadow-sm);
  font-weight: var(--font-weight-medium);
}

.nav-link .icon {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
}

.nav-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.app-sidebar.collapsed .nav-text {
  opacity: 0;
  width: 0;
}

.app-sidebar.collapsed .nav-link .icon {
  margin-right: 0;
}

/* Submenu */
.nav-submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal);
}

.nav-item.open .nav-submenu {
  max-height: 200px;
}

.nav-submenu .nav-link {
  padding-left: calc(var(--spacing-lg) + 20px + var(--spacing-md));
  font-size: var(--font-size-sm);
}

/* 移除子菜单项前的圆点装饰 */
.nav-submenu .nav-link::before {
  display: none;
}

.nav-toggle {
  background: none;
  border: none;
  padding: 0;
  margin-left: auto;
  color: var(--gray-500);
  transition: transform var(--transition-fast);
}

.nav-item.open .nav-toggle {
  transform: rotate(180deg);
}



/* Page Header */
.page-header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  margin: 0 0 var(--spacing-sm) 0;
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  margin: 0;
}

.page-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-sidebar {
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
  }
  
  .app-sidebar.show {
    transform: translateX(0);
  }
  
  .app-main {
    margin-left: 0;
  }
  
  .app-content {
    padding: var(--spacing-md);
    max-width: none; /* Remove max-width constraint on mobile */
  }
  
  .page-title {
    font-size: var(--font-size-2xl);
  }
  
  .page-actions {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 0 var(--spacing-md);
    height: 56px;
  }

  .app-content {
    padding: var(--spacing-sm);
    max-width: none; /* Remove max-width constraint on small mobile */
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-xs);
  }

  .form-control {
    font-size: var(--font-size-sm);
  }

  .table {
    font-size: var(--font-size-xs);
  }

  .nav-text {
    font-size: var(--font-size-sm);
  }
}

/* Enhanced responsive utilities */
@media (max-width: 992px) {
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .btn-group {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .modal-dialog {
    margin: var(--spacing-sm);
  }
}

/* Touch-friendly enhancements */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px;
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .form-control {
    min-height: 44px;
    padding: var(--spacing-md);
  }

  .nav-link {
    min-height: 48px;
  }
}

/* Print styles */
@media print {
  .app-sidebar,
  .app-header,
  .btn,
  .modal {
    display: none !important;
  }

  .app-main {
    margin-left: 0 !important;
  }

  .app-content {
    padding: 0 !important;
  }

  .table {
    border-collapse: collapse;
  }

  .table th,
  .table td {
    border: 1px solid #000;
  }
}
