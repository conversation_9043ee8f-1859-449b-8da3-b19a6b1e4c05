package com.mappinggen.controller.newApi;

import com.mappinggen.dto.ApiResponse;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台供应商映射控制器
 * 提供平台接口和供应商接口映射关系的管理功能
 */
@RestController
@RequestMapping("/api/platform-supplier-mapping")
public class PlatformSupplierMappingController {

    // 模拟数据存储
    private static final Map<Long, PlatformSupplierMappingResponse> mappings = new HashMap<>();
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static Long nextId = 1L;

    static {
        initializeMockData();
    }

    /**
     * 初始化模拟数据
     */
    private static void initializeMockData() {
        // 机票业务域映射关系
        createMockMapping("机票", "搜索接口", "v1.0", "东航", "航班搜索", "v1.0", "张三");
        createMockMapping("机票", "搜索接口", "v1.0", "南航", "机票查询", "v1.1", "李四");
        createMockMapping("机票", "预订接口", "v1.0", "东航", "订票接口", "v1.0", "王五");
        createMockMapping("机票", "支付接口", "v1.1", "国航", "付款API", "v2.0", "赵六");
        
        // 酒店业务域映射关系
        createMockMapping("酒店", "搜索接口", "v1.0", "携程", "酒店搜索", "v1.0", "孙七");
        createMockMapping("酒店", "预订接口", "v1.1", "美团", "房间预订", "v1.2", "周八");
        createMockMapping("酒店", "查询接口", "v1.0", "去哪儿", "订单查询", "v1.0", "吴九");
        
        // 租车业务域映射关系
        createMockMapping("租车", "搜索接口", "v1.0", "神州", "车辆搜索", "v1.0", "郑十");
        createMockMapping("租车", "预订接口", "v1.0", "一嗨", "租车预订", "v1.1", "刘一");
        createMockMapping("租车", "支付接口", "v1.1", "神州", "支付服务", "v2.0", "陈二");
        
        // 火车票业务域映射关系
        createMockMapping("火车票", "搜索接口", "v1.0", "12306", "车次查询", "v1.0", "韩三");
        createMockMapping("火车票", "预订接口", "v1.1", "12306", "购票接口", "v1.1", "张四");
    }

    /**
     * 创建模拟映射数据的辅助方法
     */
    private static void createMockMapping(String businessDomain, String platformInterface, String platformVersion,
                                        String supplier, String supplierInterface, String supplierVersion, String operator) {
        PlatformSupplierMappingResponse mapping = new PlatformSupplierMappingResponse();
        mapping.setId(nextId++);
        mapping.setPlatformInterfaceId(nextId + 100L); // 模拟关联ID
        mapping.setSupplierInterfaceId(nextId + 200L); // 模拟关联ID
        mapping.setCommonLogic("// 通用处理逻辑\nif (request == null) {\n    throw new IllegalArgumentException(\"请求参数不能为空\");\n}\n\n// 数据转换逻辑\nresponse.setCode(\"200\");\nresponse.setMessage(\"处理成功\");");
        mapping.setOperator(operator);
        mapping.setCreateTime(LocalDateTime.now().minusDays((long) (Math.random() * 30)).format(DATE_FORMAT));
        mapping.setUpdateTime(LocalDateTime.now().minusDays((long) (Math.random() * 10)).format(DATE_FORMAT));
        
        // 设置显示信息
        mapping.setPlatformBusinessDomain(businessDomain);
        mapping.setPlatformInterfaceName(platformInterface);
        mapping.setPlatformVersion(platformVersion);
        mapping.setSupplierBusinessDomain(businessDomain);
        mapping.setSupplierName(supplier);
        mapping.setSupplierInterfaceName(supplierInterface);
        mapping.setSupplierVersion(supplierVersion);
        
        // 生成拼接信息
        mapping.setPlatformAppendInfo(businessDomain + "-" + platformInterface + "-" + platformVersion);
        mapping.setSupplierAppendInfo(businessDomain + "-" + supplier + "-" + supplierInterface + "-" + supplierVersion);
        
        mappings.put(mapping.getId(), mapping);
    }

    /**
     * 5.1 分页查询映射关系
     * 
     * @param businessDomain 业务域
     * @param platformInterfaceName 平台接口名
     * @param platformVersion 平台版本
     * @param supplierName 供应商名称
     * @param supplierInterfaceName 供应商接口名
     * @param supplierVersion 供应商版本
     * @param pageNum 页码，默认1
     * @param pageSize 每页大小，默认10
     * @return 分页的映射关系列表
     */
    @GetMapping("/search")
    public ApiResponse<PlatformSupplierMappingPageResult> search(
            @RequestParam(required = false) String businessDomain,
            @RequestParam(required = false) String platformInterfaceName,
            @RequestParam(required = false) String platformVersion,
            @RequestParam(required = false) String supplierName,
            @RequestParam(required = false) String supplierInterfaceName,
            @RequestParam(required = false) String supplierVersion,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {

        try {
            // 获取所有映射数据
            List<PlatformSupplierMappingResponse> allMappings = new ArrayList<>(mappings.values());
            
            // 应用筛选条件
            List<PlatformSupplierMappingResponse> filtered = allMappings.stream()
                .filter(mapping -> businessDomain == null || businessDomain.isEmpty() || 
                        mapping.getPlatformBusinessDomain().contains(businessDomain))
                .filter(mapping -> platformInterfaceName == null || platformInterfaceName.isEmpty() || 
                        mapping.getPlatformInterfaceName().contains(platformInterfaceName))
                .filter(mapping -> platformVersion == null || platformVersion.isEmpty() || 
                        mapping.getPlatformVersion().contains(platformVersion))
                .filter(mapping -> supplierName == null || supplierName.isEmpty() || 
                        mapping.getSupplierName().contains(supplierName))
                .filter(mapping -> supplierInterfaceName == null || supplierInterfaceName.isEmpty() || 
                        mapping.getSupplierInterfaceName().contains(supplierInterfaceName))
                .filter(mapping -> supplierVersion == null || supplierVersion.isEmpty() || 
                        mapping.getSupplierVersion().contains(supplierVersion))
                .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime())) // 按创建时间倒序
                .collect(Collectors.toList());

            // 分页处理
            long total = filtered.size();
            int totalPages = (int) Math.ceil((double) total / pageSize);
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, filtered.size());
            
            List<PlatformSupplierMappingResponse> records = startIndex < filtered.size() 
                ? filtered.subList(startIndex, endIndex) 
                : new ArrayList<>();

            // 构建分页结果
            PlatformSupplierMappingPageResult pageResult = new PlatformSupplierMappingPageResult();
            pageResult.setRecords(records);
            pageResult.setTotal(total);
            pageResult.setPageNum(pageNum);
            pageResult.setPageSize(pageSize);
            pageResult.setTotalPages(totalPages);
            pageResult.setHasNext(pageNum < totalPages);
            pageResult.setHasPrevious(pageNum > 1);

            return ApiResponse.success("查询映射关系成功", pageResult);
            
        } catch (Exception e) {
            return ApiResponse.error("查询映射关系失败：" + e.getMessage());
        }
    }

    /**
     * 5.2 添加映射关系
     * 
     * @param request 映射关系请求对象
     * @return 创建的映射关系
     */
    @PostMapping
    public ApiResponse<PlatformSupplierMappingResponse> create(@RequestBody CreateMappingRequest request) {
        try {
            // 验证请求参数
            if (request.getPlatformInterfaceId() == null || request.getSupplierInterfaceId() == null) {
                return ApiResponse.error("平台接口ID和供应商接口ID不能为空");
            }

            // 检查是否已存在相同的映射关系
            boolean exists = mappings.values().stream()
                .anyMatch(mapping -> mapping.getPlatformInterfaceId().equals(request.getPlatformInterfaceId()) 
                         && mapping.getSupplierInterfaceId().equals(request.getSupplierInterfaceId()));
            
            if (exists) {
                return ApiResponse.error("该映射关系已存在，请勿重复创建");
            }

            // 创建新的映射关系
            PlatformSupplierMappingResponse mapping = new PlatformSupplierMappingResponse();
            mapping.setId(nextId++);
            mapping.setPlatformInterfaceId(request.getPlatformInterfaceId());
            mapping.setSupplierInterfaceId(request.getSupplierInterfaceId());
            mapping.setCommonLogic("// 自动生成的通用处理逻辑\nif (request == null) {\n    throw new IllegalArgumentException(\"请求参数不能为空\");\n}\n\n// 请在此处添加具体的业务逻辑\nresponse.setCode(\"200\");\nresponse.setMessage(\"处理成功\");");
            mapping.setOperator("系统用户");
            mapping.setCreateTime(LocalDateTime.now().format(DATE_FORMAT));
            mapping.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
            
            // 模拟从关联表获取详细信息（实际项目中需要查询数据库）
            // 这里使用硬编码的示例数据
            String businessDomain = "机票"; // 实际应从platformInterfaceId查询得到
            mapping.setPlatformBusinessDomain(businessDomain);
            mapping.setPlatformInterfaceName("新建接口");
            mapping.setPlatformVersion("v1.0");
            mapping.setSupplierBusinessDomain(businessDomain);
            mapping.setSupplierName("新供应商");
            mapping.setSupplierInterfaceName("新供应商接口");
            mapping.setSupplierVersion("v1.0");
            
            // 生成拼接信息
            mapping.setPlatformAppendInfo(mapping.getPlatformBusinessDomain() + "-" + 
                                        mapping.getPlatformInterfaceName() + "-" + 
                                        mapping.getPlatformVersion());
            mapping.setSupplierAppendInfo(mapping.getSupplierBusinessDomain() + "-" + 
                                        mapping.getSupplierName() + "-" + 
                                        mapping.getSupplierInterfaceName() + "-" + 
                                        mapping.getSupplierVersion());

            // 保存到内存存储
            mappings.put(mapping.getId(), mapping);

            return ApiResponse.success("添加映射关系成功", mapping);
            
        } catch (Exception e) {
            return ApiResponse.error("添加映射关系失败：" + e.getMessage());
        }
    }

    /**
     * 5.3 删除映射关系
     * 
     * @param id 映射关系ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> delete(@PathVariable Long id) {
        try {
            if (!mappings.containsKey(id)) {
                return ApiResponse.error("映射关系不存在");
            }

            mappings.remove(id);
            return ApiResponse.success("删除映射关系成功");
            
        } catch (Exception e) {
            return ApiResponse.error("删除映射关系失败：" + e.getMessage());
        }
    }

    /**
     * 创建映射关系请求对象
     */
    public static class CreateMappingRequest {
        private Long platformInterfaceId;
        private Long supplierInterfaceId;

        public Long getPlatformInterfaceId() { return platformInterfaceId; }
        public void setPlatformInterfaceId(Long platformInterfaceId) { this.platformInterfaceId = platformInterfaceId; }

        public Long getSupplierInterfaceId() { return supplierInterfaceId; }
        public void setSupplierInterfaceId(Long supplierInterfaceId) { this.supplierInterfaceId = supplierInterfaceId; }
    }

    /**
     * 平台供应商映射响应对象
     */
    public static class PlatformSupplierMappingResponse {
        private Long id;
        private Long platformInterfaceId;
        private Long supplierInterfaceId;
        private String commonLogic;
        private String operator;
        private String createTime;
        private String updateTime;
        
        // 关联查询字段
        private String platformBusinessDomain;
        private String platformInterfaceName;
        private String platformVersion;
        private String supplierBusinessDomain;
        private String supplierName;
        private String supplierInterfaceName;
        private String supplierVersion;
        
        // 拼接显示字段
        private String platformAppendInfo;
        private String supplierAppendInfo;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public Long getPlatformInterfaceId() { return platformInterfaceId; }
        public void setPlatformInterfaceId(Long platformInterfaceId) { this.platformInterfaceId = platformInterfaceId; }

        public Long getSupplierInterfaceId() { return supplierInterfaceId; }
        public void setSupplierInterfaceId(Long supplierInterfaceId) { this.supplierInterfaceId = supplierInterfaceId; }

        public String getCommonLogic() { return commonLogic; }
        public void setCommonLogic(String commonLogic) { this.commonLogic = commonLogic; }

        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }

        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }

        public String getUpdateTime() { return updateTime; }
        public void setUpdateTime(String updateTime) { this.updateTime = updateTime; }

        public String getPlatformBusinessDomain() { return platformBusinessDomain; }
        public void setPlatformBusinessDomain(String platformBusinessDomain) { this.platformBusinessDomain = platformBusinessDomain; }

        public String getPlatformInterfaceName() { return platformInterfaceName; }
        public void setPlatformInterfaceName(String platformInterfaceName) { this.platformInterfaceName = platformInterfaceName; }

        public String getPlatformVersion() { return platformVersion; }
        public void setPlatformVersion(String platformVersion) { this.platformVersion = platformVersion; }

        public String getSupplierBusinessDomain() { return supplierBusinessDomain; }
        public void setSupplierBusinessDomain(String supplierBusinessDomain) { this.supplierBusinessDomain = supplierBusinessDomain; }

        public String getSupplierName() { return supplierName; }
        public void setSupplierName(String supplierName) { this.supplierName = supplierName; }

        public String getSupplierInterfaceName() { return supplierInterfaceName; }
        public void setSupplierInterfaceName(String supplierInterfaceName) { this.supplierInterfaceName = supplierInterfaceName; }

        public String getSupplierVersion() { return supplierVersion; }
        public void setSupplierVersion(String supplierVersion) { this.supplierVersion = supplierVersion; }

        public String getPlatformAppendInfo() { return platformAppendInfo; }
        public void setPlatformAppendInfo(String platformAppendInfo) { this.platformAppendInfo = platformAppendInfo; }

        public String getSupplierAppendInfo() { return supplierAppendInfo; }
        public void setSupplierAppendInfo(String supplierAppendInfo) { this.supplierAppendInfo = supplierAppendInfo; }
    }

    /**
     * 平台供应商映射分页结果
     */
    public static class PlatformSupplierMappingPageResult {
        private List<PlatformSupplierMappingResponse> records;
        private long total;
        private int pageNum;
        private int pageSize;
        private int totalPages;
        private boolean hasNext;
        private boolean hasPrevious;

        public List<PlatformSupplierMappingResponse> getRecords() { return records; }
        public void setRecords(List<PlatformSupplierMappingResponse> records) { this.records = records; }

        public long getTotal() { return total; }
        public void setTotal(long total) { this.total = total; }

        public int getPageNum() { return pageNum; }
        public void setPageNum(int pageNum) { this.pageNum = pageNum; }

        public int getPageSize() { return pageSize; }
        public void setPageSize(int pageSize) { this.pageSize = pageSize; }

        public int getTotalPages() { return totalPages; }
        public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

        public boolean isHasNext() { return hasNext; }
        public void setHasNext(boolean hasNext) { this.hasNext = hasNext; }

        public boolean isHasPrevious() { return hasPrevious; }
        public void setHasPrevious(boolean hasPrevious) { this.hasPrevious = hasPrevious; }
    }
}