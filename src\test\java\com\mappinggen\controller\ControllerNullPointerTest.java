package com.mappinggen.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 测试控制器中的NullPointerException修复
 */
@SpringBootTest
@AutoConfigureWebMvc
public class ControllerNullPointerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Test
    public void testScenarioCreatePageNoNullPointer() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        mockMvc.perform(get("/scenario/create"))
                .andExpect(status().isOk())
                .andExpect(view().name("layout/base"))
                .andExpect(model().attributeExists("pageTitle"))
                .andExpect(model().attributeExists("breadcrumbs"))
                .andExpect(model().attributeExists("steps"));
    }

    @Test
    public void testMappingPageNoNullPointer() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        mockMvc.perform(get("/mapping"))
                .andExpect(status().isOk())
                .andExpect(view().name("layout/base"))
                .andExpect(model().attributeExists("pageTitle"))
                .andExpect(model().attributeExists("breadcrumbs"));
    }

    @Test
    public void testCodePromptPageNoNullPointer() throws Exception {
        MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        mockMvc.perform(get("/codeprompt"))
                .andExpect(status().isOk())
                .andExpect(view().name("layout/base"))
                .andExpect(model().attributeExists("pageTitle"))
                .andExpect(model().attributeExists("breadcrumbs"));
    }
}
