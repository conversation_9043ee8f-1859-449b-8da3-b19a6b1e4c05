/**
 * Common JavaScript utilities and functions
 */

// Global application object
window.MappingGen = window.MappingGen || {};

// API configuration
MappingGen.API = {
    BASE_URL: '/mapping-gen',
    
    // Helper method for making API calls
    call: async function(endpoint, options = {}) {
        const url = this.BASE_URL + endpoint;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'API call failed');
            }
            
            return data;
        } catch (error) {
            console.error('API call error:', error);
            throw error;
        }
    },
    
    // GET request
    get: function(endpoint, params = {}) {
        let url = endpoint;

        // Add query parameters if any
        if (Object.keys(params).length > 0) {
            const searchParams = new URLSearchParams();
            Object.keys(params).forEach(key => {
                if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
                    searchParams.append(key, params[key]);
                }
            });
            url += '?' + searchParams.toString();
        }

        return this.call(url, { method: 'GET' });
    },
    
    // POST request
    post: function(endpoint, data = {}) {
        return this.call(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },
    
    // PUT request
    put: function(endpoint, data = {}) {
        return this.call(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },
    
    // DELETE request
    delete: function(endpoint) {
        return this.call(endpoint, { method: 'DELETE' });
    },
    
    // File upload
    upload: function(endpoint, formData) {
        return this.call(endpoint, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        });
    }
};

// Utility functions
MappingGen.Utils = {
    // Show loading state
    showLoading: function(element) {
        if (element) {
            element.classList.add('loading');
            element.disabled = true;
        }
    },
    
    // Hide loading state
    hideLoading: function(element) {
        if (element) {
            element.classList.remove('loading');
            element.disabled = false;
        }
    },
    
    // Show alert message
    showAlert: function(message, type = 'info', container = null) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible" role="alert">
                ${message}
                <button type="button" class="alert-close" onclick="this.parentElement.remove()">
                    &times;
                </button>
            </div>
        `;
        
        const targetContainer = container || document.querySelector('.app-content');
        if (targetContainer) {
            targetContainer.insertAdjacentHTML('afterbegin', alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                const alert = targetContainer.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    },
    
    // Format date
    formatDate: function(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    // Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Validate form
    validateForm: function(form) {
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
            }
        });
        
        return isValid;
    },
    
    // Clear form
    clearForm: function(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (input.type === 'checkbox' || input.type === 'radio') {
                input.checked = false;
            } else {
                input.value = '';
            }
            input.classList.remove('is-invalid', 'is-valid');
        });
    },
    
    // Get form data as object
    getFormData: function(form) {
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            if (data[key]) {
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }
        
        return data;
    }
};

// Modal management
MappingGen.Modal = {
    show: function(modalId) {
        console.log('MappingGen.Modal.show called with modalId:', modalId);
        const modal = document.getElementById(modalId);
        if (modal) {
            console.log('Modal element found:', modal);
            console.log('Modal current display style:', window.getComputedStyle(modal).display);
            console.log('Modal current classes:', modal.className);

            // Create dynamic backdrop if it doesn't exist
            this.createBackdrop(modalId);

            modal.classList.add('show');
            modal.style.display = 'block'; // Ensure display is set
            document.body.style.overflow = 'hidden';

            console.log('Modal after show - display:', window.getComputedStyle(modal).display);
            console.log('Modal after show - classes:', modal.className);

            // Focus management
            const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (firstFocusable) {
                firstFocusable.focus();
            }
        } else {
            console.error('Modal not found with ID:', modalId);
            console.log('Available modal elements:', document.querySelectorAll('.modal'));
        }
    },
    
    hide: function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            console.log('Hiding modal:', modalId); // Debug log

            // Clear forms in the modal
            const forms = modal.querySelectorAll('form');
            forms.forEach(form => {
                if (typeof MappingGen.Utils.clearForm === 'function') {
                    MappingGen.Utils.clearForm(form);
                } else {
                    form.reset();
                }
            });

            // Clear any validation messages
            const invalidFeedbacks = modal.querySelectorAll('.invalid-feedback');
            invalidFeedbacks.forEach(feedback => {
                feedback.textContent = '';
                feedback.style.display = 'none';
            });

            // Remove validation classes
            const formControls = modal.querySelectorAll('.form-control');
            formControls.forEach(control => {
                control.classList.remove('is-invalid', 'is-valid');
            });

            modal.classList.remove('show');
            modal.style.display = 'none'; // Ensure display is set
            document.body.style.overflow = '';

            // Remove dynamic backdrop
            this.removeBackdrop(modalId);
        }
    },
    
    toggle: function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            if (modal.classList.contains('show')) {
                this.hide(modalId);
            } else {
                this.show(modalId);
            }
        }
    },

    createBackdrop: function(modalId) {
        // Remove existing backdrop if any
        this.removeBackdrop(modalId);

        // Create new backdrop as a separate element outside the modal
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop';
        backdrop.id = `${modalId}-backdrop`;
        backdrop.dataset.modalId = modalId;

        // Add click event to close modal when clicking backdrop
        backdrop.addEventListener('click', (e) => {
            if (e.target === backdrop) {
                this.hide(modalId);
            }
        });

        // Insert backdrop before the modal in DOM
        const modal = document.getElementById(modalId);
        if (modal && modal.parentNode) {
            modal.parentNode.insertBefore(backdrop, modal);
        }
    },

    removeBackdrop: function(modalId) {
        const backdrop = document.getElementById(`${modalId}-backdrop`);
        if (backdrop) {
            backdrop.remove();
        }
    }
};

// Navigation management
MappingGen.Navigation = {
    init: function() {
        // Sidebar toggle
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        const sidebar = document.querySelector('.app-sidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }
        
        // Navigation menu
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            const toggle = item.querySelector('.nav-toggle');
            if (toggle) {
                toggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    item.classList.toggle('open');
                });
            }
        });
        
        // Set active navigation item
        this.setActiveNavItem();
    },
    
    setActiveNavItem: function() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
                
                // Open parent submenu if exists
                const parentItem = link.closest('.nav-item');
                if (parentItem && parentItem.querySelector('.nav-submenu')) {
                    parentItem.classList.add('open');
                }
            }
        });
    }
};

// Tab management
MappingGen.Tabs = {
    init: function(tabContainer) {
        const tabButtons = tabContainer.querySelectorAll('.tab-button');
        const tabContents = tabContainer.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');
                
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked button and corresponding content
                button.classList.add('active');
                const targetContent = tabContainer.querySelector(`[data-tab-content="${targetTab}"]`);
                if (targetContent) {
                    targetContent.classList.add('active');
                }
            });
        });
    }
};

// Initialize common functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize navigation
    MappingGen.Navigation.init();
    
    // Initialize tabs
    const tabContainers = document.querySelectorAll('.knowledge-base-tabs');
    tabContainers.forEach(container => {
        MappingGen.Tabs.init(container.parentElement);
    });
    
    // Modal event listeners
    document.addEventListener('click', function(e) {
        // Modal close buttons
        if (e.target.classList.contains('modal-close')) {
            const modal = e.target.closest('.modal');
            if (modal) {
                MappingGen.Modal.hide(modal.id);
            }
            return;
        }

        // Modal backdrop clicks (for dynamic backdrops)
        if (e.target.classList.contains('modal-backdrop')) {
            const modalId = e.target.dataset.modalId;
            if (modalId) {
                MappingGen.Modal.hide(modalId);
            }
            return;
        }

        // Data action buttons - handle before modal content check
        if (e.target.hasAttribute('data-action')) {
            e.preventDefault();
            const action = e.target.getAttribute('data-action');
            const target = e.target.getAttribute('data-target');

            console.log('Data action clicked:', action, 'target:', target);

            switch (action) {
                case 'show-modal':
                    console.log('Attempting to show modal:', target);
                    MappingGen.Modal.show(target);
                    break;
                case 'hide-modal':
                    console.log('Attempting to hide modal:', target);
                    MappingGen.Modal.hide(target);
                    break;
            }
            return; // Prevent further processing after handling data action
        }

        // Modal dialog clicks - prevent closing when clicking inside modal content
        if (e.target.closest('.modal-content')) {
            return; // Don't close modal when clicking inside content
        }
    });
    
    // Keyboard event listeners
    document.addEventListener('keydown', function(e) {
        // Close modal on Escape key
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                MappingGen.Modal.hide(openModal.id);
            }
        }
    });
});
