/* CSS Custom Properties for Enterprise Design System */
:root {
  /* Color Palette */
  --primary-color: #1976d2;
  --primary-dark: #1565c0;
  --primary-light: #42a5f5;
  --secondary-color: #424242;
  --secondary-dark: #212121;
  --secondary-light: #757575;
  
  /* Accent Colors */
  --accent-color: #ff9800;
  --accent-dark: #f57c00;
  --accent-light: #ffb74d;
  
  /* Status Colors - Enhanced */
  --success-color: #059669;
  --success-dark: #047857;
  --success-light: #10b981;
  --success-bg: #ecfdf5;
  --success-border: #a7f3d0;

  --warning-color: #d97706;
  --warning-dark: #b45309;
  --warning-light: #f59e0b;
  --warning-bg: #fffbeb;
  --warning-border: #fde68a;

  --error-color: #dc2626;
  --error-dark: #b91c1c;
  --error-light: #ef4444;
  --error-bg: #fef2f2;
  --error-border: #fecaca;

  --info-color: #0891b2;
  --info-dark: #0e7490;
  --info-light: #06b6d4;
  --info-bg: #f0f9ff;
  --info-border: #bae6fd;

  /* Additional Colors for Filter Groups */
  --blue-500: #3b82f6;
  --green-500: #10b981;
  
  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #eeeeee;
  --gray-300: #e0e0e0;
  --gray-400: #bdbdbd;
  --gray-500: #9e9e9e;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;
  --black: #000000;
  
  /* Typography */
  --font-family-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
  
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Spacing */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  
  /* Border Radius */
  --border-radius-sm: 0.25rem;  /* 4px */
  --border-radius-md: 0.375rem; /* 6px */
  --border-radius-lg: 0.5rem;   /* 8px */
  --border-radius-xl: 0.75rem;  /* 12px */
  --border-radius-2xl: 1rem;    /* 16px */
  --border-radius-full: 9999px;
  
  /* Enhanced Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-focus: 0 0 0 3px rgba(25, 118, 210, 0.1);
  --shadow-focus-error: 0 0 0 3px rgba(220, 38, 38, 0.1);
  --shadow-focus-success: 0 0 0 3px rgba(5, 150, 105, 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  
  /* Layout */
  --header-height: 64px;
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 64px;
  --container-max-width: 1200px;
  
  /* Form Elements */
  --input-height: 40px;
  --input-padding-x: 12px;
  --input-padding-y: 8px;
  --input-border-width: 1px;
  --input-border-color: var(--gray-300);
  --input-border-radius: var(--border-radius-md);
  --input-focus-border-color: var(--primary-color);
  --input-focus-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
  
  /* Buttons - Enhanced */
  --button-height: 40px;
  --button-height-sm: 32px;
  --button-height-lg: 48px;
  --button-padding-x: 16px;
  --button-padding-x-sm: 12px;
  --button-padding-x-lg: 20px;
  --button-padding-y: 8px;
  --button-border-radius: var(--border-radius-md);
  --button-font-weight: var(--font-weight-medium);
  --button-transition: all var(--transition-fast);

  /* Tables */
  --table-border-color: var(--gray-200);
  --table-header-bg: var(--gray-50);
  --table-row-hover-bg: var(--gray-50);
  --table-stripe-bg: #fafbfc;

  /* Cards */
  --card-bg: var(--white);
  --card-border: var(--gray-200);
  --card-border-radius: var(--border-radius-lg);
  --card-shadow: var(--shadow-sm);
  --card-shadow-hover: var(--shadow-md);

  /* Modals */
  --modal-backdrop-bg: rgba(0, 0, 0, 0.5);
  --modal-border-radius: var(--border-radius-xl);
  --modal-shadow: var(--shadow-2xl);

  /* Navigation */
  --nav-link-hover-bg: rgba(25, 118, 210, 0.08);
  --nav-link-active-bg: rgba(25, 118, 210, 0.12);
  --nav-link-active-border: var(--primary-color);

  /* Animations */
  --animation-fade-in: fadeIn 0.3s ease-in-out;
  --animation-slide-up: slideUp 0.3s ease-out;
  --animation-scale-in: scaleIn 0.2s ease-out;
}
