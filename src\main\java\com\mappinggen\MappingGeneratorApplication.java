package com.mappinggen;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Main application class for the Mapping Generator application.
 * This enterprise-level application provides comprehensive mapping management
 * capabilities with a professional UI built using Thymeleaf.
 *
 * Note: File upload configuration is handled through application.yml properties
 * using Spring Boot's built-in StandardServletMultipartResolver (Servlet 3.0+).
 * This approach is recommended for Spring Boot 3.x applications.
 */
@SpringBootApplication
public class MappingGeneratorApplication {

    public static void main(String[] args) {
        SpringApplication.run(MappingGeneratorApplication.class, args);
    }
}
