package com.mappinggen.controller.api;

import com.mappinggen.dto.ApiResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Unified initialization data API controller
 * Provides all dropdown data in a single endpoint to eliminate redundant API calls
 */
@RestController
@RequestMapping("/api/init-data")
public class InitDataApiController {

    /**
     * Get all initialization data for the application
     * This single endpoint replaces multiple individual dropdown API calls
     * 
     * @return Comprehensive initialization data structure
     */
    @GetMapping
    public ApiResponse<Map<String, Object>> getAllInitData() {
        Map<String, Object> initData = new HashMap<>();
        
        // Business domains - used across all modules
        initData.put("businessDomains", getBusinessDomains());
        
        // Platform data
        initData.put("platformInterfaces", getPlatformInterfaces());
        initData.put("platformVersions", getPlatformVersions());
        
        // Vendor data
        initData.put("vendors", getVendors());
        initData.put("vendorInterfaces", getVendorInterfaces());
        initData.put("vendorVersions", getVendorVersions());
        
        // Code prompt specific data
        initData.put("promptVersions", getPromptVersions());
        
        // Knowledge base specific data
        initData.put("knowledgeBaseTypes", getKnowledgeBaseTypes());
        
        // Additional metadata
        initData.put("metadata", getMetadata());
        
        return ApiResponse.success(initData);
    }

    /**
     * Business domains used across all modules
     */
    private List<String> getBusinessDomains() {
        return Arrays.asList(
            "航班搜索", "订单管理", "支付处理", "用户管理", 
            "票务管理", "行李服务", "客户服务", "积分管理",
            "座位选择", "餐食服务", "保险服务", "退改签",
            "机场服务", "值机服务", "登机服务", "货运服务"
        );
    }

    /**
     * Platform interfaces available in the system
     */
    private List<String> getPlatformInterfaces() {
        return Arrays.asList(
            "FlightSearch", "OrderManagement", "PaymentProcess", "UserManagement",
            "TicketService", "BaggageService", "CustomerService", "LoyaltyProgram",
            "SeatSelection", "MealService", "InsuranceService", "RefundService",
            "AirportService", "CheckInService", "BoardingService", "CargoService"
        );
    }

    /**
     * Platform versions supported
     */
    private List<String> getPlatformVersions() {
        return Arrays.asList("v1.0", "v1.1", "v1.2", "v2.0", "v2.1", "v2.2", "v3.0", "v3.1");
    }

    /**
     * Vendor/airline companies
     */
    private List<String> getVendors() {
        return Arrays.asList(
            "东方航空", "南方航空", "国际航空", "春秋航空",
            "海南航空", "厦门航空", "深圳航空", "四川航空",
            "山东航空", "吉祥航空", "华夏航空", "西部航空",
            "天津航空", "首都航空", "奥凯航空", "九元航空"
        );
    }

    /**
     * Vendor interface names
     */
    private List<String> getVendorInterfaces() {
        return Arrays.asList(
            "FlightSearch", "OrderProcess", "PaymentGateway", "UserService",
            "TicketAPI", "BaggageAPI", "CustomerAPI", "LoyaltyAPI",
            "SeatAPI", "MealAPI", "InsuranceAPI", "RefundAPI",
            "AirportAPI", "CheckInAPI", "BoardingAPI", "CargoAPI"
        );
    }

    /**
     * Vendor versions available
     */
    private List<String> getVendorVersions() {
        return Arrays.asList("v1.0", "v1.1", "v1.2", "v1.3", "v1.4", "v1.5", "v2.0", "v2.1", "v2.2");
    }

    /**
     * Code prompt versions
     */
    private List<String> getPromptVersions() {
        return Arrays.asList("v1.0", "v1.1", "v1.2", "v2.0", "v2.1");
    }

    /**
     * Knowledge base types
     */
    private List<String> getKnowledgeBaseTypes() {
        return Arrays.asList("PLATFORM", "VENDOR");
    }

    /**
     * Additional metadata for the application
     */
    private Map<String, Object> getMetadata() {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("lastUpdated", System.currentTimeMillis());
        metadata.put("version", "1.0.0");
        metadata.put("supportedLanguages", Arrays.asList("zh-CN", "en-US"));
        
        // Mapping relationships for validation
        Map<String, Object> relationships = new HashMap<>();
        relationships.put("platformToVendorMapping", getPlatformToVendorMapping());
        metadata.put("relationships", relationships);
        
        return metadata;
    }

    /**
     * Platform to vendor interface mapping for validation
     */
    private Map<String, List<String>> getPlatformToVendorMapping() {
        Map<String, List<String>> mapping = new HashMap<>();
        mapping.put("FlightSearch", Arrays.asList("FlightSearch", "FlightAPI"));
        mapping.put("OrderManagement", Arrays.asList("OrderProcess", "OrderAPI"));
        mapping.put("PaymentProcess", Arrays.asList("PaymentGateway", "PaymentAPI"));
        mapping.put("UserManagement", Arrays.asList("UserService", "UserAPI"));
        return mapping;
    }
}
