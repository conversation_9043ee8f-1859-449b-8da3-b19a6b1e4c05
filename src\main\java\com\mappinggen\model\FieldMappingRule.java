package com.mappinggen.model;

import jakarta.validation.constraints.Size;

/**
 * Field Mapping Rule entity for individual field mapping rules
 */
public class FieldMappingRule {
    
    private Long id;
    private Long fieldMappingId;
    
    // IFS field (read-only, populated by backend)
    private String ifsField;
    
    // Airline response result (user input + tree selector)
    private String airlineResponseResult;
    
    // Supplementary logic (editable text input with character limit)
    @Size(max = 500, message = "补充逻辑长度不能超过500个字符")
    private String supplementaryLogic;
    
    // Mapping rule (populated by backend)
    private String mappingRule;
    
    // Constructors
    public FieldMappingRule() {}
    
    public FieldMappingRule(String ifsField) {
        this.ifsField = ifsField;
    }
    
    public FieldMappingRule(String ifsField, String airlineResponseResult, String supplementaryLogic) {
        this.ifsField = ifsField;
        this.airlineResponseResult = airlineResponseResult;
        this.supplementaryLogic = supplementaryLogic;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Long getFieldMappingId() { return fieldMappingId; }
    public void setFieldMappingId(Long fieldMappingId) { this.fieldMappingId = fieldMappingId; }
    
    public String getIfsField() { return ifsField; }
    public void setIfsField(String ifsField) { this.ifsField = ifsField; }
    
    public String getAirlineResponseResult() { return airlineResponseResult; }
    public void setAirlineResponseResult(String airlineResponseResult) { this.airlineResponseResult = airlineResponseResult; }
    
    public String getSupplementaryLogic() { return supplementaryLogic; }
    public void setSupplementaryLogic(String supplementaryLogic) { this.supplementaryLogic = supplementaryLogic; }
    
    public String getMappingRule() { return mappingRule; }
    public void setMappingRule(String mappingRule) { this.mappingRule = mappingRule; }
    
    // Helper method to check if rule is complete for generation
    public boolean isCompleteForGeneration() {
        return airlineResponseResult != null && !airlineResponseResult.trim().isEmpty();
    }
}
