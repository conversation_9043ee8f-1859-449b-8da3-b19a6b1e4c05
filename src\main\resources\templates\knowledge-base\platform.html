<!-- Platform Knowledge Base Content Fragment -->
<!-- This file contains Thymeleaf fragments and should not have DOCTYPE or html tags -->
<div th:fragment="content">
    <!-- Search and Filter Section -->
    <div class="search-filters">
        <form id="platformSearchForm" onsubmit="return false;">
            <div class="filter-row">
                <div th:replace="~{fragments/components :: form-select('platformBusinessDomain', 'businessDomain', '业务领域', ${businessDomains}, '', '请选择业务领域', false)}"></div>
                <div th:replace="~{fragments/components :: form-select('platformInterface', 'interfaceName', '接口', ${interfaces}, '', '请选择接口', false)}"></div>
                <div th:replace="~{fragments/components :: form-select('platformVersion', 'version', '版本', ${versions}, '', '请选择版本', false)}"></div>
            </div>
            <div class="filter-actions">
                <button type="button" class="btn btn-primary" onclick="searchPlatformKnowledge()">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    查询
                </button>
                <button type="button" class="btn btn-success" data-action="show-modal" data-target="addPlatformKnowledgeModal">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    添加知识
                </button>
            </div>
        </form>
    </div>
    
    <!-- Data Table -->
    <div class="data-table-container">
        <table class="data-table" id="platformKnowledgeTable">
            <thead>
                <tr>
                    <th>业务领域</th>
                    <th>接口</th>
                    <th>版本</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="platformKnowledgeTableBody">
                <!-- Data will be loaded via JavaScript -->
            </tbody>
        </table>
        
        <!-- Empty state -->
        <div id="platformEmptyState" class="empty-state" style="display: none;">
            <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="empty-state-title">暂无平台知识库数据</h3>
            <p class="empty-state-description">当前没有符合条件的平台知识库记录</p>
            <button class="btn btn-primary" data-action="show-modal" data-target="addPlatformKnowledgeModal">
                添加第一个知识库
            </button>
        </div>
        
        <!-- Pagination -->
        <div class="pagination" id="platformPagination" style="display: none;">
            <div class="pagination-info" id="platformPaginationInfo"></div>
            <div class="pagination-controls" id="platformPaginationControls"></div>
        </div>
    </div>
</div>

<!-- Modals -->
<div th:fragment="modals">
    <!-- Add Platform Knowledge Modal -->
    <div id="addPlatformKnowledgeModal" class="modal modal-lg">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">添加平台知识</h3>
                    <button type="button" class="modal-close" data-action="hide-modal" data-target="addPlatformKnowledgeModal">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addPlatformKnowledgeForm">
                        <div class="filter-row">
                            <div th:replace="~{fragments/components :: form-input('addPlatformBusinessDomain', 'businessDomain', '业务领域', 'text', '', '请输入业务领域', true, false, 100)}"></div>
                            <div th:replace="~{fragments/components :: form-input('addPlatformInterface', 'interfaceName', '接口', 'text', '', '请输入接口名称', true, false, 100)}"></div>
                            <div th:replace="~{fragments/components :: form-input('addPlatformVersion', 'version', '版本', 'text', '', '请输入版本号', true, false, 50)}"></div>
                        </div>
                        
                        <div class="filter-row">
                            <div th:replace="~{fragments/components :: file-upload('respExample', 'respExample', '平台返参示例文件', '.txt,.md,.json', false)}"></div>
                            <div th:replace="~{fragments/components :: file-upload('reqDoc', 'reqDoc', '平台入参文档文件', '.txt,.md,.json', false)}"></div>
                        </div>

                        <div class="filter-row">
                            <div th:replace="~{fragments/components :: file-upload('respDoc', 'respDoc', '平台返参文档文件', '.txt,.md,.json', false)}"></div>
                            <div th:replace="~{fragments/components :: file-upload('reqBeanPrompt', 'reqBeanPrompt', '入参Bean生成Prompt文件', '.txt,.md', false)}"></div>
                        </div>

                        <div class="filter-row">
                            <div th:replace="~{fragments/components :: file-upload('respBeanPrompt', 'respBeanPrompt', '返参Bean生成Prompt文件', '.txt,.md', false)}"></div>
                            <div th:replace="~{fragments/components :: file-upload('codeGenPrompt', 'codeGenPrompt', '代码生成Prompt文件', '.txt,.md', false)}"></div>
                        </div>

                        <div class="filter-row">
                            <div th:replace="~{fragments/components :: file-upload('codeTemplateDoc', 'codeTemplateDoc', '代码模板文档文件', '.txt,.java,.js', false)}"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-action="hide-modal" data-target="addPlatformKnowledgeModal">
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" onclick="savePlatformKnowledge()">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- View Platform Documents Modal -->
    <div id="viewPlatformDocumentsModal" class="modal modal-xl">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">查看平台文档</h3>
                    <button type="button" class="modal-close" data-action="hide-modal" data-target="viewPlatformDocumentsModal">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Knowledge base info -->
                    <div class="filter-row mb-4">
                        <div class="form-group">
                            <label class="form-label">业务领域</label>
                            <div class="form-control" id="viewPlatformBusinessDomain" readonly></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">接口</label>
                            <div class="form-control" id="viewPlatformInterface" readonly></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">版本</label>
                            <div class="form-control" id="viewPlatformVersion" readonly></div>
                        </div>
                    </div>
                    
                    <!-- Document cards -->
                    <div class="document-cards" id="platformDocumentCards">
                        <!-- Document cards will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-action="hide-modal" data-target="viewPlatformDocumentsModal">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Confirm Delete Modal -->
    <div id="confirmDeletePlatformModal" class="modal modal-sm">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">确认删除</h3>
                    <button type="button" class="modal-close" data-action="hide-modal" data-target="confirmDeletePlatformModal">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                    <p>确定要删除这个平台知识库记录吗？此操作不可撤销。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-action="hide-modal" data-target="confirmDeletePlatformModal">
                        取消
                    </button>
                    <button type="button" class="btn btn-error" onclick="confirmDeletePlatformKnowledge()">
                        确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
