/**
 * Unified Initialization Data Module
 * Centralizes all dropdown data loading to eliminate redundant API calls
 */

// Global initialization data cache
window.MappingGen = window.MappingGen || {};
window.MappingGen.InitData = {
    // Cache for initialization data
    cache: null,
    
    // Loading state
    isLoading: false,
    
    // Callbacks waiting for data
    pendingCallbacks: [],

    /**
     * Load all initialization data from unified endpoint
     * @returns {Promise<Object>} Complete initialization data
     */
    async load() {
        // Return cached data if available
        if (this.cache) {
            return this.cache;
        }

        // If already loading, wait for completion
        if (this.isLoading) {
            return new Promise((resolve, reject) => {
                this.pendingCallbacks.push({ resolve, reject });
            });
        }

        this.isLoading = true;

        try {
            console.log('Loading unified initialization data...');
            const response = await MappingGen.API.get('/api/init-data');
            
            if (response.success) {
                this.cache = response.data;
                console.log('Initialization data loaded successfully:', this.cache);
                
                // Resolve all pending callbacks
                this.pendingCallbacks.forEach(callback => callback.resolve(this.cache));
                this.pendingCallbacks = [];
                
                return this.cache;
            } else {
                throw new Error(response.message || 'Failed to load initialization data');
            }
        } catch (error) {
            console.error('Error loading initialization data:', error);
            
            // Reject all pending callbacks
            this.pendingCallbacks.forEach(callback => callback.reject(error));
            this.pendingCallbacks = [];
            
            throw error;
        } finally {
            this.isLoading = false;
        }
    },

    /**
     * Get specific data type from cache
     * @param {string} dataType - Type of data to retrieve
     * @returns {Array|Object} Requested data
     */
    get(dataType) {
        if (!this.cache) {
            console.warn('Initialization data not loaded yet. Call load() first.');
            return [];
        }
        return this.cache[dataType] || [];
    },

    /**
     * Populate a select element with options
     * @param {string} selectId - ID of the select element
     * @param {string} dataType - Type of data to populate
     * @param {Object} options - Additional options
     */
    populateSelect(selectId, dataType, options = {}) {
        const select = document.getElementById(selectId);
        if (!select) {
            console.warn(`Select element with ID '${selectId}' not found`);
            return;
        }

        const data = this.get(dataType);
        if (!Array.isArray(data)) {
            console.warn(`Data type '${dataType}' is not an array`);
            return;
        }

        // Clear existing options except the first one (placeholder)
        const firstOption = select.firstElementChild;
        select.innerHTML = '';
        if (firstOption && firstOption.value === '') {
            select.appendChild(firstOption);
        }

        // Add new options
        data.forEach(item => {
            const option = document.createElement('option');
            option.value = item;
            option.textContent = item;
            
            // Set selected if specified
            if (options.selected === item) {
                option.selected = true;
            }
            
            select.appendChild(option);
        });

        console.log(`Populated select '${selectId}' with ${data.length} ${dataType} options`);
    },

    /**
     * Populate multiple select elements at once
     * @param {Object} selectMappings - Object mapping select IDs to data types
     * @param {Object} selectedValues - Object mapping select IDs to selected values
     */
    populateMultipleSelects(selectMappings, selectedValues = {}) {
        Object.entries(selectMappings).forEach(([selectId, dataType]) => {
            this.populateSelect(selectId, dataType, {
                selected: selectedValues[selectId]
            });
        });
    },

    /**
     * Initialize all dropdowns for a specific page
     * @param {string} pageType - Type of page (mapping-association, field-mapping, code-prompt)
     * @param {Object} selectedValues - Pre-selected values
     */
    async initializePageDropdowns(pageType, selectedValues = {}) {
        try {
            // Ensure data is loaded
            await this.load();

            // Define common dropdown mappings
            const commonMappings = {
                'businessDomain': 'businessDomains',
                'platformInterface': 'platformInterfaces',
                'platformVersion': 'platformVersions',
                'vendor': 'vendors',
                'vendorInterface': 'vendorInterfaces',
                'vendorVersion': 'vendorVersions'
            };

            // Page-specific mappings
            const pageMappings = {
                'mapping-association': {
                    ...commonMappings,
                    // Add form selects
                    'addBusinessDomain': 'businessDomains',
                    'addPlatformInterface': 'platformInterfaces',
                    'addPlatformVersion': 'platformVersions',
                    'addVendor': 'vendors',
                    'addVendorInterface': 'vendorInterfaces',
                    'addVendorVersion': 'vendorVersions'
                },
                'field-mapping': commonMappings,
                'code-prompt': {
                    ...commonMappings,
                    'promptVersion': 'promptVersions'
                },
                'knowledge-base': {
                    'businessDomain': 'businessDomains',
                    'platformInterface': 'platformInterfaces',
                    'platformVersion': 'platformVersions',
                    'vendor': 'vendors',
                    'vendorInterface': 'vendorInterfaces',
                    'vendorVersion': 'vendorVersions',
                    'type': 'knowledgeBaseTypes'
                }
            };

            const mappings = pageMappings[pageType] || commonMappings;
            this.populateMultipleSelects(mappings, selectedValues);

            console.log(`Initialized dropdowns for page: ${pageType}`);
        } catch (error) {
            console.error(`Error initializing dropdowns for ${pageType}:`, error);
            MappingGen.Utils.showAlert('加载下拉数据失败：' + error.message, 'error');
        }
    },

    /**
     * Get validation data for relationships
     * @returns {Object} Validation data
     */
    getValidationData() {
        const metadata = this.get('metadata');
        return metadata.relationships || {};
    },

    /**
     * Clear cache (useful for testing or forced refresh)
     */
    clearCache() {
        this.cache = null;
        console.log('Initialization data cache cleared');
    },

    /**
     * Get cache status
     * @returns {Object} Cache status information
     */
    getCacheStatus() {
        return {
            isCached: !!this.cache,
            isLoading: this.isLoading,
            pendingCallbacks: this.pendingCallbacks.length,
            cacheSize: this.cache ? Object.keys(this.cache).length : 0
        };
    }
};

/**
 * Convenience function for backward compatibility
 * @deprecated Use MappingGen.InitData.populateSelect instead
 */
window.populateSelect = function(selectId, options, selectedValue = null) {
    console.warn('populateSelect is deprecated. Use MappingGen.InitData.populateSelect instead.');
    
    const select = document.getElementById(selectId);
    if (!select) return;

    // Clear existing options except the first one (placeholder)
    const firstOption = select.firstElementChild;
    select.innerHTML = '';
    if (firstOption && firstOption.value === '') {
        select.appendChild(firstOption);
    }

    // Add new options
    if (Array.isArray(options)) {
        options.forEach(item => {
            const option = document.createElement('option');
            option.value = item;
            option.textContent = item;
            if (selectedValue === item) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    }
};
