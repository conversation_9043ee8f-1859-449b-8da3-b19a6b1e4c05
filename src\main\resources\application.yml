server:
  port: 8080
  servlet:
    context-path: /mapping-gen
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: mapping-generator
  
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html
    servlet:
      content-type: text/html
    # Additional configuration to prevent template parsing issues
    check-template-location: true
    enable-spring-el-compiler: false
  
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB
      file-size-threshold: 1MB
  
  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 0

logging:
  level:
    com.mappinggen: DEBUG
    org.springframework.web: DEBUG
    org.thymeleaf: DEBUG
    org.thymeleaf.TemplateEngine.CONFIG: INFO
    org.thymeleaf.TemplateEngine.TIMER: INFO
    org.thymeleaf.TemplateEngine.cache.TEMPLATE_CACHE: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
