package com.mappinggen.controller.newApi;

import com.mappinggen.dto.ApiResponse;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 供应商接口控制器
 * 提供供应商接口的增删改查功能
 */
@RestController
@RequestMapping("/api/supplier-interface")
public class SupplierInterfaceController {

    // 模拟数据存储
    private static final Map<Long, SupplierInterfaceResponse> supplierInterfaces = new HashMap<>();
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static Long nextId = 1L;

    static {
        initializeMockData();
    }

    /**
     * 初始化模拟数据
     */
    private static void initializeMockData() {
        // 机票业务域 - 东航 - 搜索接口
        SupplierInterfaceResponse supplier1 = new SupplierInterfaceResponse();
        supplier1.setId(nextId++);
        supplier1.setBusinessDomain("机票");
        supplier1.setSupplierName("东航");
        supplier1.setInterfaceName("搜索接口");
        supplier1.setVersion("v1.0");
        supplier1.setRespFields("{\"fields\":[{\"name\":\"flightNo\",\"type\":\"string\",\"description\":\"航班号\"},{\"name\":\"departureTime\",\"type\":\"string\",\"description\":\"出发时间\"},{\"name\":\"arrivalTime\",\"type\":\"string\",\"description\":\"到达时间\"},{\"name\":\"price\",\"type\":\"number\",\"description\":\"价格\"}]}");
        supplier1.setReqExample("{\"departure\":\"PEK\",\"arrival\":\"SHA\",\"departureDate\":\"2024-02-01\"}");
        supplier1.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"flights\":[{\"flightNo\":\"MU1234\",\"departureTime\":\"08:30\",\"arrivalTime\":\"10:45\",\"price\":980}]}}");
        supplier1.setReqDoc("## 东航搜索API入参文档\\n\\n### 接口说明\\n- 供应商：东航\\n- 接口名称：航班搜索\\n- 请求方式：POST\\n\\n### 请求参数\\n| 参数名 | 类型 | 必填 | 说明 |\\n|--------|------|------|------|\\n| departure | String | 是 | 出发城市代码 |\\n| arrival | String | 是 | 到达城市代码 |\\n| departureDate | String | 是 | 出发日期 |");
        supplier1.setRespDoc("## 东航搜索API返参文档\\n\\n### 响应说明\\n- 响应格式：JSON\\n- 字符编码：UTF-8\\n\\n### 响应参数\\n| 参数名 | 类型 | 说明 |\\n|--------|------|------|\\n| code | String | 响应码 |\\n| message | String | 响应消息 |\\n| data | Object | 航班列表数据 |");
        supplier1.setOperator("王五");
        supplier1.setCreateTime(LocalDateTime.now().minusDays(20).format(DATE_FORMAT));
        supplier1.setUpdateTime(LocalDateTime.now().minusDays(15).format(DATE_FORMAT));
        supplierInterfaces.put(supplier1.getId(), supplier1);

        // 机票业务域 - 东航 - 搜索接口 v1.1
        SupplierInterfaceResponse supplier2 = new SupplierInterfaceResponse();
        supplier2.setId(nextId++);
        supplier2.setBusinessDomain("机票");
        supplier2.setSupplierName("东航");
        supplier2.setInterfaceName("搜索接口");
        supplier2.setVersion("v1.1");
        supplier2.setRespFields("{\"fields\":[{\"name\":\"flightNo\",\"type\":\"string\"},{\"name\":\"departureTime\",\"type\":\"string\"},{\"name\":\"arrivalTime\",\"type\":\"string\"},{\"name\":\"price\",\"type\":\"number\"},{\"name\":\"aircraft\",\"type\":\"string\"},{\"name\":\"availableSeats\",\"type\":\"number\"}]}");
        supplier2.setReqExample("{\"departure\":\"PEK\",\"arrival\":\"SHA\",\"departureDate\":\"2024-02-01\",\"returnDate\":\"2024-02-05\",\"tripType\":\"ROUND_TRIP\"}");
        supplier2.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"totalResults\":45,\"flights\":[{\"flightNo\":\"MU1234\",\"departureTime\":\"08:30\",\"arrivalTime\":\"10:45\",\"price\":980,\"aircraft\":\"A321\",\"availableSeats\":32}]}}");
        supplier2.setReqDoc("## 东航搜索API v1.1入参文档\\n\\n### 新增功能\\n- 往返程支持\\n- 高级筛选功能\\n\\n### 请求参数\\n| 参数名 | 类型 | 必填 | 说明 |\\n|--------|------|------|------|\\n| tripType | String | 否 | 行程类型 |\\n| returnDate | String | 否 | 返程日期 |");
        supplier2.setRespDoc("## 东航搜索API v1.1返参文档\\n\\n### 新增字段\\n| 参数名 | 类型 | 说明 |\\n|--------|------|------|\\n| totalResults | Integer | 总结果数 |\\n| aircraft | String | 机型 |\\n| availableSeats | Integer | 可用座位数 |");
        supplier2.setOperator("李四");
        supplier2.setCreateTime(LocalDateTime.now().minusDays(18).format(DATE_FORMAT));
        supplier2.setUpdateTime(LocalDateTime.now().minusDays(12).format(DATE_FORMAT));
        supplierInterfaces.put(supplier2.getId(), supplier2);

        // 机票业务域 - 东航 - 预订接口
        SupplierInterfaceResponse supplierBooking1 = new SupplierInterfaceResponse();
        supplierBooking1.setId(nextId++);
        supplierBooking1.setBusinessDomain("机票");
        supplierBooking1.setSupplierName("东航");
        supplierBooking1.setInterfaceName("预订接口");
        supplierBooking1.setVersion("v1.0");
        supplierBooking1.setRespFields("{\"fields\":[{\"name\":\"bookingId\",\"type\":\"string\"},{\"name\":\"status\",\"type\":\"string\"},{\"name\":\"totalAmount\",\"type\":\"number\"}]}");
        supplierBooking1.setReqExample("{\"flightNo\":\"MU1234\",\"passengers\":[{\"name\":\"张三\",\"idCard\":\"123456789012345678\",\"phone\":\"***********\"}],\"contactInfo\":{\"email\":\"<EMAIL>\"}}");
        supplierBooking1.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"bookingId\":\"MU20240201001\",\"status\":\"CONFIRMED\",\"totalAmount\":980.00}}");
        supplierBooking1.setReqDoc("## 东航预订API入参文档\\n\\n### 主要功能\\n1. 创建预订\\n2. 乘客信息验证\\n3. 座位分配");
        supplierBooking1.setRespDoc("## 东航预订API返参文档\\n\\n### 预订状态\\n- PENDING: 待确认\\n- CONFIRMED: 已确认\\n- CANCELLED: 已取消");
        supplierBooking1.setOperator("赵六");
        supplierBooking1.setCreateTime(LocalDateTime.now().minusDays(16).format(DATE_FORMAT));
        supplierBooking1.setUpdateTime(LocalDateTime.now().minusDays(10).format(DATE_FORMAT));
        supplierInterfaces.put(supplierBooking1.getId(), supplierBooking1);

        // 机票业务域 - 南航 - 搜索接口
        SupplierInterfaceResponse supplier3 = new SupplierInterfaceResponse();
        supplier3.setId(nextId++);
        supplier3.setBusinessDomain("机票");
        supplier3.setSupplierName("南航");
        supplier3.setInterfaceName("搜索接口");
        supplier3.setVersion("v1.0");
        supplier3.setRespFields("{\"fields\":[{\"name\":\"flight\",\"type\":\"string\"},{\"name\":\"depTime\",\"type\":\"string\"},{\"name\":\"arrTime\",\"type\":\"string\"},{\"name\":\"cost\",\"type\":\"number\"}]}");
        supplier3.setReqExample("{\"from\":\"BJS\",\"to\":\"GZ\",\"date\":\"2024-02-01\"}");
        supplier3.setRespExample("{\"status\":\"success\",\"data\":[{\"flight\":\"CZ3101\",\"depTime\":\"09:00\",\"arrTime\":\"12:30\",\"cost\":1200}]}");
        supplier3.setReqDoc("## 南航搜索API入参文档\\n\\n### 接口特点\\n- 简化的参数结构\\n- 快速响应\\n\\n### 请求参数\\n| 参数名 | 类型 | 必填 | 说明 |\\n|--------|------|------|------|\\n| from | String | 是 | 出发城市 |\\n| to | String | 是 | 到达城市 |\\n| date | String | 是 | 出发日期 |");
        supplier3.setRespDoc("## 南航搜索API返参文档\\n\\n### 响应特点\\n南航使用简化的响应格式，字段名较短");
        supplier3.setOperator("孙七");
        supplier3.setCreateTime(LocalDateTime.now().minusDays(14).format(DATE_FORMAT));
        supplier3.setUpdateTime(LocalDateTime.now().minusDays(8).format(DATE_FORMAT));
        supplierInterfaces.put(supplier3.getId(), supplier3);

        // 酒店业务域 - 携程 - 搜索接口
        SupplierInterfaceResponse hotelSupplier = new SupplierInterfaceResponse();
        hotelSupplier.setId(nextId++);
        hotelSupplier.setBusinessDomain("酒店");
        hotelSupplier.setSupplierName("携程");
        hotelSupplier.setInterfaceName("搜索接口");
        hotelSupplier.setVersion("v1.0");
        hotelSupplier.setRespFields("{\"fields\":[{\"name\":\"hotelId\",\"type\":\"string\"},{\"name\":\"hotelName\",\"type\":\"string\"},{\"name\":\"price\",\"type\":\"number\"},{\"name\":\"rating\",\"type\":\"number\"}]}");
        hotelSupplier.setReqExample("{\"city\":\"北京\",\"checkIn\":\"2024-02-01\",\"checkOut\":\"2024-02-03\",\"rooms\":1}");
        hotelSupplier.setRespExample("{\"code\":\"0\",\"message\":\"success\",\"data\":{\"hotels\":[{\"hotelId\":\"HTL001\",\"hotelName\":\"北京国际酒店\",\"price\":588,\"rating\":4.5}]}}");
        hotelSupplier.setReqDoc("## 携程酒店搜索API入参文档\\n\\n### 搜索参数\\n- 城市选择\\n- 入住/离店日期\\n- 房间数量");
        hotelSupplier.setRespDoc("## 携程酒店搜索API返参文档\\n\\n### 酒店信息\\n包含酒店基本信息、价格和评分");
        hotelSupplier.setOperator("周八");
        hotelSupplier.setCreateTime(LocalDateTime.now().minusDays(12).format(DATE_FORMAT));
        hotelSupplier.setUpdateTime(LocalDateTime.now().minusDays(6).format(DATE_FORMAT));
        supplierInterfaces.put(hotelSupplier.getId(), hotelSupplier);

        // 酒店业务域 - 去哪儿 - 搜索接口
        SupplierInterfaceResponse hotelSupplier2 = new SupplierInterfaceResponse();
        hotelSupplier2.setId(nextId++);
        hotelSupplier2.setBusinessDomain("酒店");
        hotelSupplier2.setSupplierName("去哪儿");
        hotelSupplier2.setInterfaceName("搜索接口");
        hotelSupplier2.setVersion("v1.0");
        hotelSupplier2.setRespFields("{\"fields\":[{\"name\":\"id\",\"type\":\"string\"},{\"name\":\"name\",\"type\":\"string\"},{\"name\":\"minPrice\",\"type\":\"number\"},{\"name\":\"star\",\"type\":\"string\"}]}");
        hotelSupplier2.setReqExample("{\"destination\":\"上海\",\"arrivalDate\":\"2024-02-01\",\"departureDate\":\"2024-02-03\"}");
        hotelSupplier2.setRespExample("{\"success\":true,\"result\":[{\"id\":\"QNR001\",\"name\":\"上海外滩酒店\",\"minPrice\":398,\"star\":\"四星级\"}]}");
        hotelSupplier2.setReqDoc("## 去哪儿酒店搜索API入参文档\\n\\n### API特色\\n- 性价比筛选\\n- 星级分类");
        hotelSupplier2.setRespDoc("## 去哪儿酒店搜索API返参文档\\n\\n### 价格信息\\n提供最低价格和星级信息");
        hotelSupplier2.setOperator("吴九");
        hotelSupplier2.setCreateTime(LocalDateTime.now().minusDays(10).format(DATE_FORMAT));
        hotelSupplier2.setUpdateTime(LocalDateTime.now().minusDays(4).format(DATE_FORMAT));
        supplierInterfaces.put(hotelSupplier2.getId(), hotelSupplier2);

        // 火车票业务域 - 12306 - 查询接口
        SupplierInterfaceResponse trainSupplier = new SupplierInterfaceResponse();
        trainSupplier.setId(nextId++);
        trainSupplier.setBusinessDomain("火车票");
        trainSupplier.setSupplierName("12306");
        trainSupplier.setInterfaceName("查询接口");
        trainSupplier.setVersion("v1.0");
        trainSupplier.setRespFields("{\"fields\":[{\"name\":\"trainCode\",\"type\":\"string\"},{\"name\":\"startTime\",\"type\":\"string\"},{\"name\":\"endTime\",\"type\":\"string\"},{\"name\":\"ticketPrice\",\"type\":\"number\"}]}");
        trainSupplier.setReqExample("{\"fromStation\":\"北京\",\"toStation\":\"上海\",\"travelDate\":\"2024-02-01\"}");
        trainSupplier.setRespExample("{\"resultCode\":\"00\",\"resultMessage\":\"成功\",\"trains\":[{\"trainCode\":\"G101\",\"startTime\":\"08:00\",\"endTime\":\"12:28\",\"ticketPrice\":553}]}");
        trainSupplier.setReqDoc("## 12306查询API入参文档\\n\\n### 官方接口\\n- 权威数据源\\n- 实时余票信息");
        trainSupplier.setRespDoc("## 12306查询API返参文档\\n\\n### 车次信息\\n包含车次、时间和票价信息");
        trainSupplier.setOperator("郑十");
        trainSupplier.setCreateTime(LocalDateTime.now().minusDays(8).format(DATE_FORMAT));
        trainSupplier.setUpdateTime(LocalDateTime.now().minusDays(3).format(DATE_FORMAT));
        supplierInterfaces.put(trainSupplier.getId(), trainSupplier);

        // 支付业务域 - 支付宝 - 支付接口
        SupplierInterfaceResponse paymentSupplier = new SupplierInterfaceResponse();
        paymentSupplier.setId(nextId++);
        paymentSupplier.setBusinessDomain("支付");
        paymentSupplier.setSupplierName("支付宝");
        paymentSupplier.setInterfaceName("支付接口");
        paymentSupplier.setVersion("v1.0");
        paymentSupplier.setRespFields("{\"fields\":[{\"name\":\"tradeNo\",\"type\":\"string\"},{\"name\":\"payStatus\",\"type\":\"string\"},{\"name\":\"amount\",\"type\":\"number\"}]}");
        paymentSupplier.setReqExample("{\"outTradeNo\":\"ORDER20240201001\",\"totalAmount\":\"1280.00\",\"subject\":\"机票支付\"}");
        paymentSupplier.setRespExample("{\"code\":\"10000\",\"msg\":\"Success\",\"tradeNo\":\"2024020122001234567890\",\"payStatus\":\"TRADE_SUCCESS\",\"amount\":1280.00}");
        paymentSupplier.setReqDoc("## 支付宝支付API入参文档\\n\\n### 支付流程\\n1. 创建支付订单\\n2. 用户确认支付\\n3. 支付结果通知");
        paymentSupplier.setRespDoc("## 支付宝支付API返参文档\\n\\n### 支付状态\\n- TRADE_SUCCESS: 支付成功\\n- TRADE_CLOSED: 交易关闭\\n- WAIT_BUYER_PAY: 等待付款");
        paymentSupplier.setOperator("钱十一");
        paymentSupplier.setCreateTime(LocalDateTime.now().minusDays(6).format(DATE_FORMAT));
        paymentSupplier.setUpdateTime(LocalDateTime.now().minusDays(2).format(DATE_FORMAT));
        supplierInterfaces.put(paymentSupplier.getId(), paymentSupplier);

        // 支付业务域 - 微信支付 - 支付接口
        SupplierInterfaceResponse wechatPay = new SupplierInterfaceResponse();
        wechatPay.setId(nextId++);
        wechatPay.setBusinessDomain("支付");
        wechatPay.setSupplierName("微信支付");
        wechatPay.setInterfaceName("支付接口");
        wechatPay.setVersion("v1.0");
        wechatPay.setRespFields("{\"fields\":[{\"name\":\"transactionId\",\"type\":\"string\"},{\"name\":\"tradeState\",\"type\":\"string\"},{\"name\":\"totalFee\",\"type\":\"number\"}]}");
        wechatPay.setReqExample("{\"outTradeNo\":\"ORDER20240201002\",\"totalFee\":1580,\"body\":\"火车票支付\",\"notifyUrl\":\"https://api.example.com/notify\"}");
        wechatPay.setRespExample("{\"returnCode\":\"SUCCESS\",\"resultCode\":\"SUCCESS\",\"transactionId\":\"4200001234567890\",\"tradeState\":\"SUCCESS\",\"totalFee\":1580}");
        wechatPay.setReqDoc("## 微信支付API入参文档\\n\\n### 支付方式\\n- APP支付\\n- 小程序支付\\n- H5支付");
        wechatPay.setRespDoc("## 微信支付API返参文档\\n\\n### 交易状态\\n- SUCCESS: 支付成功\\n- REFUND: 转入退款\\n- NOTPAY: 未支付");
        wechatPay.setOperator("李十二");
        wechatPay.setCreateTime(LocalDateTime.now().minusDays(4).format(DATE_FORMAT));
        wechatPay.setUpdateTime(LocalDateTime.now().minusDays(1).format(DATE_FORMAT));
        supplierInterfaces.put(wechatPay.getId(), wechatPay);

        // 用户管理业务域 - 微信 - 用户接口
        SupplierInterfaceResponse wechatUser = new SupplierInterfaceResponse();
        wechatUser.setId(nextId++);
        wechatUser.setBusinessDomain("用户管理");
        wechatUser.setSupplierName("微信");
        wechatUser.setInterfaceName("用户接口");
        wechatUser.setVersion("v1.0");
        wechatUser.setRespFields("{\"fields\":[{\"name\":\"openid\",\"type\":\"string\"},{\"name\":\"nickname\",\"type\":\"string\"},{\"name\":\"headimgurl\",\"type\":\"string\"},{\"name\":\"login_time\",\"type\":\"number\"}]}");
        wechatUser.setReqExample("{\"appid\":\"wx1234567890\",\"secret\":\"abcdef1234567890\",\"code\":\"AUTH_CODE\"}");
        wechatUser.setRespExample("{\"openid\":\"oUpF8uMuAJO_M2pxb1Q9zNjWeS6o\",\"nickname\":\"张三\",\"headimgurl\":\"http://thirdwx.qlogo.cn/mmopen/g3MonUZtNHkdmzicIlibx6iaFqAc56vxLSUfpb6n5WKSYVY0ChQKkiaJSgQ1dZuTOgvLLrhJbERQQ4eMsv84eavHiaiceqxibJxCfHe/0\",\"login_time\":1640995200}");
        wechatUser.setReqDoc("## 微信用户接口API入参文档\\n\\n### 授权参数\\n- 应用ID\\n- 应用密钥\\n- 授权码");
        wechatUser.setRespDoc("## 微信用户接口API返参文档\\n\\n### 用户信息\\n包含用户唯一标识、昵称、头像等基本信息");
        wechatUser.setOperator("赵十三");
        wechatUser.setCreateTime(LocalDateTime.now().minusDays(3).format(DATE_FORMAT));
        wechatUser.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        supplierInterfaces.put(wechatUser.getId(), wechatUser);

        // 机票业务域 - 国航 - 搜索接口
        SupplierInterfaceResponse airChina = new SupplierInterfaceResponse();
        airChina.setId(nextId++);
        airChina.setBusinessDomain("机票");
        airChina.setSupplierName("国航");
        airChina.setInterfaceName("搜索接口");
        airChina.setVersion("v1.0");
        airChina.setRespFields("{\"fields\":[{\"name\":\"flightNumber\",\"type\":\"string\"},{\"name\":\"deptTime\",\"type\":\"string\"},{\"name\":\"arriTime\",\"type\":\"string\"},{\"name\":\"fare\",\"type\":\"number\"}]}");
        airChina.setReqExample("{\"origin\":\"PEK\",\"destination\":\"LAX\",\"departDate\":\"2024-02-01\"}");
        airChina.setRespExample("{\"responseCode\":\"0000\",\"responseMsg\":\"成功\",\"flightList\":[{\"flightNumber\":\"CA987\",\"deptTime\":\"14:30\",\"arriTime\":\"10:45+1\",\"fare\":8800}]}");
        airChina.setReqDoc("## 国航搜索API入参文档\\n\\n### 国际航线\\n- 支持跨时区查询\\n- 多舱位选择");
        airChina.setRespDoc("## 国航搜索API返参文档\\n\\n### 时间格式\\n时间后+1表示次日到达");
        airChina.setOperator("张十三");
        airChina.setCreateTime(LocalDateTime.now().minusDays(2).format(DATE_FORMAT));
        airChina.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        supplierInterfaces.put(airChina.getId(), airChina);

        // 酒店业务域 - 飞猪 - 预订接口
        SupplierInterfaceResponse fliggyHotel = new SupplierInterfaceResponse();
        fliggyHotel.setId(nextId++);
        fliggyHotel.setBusinessDomain("酒店");
        fliggyHotel.setSupplierName("飞猪");
        fliggyHotel.setInterfaceName("预订接口");
        fliggyHotel.setVersion("v1.0");
        fliggyHotel.setRespFields("{\"fields\":[{\"name\":\"orderId\",\"type\":\"string\"},{\"name\":\"orderStatus\",\"type\":\"string\"},{\"name\":\"roomInfo\",\"type\":\"object\"}]}");
        fliggyHotel.setReqExample("{\"hotelId\":\"FZ001\",\"roomType\":\"大床房\",\"checkInDate\":\"2024-02-01\",\"checkOutDate\":\"2024-02-03\",\"guestInfo\":{\"name\":\"李明\",\"phone\":\"***********\"}}");
        fliggyHotel.setRespExample("{\"success\":true,\"data\":{\"orderId\":\"FZ20240201001\",\"orderStatus\":\"CONFIRMED\",\"roomInfo\":{\"roomNo\":\"1806\",\"roomType\":\"大床房\"}}}");
        fliggyHotel.setReqDoc("## 飞猪酒店预订API入参文档\\n\\n### 预订信息\\n- 酒店和房型选择\\n- 客人信息填写");
        fliggyHotel.setRespDoc("## 飞猪酒店预订API返参文档\\n\\n### 订单确认\\n返回订单号和房间信息");
        fliggyHotel.setOperator("王十四");
        fliggyHotel.setCreateTime(LocalDateTime.now().format(DATE_FORMAT));
        fliggyHotel.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        supplierInterfaces.put(fliggyHotel.getId(), fliggyHotel);
    }

    /**
     * 3.1 查询供应商接口树形结构
     * GET /api/supplier-interface/tree
     */
    @GetMapping("/tree")
    public ApiResponse<List<SupplierBusinessDomainTreeResponse>> getTree() {
        try {
            // 按业务域分组
            Map<String, SupplierBusinessDomainTreeResponse> domainMap = new LinkedHashMap<>();
            
            for (SupplierInterfaceResponse supplier : supplierInterfaces.values()) {
                String domain = supplier.getBusinessDomain();
                
                // 获取或创建业务域
                SupplierBusinessDomainTreeResponse domainResponse = domainMap.computeIfAbsent(domain, 
                    k -> {
                        SupplierBusinessDomainTreeResponse d = new SupplierBusinessDomainTreeResponse();
                        d.setBusinessDomain(k);
                        d.setSuppliers(new ArrayList<>());
                        return d;
                    });
                
                // 查找或创建供应商
                String supplierName = supplier.getSupplierName();
                SupplierTreeNode supplierNode = domainResponse.getSuppliers().stream()
                    .filter(s -> s.getSupplierName().equals(supplierName))
                    .findFirst()
                    .orElse(null);
                
                if (supplierNode == null) {
                    supplierNode = new SupplierTreeNode();
                    supplierNode.setSupplierName(supplierName);
                    supplierNode.setInterfaces(new ArrayList<>());
                    domainResponse.getSuppliers().add(supplierNode);
                }
                
                // 查找或创建接口
                String interfaceName = supplier.getInterfaceName();
                SupplierInterfaceTreeNode interfaceNode = supplierNode.getInterfaces().stream()
                    .filter(i -> i.getInterfaceName().equals(interfaceName))
                    .findFirst()
                    .orElse(null);
                
                if (interfaceNode == null) {
                    interfaceNode = new SupplierInterfaceTreeNode();
                    interfaceNode.setInterfaceName(interfaceName);
                    interfaceNode.setVersions(new ArrayList<>());
                    supplierNode.getInterfaces().add(interfaceNode);
                }
                
                // 添加版本
                SupplierVersionTreeNode versionNode = new SupplierVersionTreeNode();
                versionNode.setId(supplier.getId());
                versionNode.setVersion(supplier.getVersion());
                interfaceNode.getVersions().add(versionNode);
            }
            
            return ApiResponse.success(new ArrayList<>(domainMap.values()));
        } catch (Exception e) {
            return ApiResponse.error("查询供应商接口树形结构失败：" + e.getMessage());
        }
    }

    /**
     * 3.2 分页查询供应商接口列表
     * GET /api/supplier-interface/list
     */
    @GetMapping("/list")
    public ApiResponse<SupplierInterfacePageResult> list(
            @RequestParam(required = false) String businessDomain,
            @RequestParam(required = false) String supplierName,
            @RequestParam(required = false) String interfaceName,
            @RequestParam(required = false) String version,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        
        try {
            // 过滤数据
            List<SupplierInterfaceResponse> filtered = supplierInterfaces.values().stream()
                    .filter(s -> businessDomain == null || businessDomain.equals(s.getBusinessDomain()))
                    .filter(s -> supplierName == null || supplierName.equals(s.getSupplierName()))
                    .filter(s -> interfaceName == null || interfaceName.equals(s.getInterfaceName()))
                    .filter(s -> version == null || version.equals(s.getVersion()))
                    .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                    .toList();

            // 分页计算
            int total = filtered.size();
            int totalPages = (int) Math.ceil((double) total / pageSize);
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, total);
            
            List<SupplierInterfaceResponse> records = start < total ? 
                filtered.subList(start, end) : new ArrayList<>();

            // 构建分页结果
            SupplierInterfacePageResult pageResult = new SupplierInterfacePageResult();
            pageResult.setRecords(records);
            pageResult.setTotal(total);
            pageResult.setPageNum(pageNum);
            pageResult.setPageSize(pageSize);
            pageResult.setTotalPages(totalPages);
            pageResult.setHasNext(pageNum < totalPages);
            pageResult.setHasPrevious(pageNum > 1);

            return ApiResponse.success(pageResult);
        } catch (Exception e) {
            return ApiResponse.error("查询供应商接口列表失败：" + e.getMessage());
        }
    }

    /**
     * 3.3 根据ID查询供应商接口
     * GET /api/supplier-interface/{id}
     */
    @GetMapping("/{id}")
    public ApiResponse<SupplierInterfaceResponse> getById(@PathVariable Long id) {
        try {
            SupplierInterfaceResponse supplier = supplierInterfaces.get(id);
            if (supplier == null) {
                return ApiResponse.error("供应商接口不存在");
            }
            return ApiResponse.success(supplier);
        } catch (Exception e) {
            return ApiResponse.error("查询供应商接口失败：" + e.getMessage());
        }
    }

    /**
     * 3.4 添加供应商接口
     * POST /api/supplier-interface
     */
    @PostMapping
    public ApiResponse<Void> create(
            @RequestParam(required = false) String businessDomain,
            @RequestParam(required = false) String supplierName,
            @RequestParam(required = false) String interfaceName,
            @RequestParam(required = false) String version,
            @RequestParam(required = false) String respFields,
            @RequestParam(required = false) String operator,
            @RequestParam(required = false) MultipartFile reqExample,
            @RequestParam(required = false) MultipartFile respExample,
            @RequestParam(required = false) MultipartFile reqDoc,
            @RequestParam(required = false) MultipartFile respDoc) {
        
        try {
            SupplierInterfaceResponse supplier = new SupplierInterfaceResponse();
            supplier.setId(nextId++);
            supplier.setBusinessDomain(businessDomain != null ? businessDomain : "默认业务域");
            supplier.setSupplierName(supplierName != null ? supplierName : "默认供应商");
            supplier.setInterfaceName(interfaceName != null ? interfaceName : "默认接口");
            supplier.setVersion(version != null ? version : "v1.0");
            supplier.setRespFields(respFields != null ? respFields : "{\"fields\":[]}");
            supplier.setOperator(operator != null ? operator : "系统");
            
            // 处理文件上传
            supplier.setReqExample(processFile(reqExample, "供应入参示例"));
            supplier.setRespExample(processFile(respExample, "供应返参示例"));
            supplier.setReqDoc(processFile(reqDoc, "供应入参文档"));
            supplier.setRespDoc(processFile(respDoc, "供应返参文档"));
            
            // 设置时间戳
            supplier.setCreateTime(LocalDateTime.now().format(DATE_FORMAT));
            supplier.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
            
            supplierInterfaces.put(supplier.getId(), supplier);
            
            return ApiResponse.success("添加供应商接口成功");
        } catch (Exception e) {
            return ApiResponse.error("添加供应商接口失败：" + e.getMessage());
        }
    }

    /**
     * 3.5 更新供应商接口
     * PUT /api/supplier-interface/{id}
     */
    @PutMapping("/{id}")
    public ApiResponse<Void> update(@PathVariable Long id, @RequestBody SupplierInterfaceUpdateRequest request) {
        try {
            SupplierInterfaceResponse supplier = supplierInterfaces.get(id);
            if (supplier == null) {
                return ApiResponse.error("供应商接口不存在");
            }

            // 更新字段
            if (request.getBusinessDomain() != null) {
                supplier.setBusinessDomain(request.getBusinessDomain());
            }
            if (request.getSupplierName() != null) {
                supplier.setSupplierName(request.getSupplierName());
            }
            if (request.getInterfaceName() != null) {
                supplier.setInterfaceName(request.getInterfaceName());
            }
            if (request.getVersion() != null) {
                supplier.setVersion(request.getVersion());
            }
            if (request.getRespFields() != null) {
                supplier.setRespFields(request.getRespFields());
            }
            if (request.getReqExample() != null) {
                supplier.setReqExample(request.getReqExample());
            }
            if (request.getRespExample() != null) {
                supplier.setRespExample(request.getRespExample());
            }
            if (request.getReqDoc() != null) {
                supplier.setReqDoc(request.getReqDoc());
            }
            if (request.getRespDoc() != null) {
                supplier.setRespDoc(request.getRespDoc());
            }
            if (request.getOperator() != null) {
                supplier.setOperator(request.getOperator());
            }

            supplier.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));

            return ApiResponse.success("更新供应商接口成功");
        } catch (Exception e) {
            return ApiResponse.error("更新供应商接口失败：" + e.getMessage());
        }
    }

    /**
     * 3.6 删除供应商接口
     * DELETE /api/supplier-interface/{id}
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> delete(@PathVariable Long id) {
        try {
            SupplierInterfaceResponse supplier = supplierInterfaces.remove(id);
            if (supplier == null) {
                return ApiResponse.error("供应商接口不存在");
            }
            return ApiResponse.success("删除供应商接口成功");
        } catch (Exception e) {
            return ApiResponse.error("删除供应商接口失败：" + e.getMessage());
        }
    }

    /**
     * 处理文件上传
     */
    private String processFile(MultipartFile file, String defaultContent) {
        if (file == null || file.isEmpty()) {
            return defaultContent;
        }
        
        try {
            return new String(file.getBytes(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            return defaultContent + "（文件读取失败：" + e.getMessage() + "）";
        }
    }

    /**
     * 供应商接口响应对象
     */
    public static class SupplierInterfaceResponse {
        private Long id;
        private String businessDomain;
        private String supplierName;
        private String interfaceName;
        private String version;
        private String respFields;
        private String reqExample;
        private String respExample;
        private String reqDoc;
        private String respDoc;
        private String operator;
        private String createTime;
        private String updateTime;

        // 构造函数
        public SupplierInterfaceResponse() {}

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getBusinessDomain() { return businessDomain; }
        public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }

        public String getSupplierName() { return supplierName; }
        public void setSupplierName(String supplierName) { this.supplierName = supplierName; }

        public String getInterfaceName() { return interfaceName; }
        public void setInterfaceName(String interfaceName) { this.interfaceName = interfaceName; }

        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }

        public String getRespFields() { return respFields; }
        public void setRespFields(String respFields) { this.respFields = respFields; }

        public String getReqExample() { return reqExample; }
        public void setReqExample(String reqExample) { this.reqExample = reqExample; }

        public String getRespExample() { return respExample; }
        public void setRespExample(String respExample) { this.respExample = respExample; }

        public String getReqDoc() { return reqDoc; }
        public void setReqDoc(String reqDoc) { this.reqDoc = reqDoc; }

        public String getRespDoc() { return respDoc; }
        public void setRespDoc(String respDoc) { this.respDoc = respDoc; }

        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }

        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }

        public String getUpdateTime() { return updateTime; }
        public void setUpdateTime(String updateTime) { this.updateTime = updateTime; }
    }

    /**
     * 供应商接口更新请求对象
     */
    public static class SupplierInterfaceUpdateRequest {
        private String businessDomain;
        private String supplierName;
        private String interfaceName;
        private String version;
        private String respFields;
        private String reqExample;
        private String respExample;
        private String reqDoc;
        private String respDoc;
        private String operator;

        // Getters and Setters
        public String getBusinessDomain() { return businessDomain; }
        public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }

        public String getSupplierName() { return supplierName; }
        public void setSupplierName(String supplierName) { this.supplierName = supplierName; }

        public String getInterfaceName() { return interfaceName; }
        public void setInterfaceName(String interfaceName) { this.interfaceName = interfaceName; }

        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }

        public String getRespFields() { return respFields; }
        public void setRespFields(String respFields) { this.respFields = respFields; }

        public String getReqExample() { return reqExample; }
        public void setReqExample(String reqExample) { this.reqExample = reqExample; }

        public String getRespExample() { return respExample; }
        public void setRespExample(String respExample) { this.respExample = respExample; }

        public String getReqDoc() { return reqDoc; }
        public void setReqDoc(String reqDoc) { this.reqDoc = reqDoc; }

        public String getRespDoc() { return respDoc; }
        public void setRespDoc(String respDoc) { this.respDoc = respDoc; }

        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }
    }

    /**
     * 供应商接口分页结果
     */
    public static class SupplierInterfacePageResult {
        private List<SupplierInterfaceResponse> records;
        private long total;
        private int pageNum;
        private int pageSize;
        private int totalPages;
        private boolean hasNext;
        private boolean hasPrevious;

        // Getters and Setters
        public List<SupplierInterfaceResponse> getRecords() { return records; }
        public void setRecords(List<SupplierInterfaceResponse> records) { this.records = records; }

        public long getTotal() { return total; }
        public void setTotal(long total) { this.total = total; }

        public int getPageNum() { return pageNum; }
        public void setPageNum(int pageNum) { this.pageNum = pageNum; }

        public int getPageSize() { return pageSize; }
        public void setPageSize(int pageSize) { this.pageSize = pageSize; }

        public int getTotalPages() { return totalPages; }
        public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

        public boolean isHasNext() { return hasNext; }
        public void setHasNext(boolean hasNext) { this.hasNext = hasNext; }

        public boolean isHasPrevious() { return hasPrevious; }
        public void setHasPrevious(boolean hasPrevious) { this.hasPrevious = hasPrevious; }
    }

    /**
     * 供应商业务域树形响应对象
     */
    public static class SupplierBusinessDomainTreeResponse {
        private Long id;
        private String businessDomain;
        private List<SupplierTreeNode> suppliers;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getBusinessDomain() { return businessDomain; }
        public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }

        public List<SupplierTreeNode> getSuppliers() { return suppliers; }
        public void setSuppliers(List<SupplierTreeNode> suppliers) { this.suppliers = suppliers; }
    }

    /**
     * 供应商树形节点
     */
    public static class SupplierTreeNode {
        private Long id;
        private String supplierName;
        private List<SupplierInterfaceTreeNode> interfaces;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getSupplierName() { return supplierName; }
        public void setSupplierName(String supplierName) { this.supplierName = supplierName; }

        public List<SupplierInterfaceTreeNode> getInterfaces() { return interfaces; }
        public void setInterfaces(List<SupplierInterfaceTreeNode> interfaces) { this.interfaces = interfaces; }
    }

    /**
     * 供应商接口树形节点
     */
    public static class SupplierInterfaceTreeNode {
        private Long id;
        private String interfaceName;
        private List<SupplierVersionTreeNode> versions;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getInterfaceName() { return interfaceName; }
        public void setInterfaceName(String interfaceName) { this.interfaceName = interfaceName; }

        public List<SupplierVersionTreeNode> getVersions() { return versions; }
        public void setVersions(List<SupplierVersionTreeNode> versions) { this.versions = versions; }
    }

    /**
     * 供应商版本树形节点
     */
    public static class SupplierVersionTreeNode {
        private Long id;
        private String version;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
    }
}