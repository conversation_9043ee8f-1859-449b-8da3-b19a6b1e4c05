package com.mappinggen.controller.api;

import com.mappinggen.dto.ApiResponse;
import com.mappinggen.dto.PageResult;
import com.mappinggen.model.MappingAssociation;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * REST API controller for Mapping Association operations
 */
@RestController
@RequestMapping("/api/mapping-association")
public class MappingAssociationApiController {

    // Mock data storage
    private static final Map<Long, MappingAssociation> associationStorage = new HashMap<>();
    private static Long nextId = 1L;

    static {
        initializeMockData();
    }

    private static void initializeMockData() {
        // Flight Search Associations
        MappingAssociation assoc1 = new MappingAssociation(
            "航班搜索", "FlightSearch", "v1.0",
            "东方航空", "FlightSearch", "v1.0"
        );
        assoc1.setId(nextId++);
        assoc1.setPlatformKnowledgeBaseId(1L);
        assoc1.setVendorKnowledgeBaseId(7L);
        assoc1.setCreateTime(LocalDateTime.now().minusDays(12));
        associationStorage.put(assoc1.getId(), assoc1);

        MappingAssociation assoc2 = new MappingAssociation(
            "航班搜索", "FlightSearch", "v1.0",
            "南方航空", "FlightSearch", "v1.0"
        );
        assoc2.setId(nextId++);
        assoc2.setPlatformKnowledgeBaseId(1L);
        assoc2.setVendorKnowledgeBaseId(8L);
        assoc2.setCreateTime(LocalDateTime.now().minusDays(11));
        associationStorage.put(assoc2.getId(), assoc2);

        MappingAssociation assoc3 = new MappingAssociation(
            "航班搜索", "FlightSearch", "v1.1",
            "国际航空", "FlightSearch", "v1.1"
        );
        assoc3.setId(nextId++);
        assoc3.setPlatformKnowledgeBaseId(2L);
        assoc3.setVendorKnowledgeBaseId(9L);
        assoc3.setCreateTime(LocalDateTime.now().minusDays(9));
        associationStorage.put(assoc3.getId(), assoc3);

        MappingAssociation assoc4 = new MappingAssociation(
            "航班搜索", "FlightSearch", "v1.0",
            "春秋航空", "FlightSearch", "v1.0"
        );
        assoc4.setId(nextId++);
        assoc4.setPlatformKnowledgeBaseId(1L);
        assoc4.setVendorKnowledgeBaseId(10L);
        assoc4.setCreateTime(LocalDateTime.now().minusDays(7));
        associationStorage.put(assoc4.getId(), assoc4);

        // Order Management Associations
        MappingAssociation assoc5 = new MappingAssociation(
            "订单管理", "OrderManagement", "v2.0",
            "东方航空", "OrderProcess", "v1.5"
        );
        assoc5.setId(nextId++);
        assoc5.setPlatformKnowledgeBaseId(3L);
        assoc5.setVendorKnowledgeBaseId(11L);
        assoc5.setCreateTime(LocalDateTime.now().minusDays(6));
        associationStorage.put(assoc5.getId(), assoc5);

        MappingAssociation assoc6 = new MappingAssociation(
            "订单管理", "OrderManagement", "v2.1",
            "南方航空", "OrderProcess", "v1.3"
        );
        assoc6.setId(nextId++);
        assoc6.setPlatformKnowledgeBaseId(4L);
        assoc6.setVendorKnowledgeBaseId(12L);
        assoc6.setCreateTime(LocalDateTime.now().minusDays(4));
        associationStorage.put(assoc6.getId(), assoc6);

        // Payment Processing Associations
        MappingAssociation assoc7 = new MappingAssociation(
            "支付处理", "PaymentProcess", "v1.0",
            "东方航空", "PaymentGateway", "v2.0"
        );
        assoc7.setId(nextId++);
        assoc7.setPlatformKnowledgeBaseId(5L);
        assoc7.setVendorKnowledgeBaseId(13L);
        assoc7.setCreateTime(LocalDateTime.now().minusDays(3));
        associationStorage.put(assoc7.getId(), assoc7);

        MappingAssociation assoc8 = new MappingAssociation(
            "支付处理", "PaymentProcess", "v1.0",
            "国际航空", "PaymentGateway", "v1.8"
        );
        assoc8.setId(nextId++);
        assoc8.setPlatformKnowledgeBaseId(5L);
        assoc8.setVendorKnowledgeBaseId(14L);
        assoc8.setCreateTime(LocalDateTime.now().minusDays(2));
        associationStorage.put(assoc8.getId(), assoc8);

        // User Management Associations
        MappingAssociation assoc9 = new MappingAssociation(
            "用户管理", "UserManagement", "v1.2",
            "春秋航空", "UserService", "v1.0"
        );
        assoc9.setId(nextId++);
        assoc9.setPlatformKnowledgeBaseId(6L);
        assoc9.setVendorKnowledgeBaseId(15L);
        assoc9.setCreateTime(LocalDateTime.now().minusDays(1));
        associationStorage.put(assoc9.getId(), assoc9);
    }

    // Dropdown endpoints removed - now using unified /api/init-data endpoint

    @GetMapping
    public ApiResponse<PageResult<MappingAssociation>> getMappingAssociations(
            @RequestParam(required = false) String businessDomain,
            @RequestParam(required = false) String platformInterface,
            @RequestParam(required = false) String platformVersion,
            @RequestParam(required = false) String vendor,
            @RequestParam(required = false) String vendorInterface,
            @RequestParam(required = false) String vendorVersion,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        List<MappingAssociation> filtered = associationStorage.values().stream()
                .filter(assoc -> businessDomain == null || businessDomain.equals(assoc.getBusinessDomain()))
                .filter(assoc -> platformInterface == null || platformInterface.equals(assoc.getPlatformInterface()))
                .filter(assoc -> platformVersion == null || platformVersion.equals(assoc.getPlatformVersion()))
                .filter(assoc -> vendor == null || vendor.equals(assoc.getVendor()))
                .filter(assoc -> vendorInterface == null || vendorInterface.equals(assoc.getVendorInterface()))
                .filter(assoc -> vendorVersion == null || vendorVersion.equals(assoc.getVendorVersion()))
                .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                .collect(Collectors.toList());

        int start = page * size;
        int end = Math.min(start + size, filtered.size());
        List<MappingAssociation> pageContent = start < filtered.size() ? filtered.subList(start, end) : new ArrayList<>();

        PageResult<MappingAssociation> pageResult = PageResult.of(pageContent, page, size, filtered.size());
        return ApiResponse.success(pageResult);
    }

    @PostMapping
    public ApiResponse<MappingAssociation> createMappingAssociation(@RequestBody MappingAssociation association) {
        association.setId(nextId++);
        association.setCreateTime(LocalDateTime.now());
        association.setUpdateTime(LocalDateTime.now());

        // Mock knowledge base IDs based on the association data
        association.setPlatformKnowledgeBaseId(generateMockKnowledgeBaseId(association.getPlatformInterface()));
        association.setVendorKnowledgeBaseId(generateMockKnowledgeBaseId(association.getVendorInterface()));

        associationStorage.put(association.getId(), association);

        return ApiResponse.success("映射关联创建成功", association);
    }

    @GetMapping("/{id}")
    public ApiResponse<MappingAssociation> getMappingAssociation(@PathVariable Long id) {
        MappingAssociation association = associationStorage.get(id);
        if (association == null) {
            return ApiResponse.error("映射关联不存在");
        }
        return ApiResponse.success(association);
    }

    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteMappingAssociation(@PathVariable Long id) {
        if (associationStorage.remove(id) != null) {
            return ApiResponse.success("删除成功");
        }
        return ApiResponse.error("映射关联不存在");
    }

    private Long generateMockKnowledgeBaseId(String interfaceName) {
        // Simple mock logic to generate knowledge base IDs
        switch (interfaceName) {
            case "FlightSearch":
                return 1L;
            case "OrderManagement":
            case "OrderProcess":
                return 2L;
            case "PaymentProcess":
            case "PaymentGateway":
                return 3L;
            case "UserManagement":
            case "UserService":
                return 4L;
            default:
                return 1L;
        }
    }
}
