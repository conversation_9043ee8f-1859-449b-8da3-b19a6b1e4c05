package com.mappinggen.controller.newApi;

import com.mappinggen.dto.ApiResponse;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 平台接口控制器
 * 提供平台接口的增删改查功能
 */
@RestController
@RequestMapping("/api/platform-interface")
public class PlatformInterfaceController {

    // 模拟数据存储
    private static final Map<Long, PlatformInterfaceResponse> platformInterfaces = new HashMap<>();
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static Long nextId = 1L;

    static {
        initializeMockData();
    }

    /**
     * 初始化模拟数据
     */
    private static void initializeMockData() {
        // 机票业务域 - 搜索接口
        PlatformInterfaceResponse flight1 = new PlatformInterfaceResponse();
        flight1.setId(nextId++);
        flight1.setBusinessDomain("机票");
        flight1.setInterfaceName("搜索接口");
        flight1.setInterfaceVersion("v1.0");
        flight1.setRespFields("{\"fields\":[{\"name\":\"flightNo\",\"type\":\"string\",\"description\":\"航班号\"},{\"name\":\"departureTime\",\"type\":\"string\",\"description\":\"出发时间\"},{\"name\":\"arrivalTime\",\"type\":\"string\",\"description\":\"到达时间\"},{\"name\":\"price\",\"type\":\"number\",\"description\":\"价格\"}]}");
        flight1.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"flights\":[{\"flightNo\":\"CA1234\",\"departureTime\":\"08:30\",\"arrivalTime\":\"10:45\",\"price\":850}]}}");
        flight1.setReqDoc("## 航班搜索API入参文档\\n\\n### 接口说明\\n- 接口名称：航班搜索\\n- 请求方式：POST\\n- 内容类型：application/json\\n\\n### 请求参数\\n| 参数名 | 类型 | 必填 | 说明 |\\n|--------|------|------|------|\\n| origin | String | 是 | 出发城市代码 |\\n| destination | String | 是 | 到达城市代码 |\\n| departureDate | String | 是 | 出发日期 |");
        flight1.setRespDoc("## 航班搜索API返参文档\\n\\n### 响应说明\\n- 响应格式：JSON\\n- 字符编码：UTF-8\\n\\n### 响应参数\\n| 参数名 | 类型 | 说明 |\\n|--------|------|------|\\n| code | String | 响应码 |\\n| message | String | 响应消息 |\\n| data | Object | 响应数据 |");
        flight1.setReqBeanPrompt("根据以下接口文档生成Java Bean类：\\n\\n```\\n接口：航班搜索\\n参数：\\n- origin: 出发城市代码（必填）\\n- destination: 到达城市代码（必填）\\n- departureDate: 出发日期（必填）\\n```\\n\\n请生成对应的请求Bean类，包含：\\n1. 字段定义\\n2. getter/setter方法\\n3. 参数校验注解");
        flight1.setRespBeanPrompt("根据以下响应结构生成Java Bean类：\\n\\n```json\\n{\\n  \\\"code\\\": \\\"200\\\",\\n  \\\"message\\\": \\\"success\\\",\\n  \\\"data\\\": {\\n    \\\"flights\\\": []\\n  }\\n}\\n```\\n\\n请生成对应的响应Bean类，包含嵌套对象处理。");
        flight1.setCodeGenPrompt("基于以上文档和Bean定义，生成完整的Controller代码，要求：\\n\\n1. 使用Spring Boot注解\\n2. 包含参数校验\\n3. 异常处理\\n4. 返回统一响应格式\\n5. 添加接口文档注解\\n\\n示例控制器方法应包含@PostMapping、@RequestBody等注解。");
        flight1.setCodeTemplateDoc("# 航班搜索代码模板\\n\\n## Controller模板\\n```java\\n@RestController\\n@RequestMapping(\\\"/api/flight\\\")\\npublic class FlightController {\\n    \\n    @PostMapping(\\\"/search\\\")\\n    public ApiResponse<FlightSearchResponse> search(@RequestBody @Valid FlightSearchRequest request) {\\n        // 业务逻辑处理\\n        return ApiResponse.success(result);\\n    }\\n}\\n```");
        flight1.setOperator("张三");
        flight1.setCreateTime(LocalDateTime.now().minusDays(15).format(DATE_FORMAT));
        flight1.setUpdateTime(LocalDateTime.now().minusDays(10).format(DATE_FORMAT));
        platformInterfaces.put(flight1.getId(), flight1);

        // 机票业务域 - 搜索接口 v1.1
        PlatformInterfaceResponse flight2 = new PlatformInterfaceResponse();
        flight2.setId(nextId++);
        flight2.setBusinessDomain("机票");
        flight2.setInterfaceName("搜索接口");
        flight2.setInterfaceVersion("v1.1");
        flight2.setRespFields("{\"fields\":[{\"name\":\"flightNo\",\"type\":\"string\"},{\"name\":\"departureTime\",\"type\":\"string\"},{\"name\":\"arrivalTime\",\"type\":\"string\"},{\"name\":\"price\",\"type\":\"number\"},{\"name\":\"aircraft\",\"type\":\"string\"},{\"name\":\"availableSeats\",\"type\":\"number\"}]}");
        flight2.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"totalResults\":25,\"flights\":[{\"flightNo\":\"CA1234\",\"departureTime\":\"08:30\",\"arrivalTime\":\"10:45\",\"price\":850,\"aircraft\":\"A320\",\"availableSeats\":45}]}}");
        flight2.setReqDoc("## 航班搜索API v1.1入参文档\\n\\n### 新增功能\\n- 多程航班支持\\n- 高级筛选功能\\n\\n### 请求参数\\n| 参数名 | 类型 | 必填 | 说明 |\\n|--------|------|------|------|\\n| tripType | String | 否 | 行程类型 |\\n| filters | Object | 否 | 筛选条件 |");
        flight2.setRespDoc("## 航班搜索API v1.1返参文档\\n\\n### 新增字段\\n| 参数名 | 类型 | 说明 |\\n|--------|------|------|\\n| totalResults | Integer | 总结果数 |\\n| filters | Object | 可用筛选选项 |");
        flight2.setReqBeanPrompt("航班搜索v1.1请求Bean生成提示");
        flight2.setRespBeanPrompt("航班搜索v1.1响应Bean生成提示");
        flight2.setCodeGenPrompt("航班搜索v1.1代码生成提示");
        flight2.setCodeTemplateDoc("航班搜索v1.1代码模板");
        flight2.setOperator("李四");
        flight2.setCreateTime(LocalDateTime.now().minusDays(12).format(DATE_FORMAT));
        flight2.setUpdateTime(LocalDateTime.now().minusDays(8).format(DATE_FORMAT));
        platformInterfaces.put(flight2.getId(), flight2);

        // 机票业务域 - 预订接口
        PlatformInterfaceResponse booking1 = new PlatformInterfaceResponse();
        booking1.setId(nextId++);
        booking1.setBusinessDomain("机票");
        booking1.setInterfaceName("预订接口");
        booking1.setInterfaceVersion("v1.0");
        booking1.setRespFields("{\"fields\":[{\"name\":\"orderId\",\"type\":\"string\"},{\"name\":\"status\",\"type\":\"string\"},{\"name\":\"totalAmount\",\"type\":\"number\"}]}");
        booking1.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"orderId\":\"ORD001\",\"status\":\"CONFIRMED\",\"totalAmount\":850.00}}");
        booking1.setReqDoc("## 机票预订API入参文档\\n\\n### 主要功能\\n1. 创建预订\\n2. 确认预订\\n3. 取消预订");
        booking1.setRespDoc("## 机票预订API返参文档\\n\\n### 预订状态\\n- PENDING: 待确认\\n- CONFIRMED: 已确认\\n- CANCELLED: 已取消");
        booking1.setReqBeanPrompt("机票预订请求Bean生成提示");
        booking1.setRespBeanPrompt("机票预订响应Bean生成提示");
        booking1.setCodeGenPrompt("机票预订代码生成提示");
        booking1.setCodeTemplateDoc("机票预订代码模板");
        booking1.setOperator("王五");
        booking1.setCreateTime(LocalDateTime.now().minusDays(10).format(DATE_FORMAT));
        booking1.setUpdateTime(LocalDateTime.now().minusDays(6).format(DATE_FORMAT));
        platformInterfaces.put(booking1.getId(), booking1);

        // 酒店业务域 - 搜索接口
        PlatformInterfaceResponse hotel1 = new PlatformInterfaceResponse();
        hotel1.setId(nextId++);
        hotel1.setBusinessDomain("酒店");
        hotel1.setInterfaceName("搜索接口");
        hotel1.setInterfaceVersion("v1.0");
        hotel1.setRespFields("{\"fields\":[{\"name\":\"hotelId\",\"type\":\"string\"},{\"name\":\"hotelName\",\"type\":\"string\"},{\"name\":\"price\",\"type\":\"number\"},{\"name\":\"rating\",\"type\":\"number\"}]}");
        hotel1.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"hotels\":[{\"hotelId\":\"H001\",\"hotelName\":\"五星酒店\",\"price\":580,\"rating\":4.8}]}}");
        hotel1.setReqDoc("## 酒店搜索API入参文档");
        hotel1.setRespDoc("## 酒店搜索API返参文档");
        hotel1.setReqBeanPrompt("酒店搜索请求Bean生成提示");
        hotel1.setRespBeanPrompt("酒店搜索响应Bean生成提示");
        hotel1.setCodeGenPrompt("酒店搜索代码生成提示");
        hotel1.setCodeTemplateDoc("酒店搜索代码模板");
        hotel1.setOperator("赵六");
        hotel1.setCreateTime(LocalDateTime.now().minusDays(8).format(DATE_FORMAT));
        hotel1.setUpdateTime(LocalDateTime.now().minusDays(5).format(DATE_FORMAT));
        platformInterfaces.put(hotel1.getId(), hotel1);

        // 用户管理业务域
        PlatformInterfaceResponse user1 = new PlatformInterfaceResponse();
        user1.setId(nextId++);
        user1.setBusinessDomain("用户管理");
        user1.setInterfaceName("用户查询");
        user1.setInterfaceVersion("v1.2");
        user1.setRespFields("{\"fields\":[{\"name\":\"userId\",\"type\":\"string\"},{\"name\":\"username\",\"type\":\"string\"},{\"name\":\"status\",\"type\":\"string\"}]}");
        user1.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"userId\":\"U001\",\"username\":\"user123\",\"status\":\"ACTIVE\"}}");
        user1.setReqDoc("## 用户管理API入参文档\\n\\n### 主要功能\\n- 用户注册\\n- 用户登录\\n- 信息管理");
        user1.setRespDoc("## 用户管理API返参文档\\n\\n### 用户状态\\n- ACTIVE: 活跃\\n- INACTIVE: 非活跃\\n- BLOCKED: 已封禁");
        user1.setReqBeanPrompt("用户管理请求Bean生成提示");
        user1.setRespBeanPrompt("用户管理响应Bean生成提示");
        user1.setCodeGenPrompt("用户管理代码生成提示");
        user1.setCodeTemplateDoc("用户管理代码模板");
        user1.setOperator("孙七");
        user1.setCreateTime(LocalDateTime.now().minusDays(5).format(DATE_FORMAT));
        user1.setUpdateTime(LocalDateTime.now().minusDays(2).format(DATE_FORMAT));
        platformInterfaces.put(user1.getId(), user1);

        // 火车票业务域 - 搜索接口
        PlatformInterfaceResponse train1 = new PlatformInterfaceResponse();
        train1.setId(nextId++);
        train1.setBusinessDomain("火车票");
        train1.setInterfaceName("搜索接口");
        train1.setInterfaceVersion("v1.0");
        train1.setRespFields("{\"fields\":[{\"name\":\"trainNo\",\"type\":\"string\",\"description\":\"车次\"},{\"name\":\"departureTime\",\"type\":\"string\",\"description\":\"出发时间\"},{\"name\":\"arrivalTime\",\"type\":\"string\",\"description\":\"到达时间\"},{\"name\":\"price\",\"type\":\"number\",\"description\":\"票价\"}]}");
        train1.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"trains\":[{\"trainNo\":\"G123\",\"departureTime\":\"08:00\",\"arrivalTime\":\"12:30\",\"price\":553}]}}");
        train1.setReqDoc("## 火车票搜索API入参文档\\n\\n### 接口说明\\n- 接口名称：火车票搜索\\n- 请求方式：POST\\n\\n### 请求参数\\n| 参数名 | 类型 | 必填 | 说明 |\\n|--------|------|------|------|\\n| fromStation | String | 是 | 出发站 |\\n| toStation | String | 是 | 到达站 |\\n| trainDate | String | 是 | 乘车日期 |");
        train1.setRespDoc("## 火车票搜索API返参文档\\n\\n### 响应说明\\n火车票查询结果，包含车次、时间、价格等信息");
        train1.setReqBeanPrompt("火车票搜索请求Bean生成提示");
        train1.setRespBeanPrompt("火车票搜索响应Bean生成提示");
        train1.setCodeGenPrompt("火车票搜索代码生成提示");
        train1.setCodeTemplateDoc("火车票搜索代码模板");
        train1.setOperator("赵六");
        train1.setCreateTime(LocalDateTime.now().minusDays(8).format(DATE_FORMAT));
        train1.setUpdateTime(LocalDateTime.now().minusDays(6).format(DATE_FORMAT));
        platformInterfaces.put(train1.getId(), train1);

        // 火车票业务域 - 预订接口
        PlatformInterfaceResponse trainBooking = new PlatformInterfaceResponse();
        trainBooking.setId(nextId++);
        trainBooking.setBusinessDomain("火车票");
        trainBooking.setInterfaceName("预订接口");
        trainBooking.setInterfaceVersion("v1.0");
        trainBooking.setRespFields("{\"fields\":[{\"name\":\"orderId\",\"type\":\"string\"},{\"name\":\"ticketNo\",\"type\":\"string\"},{\"name\":\"seatInfo\",\"type\":\"string\"}]}");
        trainBooking.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"orderId\":\"TRN001\",\"ticketNo\":\"T20240130001\",\"seatInfo\":\"02车06D\"}}");
        trainBooking.setReqDoc("## 火车票预订API入参文档\\n\\n### 预订流程\\n1. 选择车次\\n2. 填写乘客信息\\n3. 确认预订");
        trainBooking.setRespDoc("## 火车票预订API返参文档\\n\\n### 预订结果\\n包含订单号、票号、座位信息等");
        trainBooking.setReqBeanPrompt("火车票预订请求Bean生成提示");
        trainBooking.setRespBeanPrompt("火车票预订响应Bean生成提示");
        trainBooking.setCodeGenPrompt("火车票预订代码生成提示");
        trainBooking.setCodeTemplateDoc("火车票预订代码模板");
        trainBooking.setOperator("钱八");
        trainBooking.setCreateTime(LocalDateTime.now().minusDays(7).format(DATE_FORMAT));
        trainBooking.setUpdateTime(LocalDateTime.now().minusDays(5).format(DATE_FORMAT));
        platformInterfaces.put(trainBooking.getId(), trainBooking);

        // 酒店业务域 - 预订接口
        PlatformInterfaceResponse hotelBooking = new PlatformInterfaceResponse();
        hotelBooking.setId(nextId++);
        hotelBooking.setBusinessDomain("酒店");
        hotelBooking.setInterfaceName("预订接口");
        hotelBooking.setInterfaceVersion("v1.0");
        hotelBooking.setRespFields("{\"fields\":[{\"name\":\"bookingId\",\"type\":\"string\"},{\"name\":\"confirmationNo\",\"type\":\"string\"},{\"name\":\"status\",\"type\":\"string\"}]}");
        hotelBooking.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"bookingId\":\"HTL001\",\"confirmationNo\":\"CF123456\",\"status\":\"CONFIRMED\"}}");
        hotelBooking.setReqDoc("## 酒店预订API入参文档\\n\\n### 预订信息\\n- 酒店ID\\n- 入住日期\\n- 离店日期\\n- 房间数量");
        hotelBooking.setRespDoc("## 酒店预订API返参文档\\n\\n### 预订状态\\n- PENDING: 待确认\\n- CONFIRMED: 已确认");
        hotelBooking.setReqBeanPrompt("酒店预订请求Bean生成提示");
        hotelBooking.setRespBeanPrompt("酒店预订响应Bean生成提示");
        hotelBooking.setCodeGenPrompt("酒店预订代码生成提示");
        hotelBooking.setCodeTemplateDoc("酒店预订代码模板");
        hotelBooking.setOperator("周九");
        hotelBooking.setCreateTime(LocalDateTime.now().minusDays(6).format(DATE_FORMAT));
        hotelBooking.setUpdateTime(LocalDateTime.now().minusDays(4).format(DATE_FORMAT));
        platformInterfaces.put(hotelBooking.getId(), hotelBooking);

        // 支付业务域 - 支付接口
        PlatformInterfaceResponse payment1 = new PlatformInterfaceResponse();
        payment1.setId(nextId++);
        payment1.setBusinessDomain("支付");
        payment1.setInterfaceName("支付接口");
        payment1.setInterfaceVersion("v1.0");
        payment1.setRespFields("{\"fields\":[{\"name\":\"paymentId\",\"type\":\"string\"},{\"name\":\"status\",\"type\":\"string\"},{\"name\":\"amount\",\"type\":\"number\"}]}");
        payment1.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"paymentId\":\"PAY001\",\"status\":\"SUCCESS\",\"amount\":1200.50}}");
        payment1.setReqDoc("## 支付API入参文档\\n\\n### 支付参数\\n- 订单号\\n- 支付金额\\n- 支付方式");
        payment1.setRespDoc("## 支付API返参文档\\n\\n### 支付状态\\n- SUCCESS: 成功\\n- FAILED: 失败\\n- PENDING: 处理中");
        payment1.setReqBeanPrompt("支付请求Bean生成提示");
        payment1.setRespBeanPrompt("支付响应Bean生成提示");
        payment1.setCodeGenPrompt("支付代码生成提示");
        payment1.setCodeTemplateDoc("支付代码模板");
        payment1.setOperator("吴十");
        payment1.setCreateTime(LocalDateTime.now().minusDays(4).format(DATE_FORMAT));
        payment1.setUpdateTime(LocalDateTime.now().minusDays(3).format(DATE_FORMAT));
        platformInterfaces.put(payment1.getId(), payment1);

        // 支付业务域 - 退款接口
        PlatformInterfaceResponse refund1 = new PlatformInterfaceResponse();
        refund1.setId(nextId++);
        refund1.setBusinessDomain("支付");
        refund1.setInterfaceName("退款接口");
        refund1.setInterfaceVersion("v1.0");
        refund1.setRespFields("{\"fields\":[{\"name\":\"refundId\",\"type\":\"string\"},{\"name\":\"status\",\"type\":\"string\"},{\"name\":\"refundAmount\",\"type\":\"number\"}]}");
        refund1.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"refundId\":\"REF001\",\"status\":\"SUCCESS\",\"refundAmount\":800.00}}");
        refund1.setReqDoc("## 退款API入参文档\\n\\n### 退款参数\\n- 原支付订单号\\n- 退款金额\\n- 退款原因");
        refund1.setRespDoc("## 退款API返参文档\\n\\n### 退款状态说明\\n包含退款ID、状态和金额信息");
        refund1.setReqBeanPrompt("退款请求Bean生成提示");
        refund1.setRespBeanPrompt("退款响应Bean生成提示");
        refund1.setCodeGenPrompt("退款代码生成提示");
        refund1.setCodeTemplateDoc("退款代码模板");
        refund1.setOperator("郑十一");
        refund1.setCreateTime(LocalDateTime.now().minusDays(3).format(DATE_FORMAT));
        refund1.setUpdateTime(LocalDateTime.now().minusDays(1).format(DATE_FORMAT));
        platformInterfaces.put(refund1.getId(), refund1);

        // 机票业务域 - 取消接口
        PlatformInterfaceResponse flightCancel = new PlatformInterfaceResponse();
        flightCancel.setId(nextId++);
        flightCancel.setBusinessDomain("机票");
        flightCancel.setInterfaceName("取消接口");
        flightCancel.setInterfaceVersion("v1.0");
        flightCancel.setRespFields("{\"fields\":[{\"name\":\"cancelId\",\"type\":\"string\"},{\"name\":\"status\",\"type\":\"string\"},{\"name\":\"refundAmount\",\"type\":\"number\"}]}");
        flightCancel.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"cancelId\":\"CAN001\",\"status\":\"CANCELLED\",\"refundAmount\":680.00}}");
        flightCancel.setReqDoc("## 机票取消API入参文档\\n\\n### 取消规则\\n- 起飞前2小时可免费取消\\n- 其他情况收取手续费");
        flightCancel.setRespDoc("## 机票取消API返参文档\\n\\n### 取消结果\\n包含取消ID、状态和退款金额");
        flightCancel.setReqBeanPrompt("机票取消请求Bean生成提示");
        flightCancel.setRespBeanPrompt("机票取消响应Bean生成提示");
        flightCancel.setCodeGenPrompt("机票取消代码生成提示");
        flightCancel.setCodeTemplateDoc("机票取消代码模板");
        flightCancel.setOperator("王十二");
        flightCancel.setCreateTime(LocalDateTime.now().minusDays(2).format(DATE_FORMAT));
        flightCancel.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        platformInterfaces.put(flightCancel.getId(), flightCancel);

        // 用户管理业务域 - 权限接口
        PlatformInterfaceResponse userAuth = new PlatformInterfaceResponse();
        userAuth.setId(nextId++);
        userAuth.setBusinessDomain("用户管理");
        userAuth.setInterfaceName("权限接口");
        userAuth.setInterfaceVersion("v1.0");
        userAuth.setRespFields("{\"fields\":[{\"name\":\"permissions\",\"type\":\"array\"},{\"name\":\"roles\",\"type\":\"array\"},{\"name\":\"accessLevel\",\"type\":\"string\"}]}");
        userAuth.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"permissions\":[\"READ\",\"WRITE\"],\"roles\":[\"USER\",\"ADMIN\"],\"accessLevel\":\"FULL\"}}");
        userAuth.setReqDoc("## 用户权限API入参文档\\n\\n### 权限查询\\n- 用户ID\\n- 资源类型\\n- 操作类型");
        userAuth.setRespDoc("## 用户权限API返参文档\\n\\n### 权限信息\\n包含用户的权限列表、角色和访问级别");
        userAuth.setReqBeanPrompt("用户权限请求Bean生成提示");
        userAuth.setRespBeanPrompt("用户权限响应Bean生成提示");
        userAuth.setCodeGenPrompt("用户权限代码生成提示");
        userAuth.setCodeTemplateDoc("用户权限代码模板");
        userAuth.setOperator("陈十三");
        userAuth.setCreateTime(LocalDateTime.now().minusDays(1).format(DATE_FORMAT));
        userAuth.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        platformInterfaces.put(userAuth.getId(), userAuth);

        // 酒店业务域 - 评价接口
        PlatformInterfaceResponse hotelReview = new PlatformInterfaceResponse();
        hotelReview.setId(nextId++);
        hotelReview.setBusinessDomain("酒店");
        hotelReview.setInterfaceName("评价接口");
        hotelReview.setInterfaceVersion("v1.0");
        hotelReview.setRespFields("{\"fields\":[{\"name\":\"reviewId\",\"type\":\"string\"},{\"name\":\"rating\",\"type\":\"number\"},{\"name\":\"comment\",\"type\":\"string\"}]}");
        hotelReview.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"reviewId\":\"REV001\",\"rating\":4.5,\"comment\":\"服务很好，环境优美\"}}");
        hotelReview.setReqDoc("## 酒店评价API入参文档\\n\\n### 评价内容\\n- 订单ID\\n- 评分（1-5分）\\n- 评价内容");
        hotelReview.setRespDoc("## 酒店评价API返参文档\\n\\n### 评价结果\\n返回评价ID和评价详情");
        hotelReview.setReqBeanPrompt("酒店评价请求Bean生成提示");
        hotelReview.setRespBeanPrompt("酒店评价响应Bean生成提示");
        hotelReview.setCodeGenPrompt("酒店评价代码生成提示");
        hotelReview.setCodeTemplateDoc("酒店评价代码模板");
        hotelReview.setOperator("林十四");
        hotelReview.setCreateTime(LocalDateTime.now().format(DATE_FORMAT));
        hotelReview.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        platformInterfaces.put(hotelReview.getId(), hotelReview);

        // 机票业务域 - 改签接口  
        PlatformInterfaceResponse flightChange = new PlatformInterfaceResponse();
        flightChange.setId(nextId++);
        flightChange.setBusinessDomain("机票");
        flightChange.setInterfaceName("改签接口");
        flightChange.setInterfaceVersion("v1.0");
        flightChange.setRespFields("{\"fields\":[{\"name\":\"changeId\",\"type\":\"string\"},{\"name\":\"newFlightNo\",\"type\":\"string\"},{\"name\":\"changeFee\",\"type\":\"number\"}]}");
        flightChange.setRespExample("{\"code\":\"200\",\"message\":\"success\",\"data\":{\"changeId\":\"CHG001\",\"newFlightNo\":\"CA5678\",\"changeFee\":150.00}}");
        flightChange.setReqDoc("## 机票改签API入参文档\\n\\n### 改签规则\\n- 原订单号\\n- 新航班信息\\n- 改签原因");
        flightChange.setRespDoc("## 机票改签API返参文档\\n\\n### 改签结果\\n包含改签ID、新航班号和改签费用");
        flightChange.setReqBeanPrompt("机票改签请求Bean生成提示");
        flightChange.setRespBeanPrompt("机票改签响应Bean生成提示");
        flightChange.setCodeGenPrompt("机票改签代码生成提示");
        flightChange.setCodeTemplateDoc("机票改签代码模板");
        flightChange.setOperator("黄十五");
        flightChange.setCreateTime(LocalDateTime.now().format(DATE_FORMAT));
        flightChange.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
        platformInterfaces.put(flightChange.getId(), flightChange);
    }

    /**
     * 2.1 查询平台接口树形结构
     * GET /api/platform-interface/tree
     */
    @GetMapping("/tree")
    public ApiResponse<List<BusinessDomainTreeResponse>> getTree() {
        try {
            // 按业务域分组
            Map<String, BusinessDomainTreeResponse> domainMap = new LinkedHashMap<>();
            
            for (PlatformInterfaceResponse platform : platformInterfaces.values()) {
                String domain = platform.getBusinessDomain();
                
                BusinessDomainTreeResponse domainNode = domainMap.computeIfAbsent(domain, k -> {
                    BusinessDomainTreeResponse node = new BusinessDomainTreeResponse();
                    node.setId(null);
                    node.setBusinessDomain(k);
                    node.setInterfaces(new ArrayList<>());
                    return node;
                });
                
                // 查找或创建接口节点
                InterfaceTreeNode interfaceNode = domainNode.getInterfaces().stream()
                    .filter(i -> i.getInterfaceName().equals(platform.getInterfaceName()))
                    .findFirst()
                    .orElse(null);
                
                if (interfaceNode == null) {
                    interfaceNode = new InterfaceTreeNode();
                    interfaceNode.setId(null);
                    interfaceNode.setInterfaceName(platform.getInterfaceName());
                    interfaceNode.setVersions(new ArrayList<>());
                    domainNode.getInterfaces().add(interfaceNode);
                }
                
                // 添加版本节点
                VersionTreeNode versionNode = new VersionTreeNode();
                versionNode.setId(platform.getId());
                versionNode.setVersion(platform.getInterfaceVersion());
                interfaceNode.getVersions().add(versionNode);
            }
            
            return ApiResponse.success(new ArrayList<>(domainMap.values()));
        } catch (Exception e) {
            return ApiResponse.error("获取树形结构失败：" + e.getMessage());
        }
    }

    /**
     * 2.2 分页查询平台接口列表
     * GET /api/platform-interface/list
     */
    @GetMapping("/list")
    public ApiResponse<PlatformInterfacePageResult> getList(
            @RequestParam(required = false) String businessDomain,
            @RequestParam(required = false) String interfaceName,
            @RequestParam(required = false) String version,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        
        try {
            // 过滤数据
            List<PlatformInterfaceResponse> filtered = platformInterfaces.values().stream()
                    .filter(p -> businessDomain == null || businessDomain.equals(p.getBusinessDomain()))
                    .filter(p -> interfaceName == null || interfaceName.equals(p.getInterfaceName()))
                    .filter(p -> version == null || version.equals(p.getInterfaceVersion()))
                    .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                    .toList();

            // 分页计算
            int total = filtered.size();
            int totalPages = (int) Math.ceil((double) total / pageSize);
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, total);
            
            List<PlatformInterfaceResponse> records = start < total ? 
                filtered.subList(start, end) : new ArrayList<>();

            // 构建分页结果
            PlatformInterfacePageResult pageResult = new PlatformInterfacePageResult();
            pageResult.setRecords(records);
            pageResult.setTotal(total);
            pageResult.setPageNum(pageNum);
            pageResult.setPageSize(pageSize);
            pageResult.setTotalPages(totalPages);
            pageResult.setHasNext(pageNum < totalPages);
            pageResult.setHasPrevious(pageNum > 1);

            return ApiResponse.success(pageResult);
        } catch (Exception e) {
            return ApiResponse.error("查询平台接口列表失败：" + e.getMessage());
        }
    }

    /**
     * 2.3 根据ID查询平台接口
     * GET /api/platform-interface/{id}
     */
    @GetMapping("/{id}")
    public ApiResponse<PlatformInterfaceResponse> getById(@PathVariable Long id) {
        try {
            PlatformInterfaceResponse platform = platformInterfaces.get(id);
            if (platform == null) {
                return ApiResponse.error("平台接口不存在");
            }
            return ApiResponse.success(platform);
        } catch (Exception e) {
            return ApiResponse.error("查询平台接口失败：" + e.getMessage());
        }
    }

    /**
     * 2.4 添加平台接口
     * POST /api/platform-interface
     */
    @PostMapping
    public ApiResponse<Void> create(
            @RequestParam(required = false) String businessDomain,
            @RequestParam(required = false) String interfaceName,
            @RequestParam(required = false) String interfaceVersion,
            @RequestParam(required = false) String operator,
            @RequestParam(required = false) MultipartFile respExample,
            @RequestParam(required = false) MultipartFile reqDoc,
            @RequestParam(required = false) MultipartFile respDoc,
            @RequestParam(required = false) MultipartFile reqBeanPrompt,
            @RequestParam(required = false) MultipartFile respBeanPrompt,
            @RequestParam(required = false) MultipartFile codeGenPrompt,
            @RequestParam(required = false) MultipartFile codeTemplateDoc) {
        
        try {
            PlatformInterfaceResponse platform = new PlatformInterfaceResponse();
            platform.setId(nextId++);
            platform.setBusinessDomain(businessDomain != null ? businessDomain : "默认业务域");
            platform.setInterfaceName(interfaceName != null ? interfaceName : "默认接口");
            platform.setInterfaceVersion(interfaceVersion != null ? interfaceVersion : "v1.0");
            platform.setOperator(operator != null ? operator : "系统");
            
            // 处理文件上传
            platform.setRespExample(processFile(respExample, "平台返参示例"));
            platform.setReqDoc(processFile(reqDoc, "平台入参文档"));
            platform.setRespDoc(processFile(respDoc, "平台返参文档"));
            platform.setReqBeanPrompt(processFile(reqBeanPrompt, "入参Bean生成Prompt"));
            platform.setRespBeanPrompt(processFile(respBeanPrompt, "返参Bean生成Prompt"));
            platform.setCodeGenPrompt(processFile(codeGenPrompt, "代码生成Prompt"));
            platform.setCodeTemplateDoc(processFile(codeTemplateDoc, "代码模板文档"));
            
            // 设置默认值
            platform.setRespFields("{\"fields\":[]}");
            platform.setCreateTime(LocalDateTime.now().format(DATE_FORMAT));
            platform.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
            
            platformInterfaces.put(platform.getId(), platform);
            
            return ApiResponse.success("添加平台接口成功");
        } catch (Exception e) {
            return ApiResponse.error("添加平台接口失败：" + e.getMessage());
        }
    }

    /**
     * 2.5 更新平台接口
     * PUT /api/platform-interface/{id}
     */
    @PutMapping("/{id}")
    public ApiResponse<Void> update(@PathVariable Long id, @RequestBody PlatformInterfaceUpdateRequest request) {
        try {
            PlatformInterfaceResponse platform = platformInterfaces.get(id);
            if (platform == null) {
                return ApiResponse.error("平台接口不存在");
            }
            
            // 更新字段
            if (request.getBusinessDomain() != null) {
                platform.setBusinessDomain(request.getBusinessDomain());
            }
            if (request.getInterfaceName() != null) {
                platform.setInterfaceName(request.getInterfaceName());
            }
            if (request.getInterfaceVersion() != null) {
                platform.setInterfaceVersion(request.getInterfaceVersion());
            }
            if (request.getRespFields() != null) {
                platform.setRespFields(request.getRespFields());
            }
            if (request.getRespExample() != null) {
                platform.setRespExample(request.getRespExample());
            }
            if (request.getReqDoc() != null) {
                platform.setReqDoc(request.getReqDoc());
            }
            if (request.getRespDoc() != null) {
                platform.setRespDoc(request.getRespDoc());
            }
            if (request.getReqBeanPrompt() != null) {
                platform.setReqBeanPrompt(request.getReqBeanPrompt());
            }
            if (request.getRespBeanPrompt() != null) {
                platform.setRespBeanPrompt(request.getRespBeanPrompt());
            }
            if (request.getCodeGenPrompt() != null) {
                platform.setCodeGenPrompt(request.getCodeGenPrompt());
            }
            if (request.getCodeTemplateDoc() != null) {
                platform.setCodeTemplateDoc(request.getCodeTemplateDoc());
            }
            if (request.getOperator() != null) {
                platform.setOperator(request.getOperator());
            }
            
            platform.setUpdateTime(LocalDateTime.now().format(DATE_FORMAT));
            
            return ApiResponse.success("更新平台接口成功");
        } catch (Exception e) {
            return ApiResponse.error("更新平台接口失败：" + e.getMessage());
        }
    }

    /**
     * 2.6 删除平台接口
     * DELETE /api/platform-interface/{id}
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> delete(@PathVariable Long id) {
        try {
            PlatformInterfaceResponse platform = platformInterfaces.remove(id);
            if (platform == null) {
                return ApiResponse.error("平台接口不存在");
            }
            return ApiResponse.success("删除平台接口成功");
        } catch (Exception e) {
            return ApiResponse.error("删除平台接口失败：" + e.getMessage());
        }
    }

    /**
     * 处理文件上传
     */
    private String processFile(MultipartFile file, String defaultContent) {
        if (file != null && !file.isEmpty()) {
            try {
                return new String(file.getBytes(), StandardCharsets.UTF_8);
            } catch (Exception e) {
                return defaultContent + "（文件处理失败）";
            }
        }
        return defaultContent;
    }

    // ===== 内部DTO类定义 =====

    /**
     * 平台接口响应对象
     */
    public static class PlatformInterfaceResponse {
        private Long id;
        private String businessDomain;
        private String interfaceName;
        private String interfaceVersion;
        private String respFields;
        private String respExample;
        private String reqDoc;
        private String respDoc;
        private String reqBeanPrompt;
        private String respBeanPrompt;
        private String codeGenPrompt;
        private String codeTemplateDoc;
        private String operator;
        private String createTime;
        private String updateTime;

        // 构造函数
        public PlatformInterfaceResponse() {}

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getBusinessDomain() { return businessDomain; }
        public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }

        public String getInterfaceName() { return interfaceName; }
        public void setInterfaceName(String interfaceName) { this.interfaceName = interfaceName; }

        public String getInterfaceVersion() { return interfaceVersion; }
        public void setInterfaceVersion(String interfaceVersion) { this.interfaceVersion = interfaceVersion; }

        public String getRespFields() { return respFields; }
        public void setRespFields(String respFields) { this.respFields = respFields; }

        public String getRespExample() { return respExample; }
        public void setRespExample(String respExample) { this.respExample = respExample; }

        public String getReqDoc() { return reqDoc; }
        public void setReqDoc(String reqDoc) { this.reqDoc = reqDoc; }

        public String getRespDoc() { return respDoc; }
        public void setRespDoc(String respDoc) { this.respDoc = respDoc; }

        public String getReqBeanPrompt() { return reqBeanPrompt; }
        public void setReqBeanPrompt(String reqBeanPrompt) { this.reqBeanPrompt = reqBeanPrompt; }

        public String getRespBeanPrompt() { return respBeanPrompt; }
        public void setRespBeanPrompt(String respBeanPrompt) { this.respBeanPrompt = respBeanPrompt; }

        public String getCodeGenPrompt() { return codeGenPrompt; }
        public void setCodeGenPrompt(String codeGenPrompt) { this.codeGenPrompt = codeGenPrompt; }

        public String getCodeTemplateDoc() { return codeTemplateDoc; }
        public void setCodeTemplateDoc(String codeTemplateDoc) { this.codeTemplateDoc = codeTemplateDoc; }

        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }

        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }

        public String getUpdateTime() { return updateTime; }
        public void setUpdateTime(String updateTime) { this.updateTime = updateTime; }
    }

    /**
     * 平台接口更新请求对象
     */
    public static class PlatformInterfaceUpdateRequest {
        private String businessDomain;
        private String interfaceName;
        private String interfaceVersion;
        private String respFields;
        private String respExample;
        private String reqDoc;
        private String respDoc;
        private String reqBeanPrompt;
        private String respBeanPrompt;
        private String codeGenPrompt;
        private String codeTemplateDoc;
        private String operator;

        // Getters and Setters
        public String getBusinessDomain() { return businessDomain; }
        public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }

        public String getInterfaceName() { return interfaceName; }
        public void setInterfaceName(String interfaceName) { this.interfaceName = interfaceName; }

        public String getInterfaceVersion() { return interfaceVersion; }
        public void setInterfaceVersion(String interfaceVersion) { this.interfaceVersion = interfaceVersion; }

        public String getRespFields() { return respFields; }
        public void setRespFields(String respFields) { this.respFields = respFields; }

        public String getRespExample() { return respExample; }
        public void setRespExample(String respExample) { this.respExample = respExample; }

        public String getReqDoc() { return reqDoc; }
        public void setReqDoc(String reqDoc) { this.reqDoc = reqDoc; }

        public String getRespDoc() { return respDoc; }
        public void setRespDoc(String respDoc) { this.respDoc = respDoc; }

        public String getReqBeanPrompt() { return reqBeanPrompt; }
        public void setReqBeanPrompt(String reqBeanPrompt) { this.reqBeanPrompt = reqBeanPrompt; }

        public String getRespBeanPrompt() { return respBeanPrompt; }
        public void setRespBeanPrompt(String respBeanPrompt) { this.respBeanPrompt = respBeanPrompt; }

        public String getCodeGenPrompt() { return codeGenPrompt; }
        public void setCodeGenPrompt(String codeGenPrompt) { this.codeGenPrompt = codeGenPrompt; }

        public String getCodeTemplateDoc() { return codeTemplateDoc; }
        public void setCodeTemplateDoc(String codeTemplateDoc) { this.codeTemplateDoc = codeTemplateDoc; }

        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }
    }

    /**
     * 业务域树形响应对象
     */
    public static class BusinessDomainTreeResponse {
        private Long id;
        private String businessDomain;
        private List<InterfaceTreeNode> interfaces;

        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getBusinessDomain() { return businessDomain; }
        public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }

        public List<InterfaceTreeNode> getInterfaces() { return interfaces; }
        public void setInterfaces(List<InterfaceTreeNode> interfaces) { this.interfaces = interfaces; }
    }

    /**
     * 接口树形节点
     */
    public static class InterfaceTreeNode {
        private Long id;
        private String interfaceName;
        private List<VersionTreeNode> versions;

        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getInterfaceName() { return interfaceName; }
        public void setInterfaceName(String interfaceName) { this.interfaceName = interfaceName; }

        public List<VersionTreeNode> getVersions() { return versions; }
        public void setVersions(List<VersionTreeNode> versions) { this.versions = versions; }
    }

    /**
     * 版本树形节点
     */
    public static class VersionTreeNode {
        private Long id;
        private String version;

        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
    }

    /**
     * 平台接口分页结果
     */
    public static class PlatformInterfacePageResult {
        private List<PlatformInterfaceResponse> records;
        private long total;
        private int pageNum;
        private int pageSize;
        private int totalPages;
        private boolean hasNext;
        private boolean hasPrevious;

        public List<PlatformInterfaceResponse> getRecords() { return records; }
        public void setRecords(List<PlatformInterfaceResponse> records) { this.records = records; }

        public long getTotal() { return total; }
        public void setTotal(long total) { this.total = total; }

        public int getPageNum() { return pageNum; }
        public void setPageNum(int pageNum) { this.pageNum = pageNum; }

        public int getPageSize() { return pageSize; }
        public void setPageSize(int pageSize) { this.pageSize = pageSize; }

        public int getTotalPages() { return totalPages; }
        public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

        public boolean isHasNext() { return hasNext; }
        public void setHasNext(boolean hasNext) { this.hasNext = hasNext; }

        public boolean isHasPrevious() { return hasPrevious; }
        public void setHasPrevious(boolean hasPrevious) { this.hasPrevious = hasPrevious; }
    }
} 