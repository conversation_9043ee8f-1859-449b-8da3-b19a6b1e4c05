package com.mappinggen.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Code Prompt entity for managing code prompts
 */
public class CodePrompt {
    
    private Long id;
    private Long fieldMappingId;
    
    // Business context
    private String businessDomain;
    private String platformInterface;
    private String platformVersion;
    private String vendor;
    private String vendorInterface;
    private String vendorVersion;
    private String promptVersion;
    
    // Prompt content files
    private List<PromptFile> promptFiles;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    // Constructors
    public CodePrompt() {}
    
    public CodePrompt(Long fieldMappingId, String businessDomain) {
        this.fieldMappingId = fieldMappingId;
        this.businessDomain = businessDomain;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Long getFieldMappingId() { return fieldMappingId; }
    public void setFieldMappingId(Long fieldMappingId) { this.fieldMappingId = fieldMappingId; }
    
    public String getBusinessDomain() { return businessDomain; }
    public void setBusinessDomain(String businessDomain) { this.businessDomain = businessDomain; }
    
    public String getPlatformInterface() { return platformInterface; }
    public void setPlatformInterface(String platformInterface) { this.platformInterface = platformInterface; }
    
    public String getPlatformVersion() { return platformVersion; }
    public void setPlatformVersion(String platformVersion) { this.platformVersion = platformVersion; }
    
    public String getVendor() { return vendor; }
    public void setVendor(String vendor) { this.vendor = vendor; }
    
    public String getVendorInterface() { return vendorInterface; }
    public void setVendorInterface(String vendorInterface) { this.vendorInterface = vendorInterface; }
    
    public String getVendorVersion() { return vendorVersion; }
    public void setVendorVersion(String vendorVersion) { this.vendorVersion = vendorVersion; }
    
    public String getPromptVersion() { return promptVersion; }
    public void setPromptVersion(String promptVersion) { this.promptVersion = promptVersion; }
    
    public List<PromptFile> getPromptFiles() { return promptFiles; }
    public void setPromptFiles(List<PromptFile> promptFiles) { this.promptFiles = promptFiles; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
}
