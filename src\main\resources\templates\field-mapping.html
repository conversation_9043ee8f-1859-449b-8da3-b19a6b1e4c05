<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('字段映射管理')}"></head>
<body>
    <div class="app-layout">
        <div th:replace="~{fragments/layout :: sidebar}"></div>
        <div class="app-main">
            <div th:replace="~{fragments/layout :: header}"></div>
            <main class="app-content">

                <!-- Page Header -->
                <div class="page-header">
                    <h1 class="page-title">字段映射管理</h1>
                    <p class="page-subtitle">管理平台端和供应商端的字段映射规则</p>
                </div>
                
                <!-- Search and Filter Section -->
                <div class="search-filters">
                    <form id="fieldMappingSearchForm" onsubmit="return false;">
                        <!-- Business Domain Section -->
                        <div class="filter-section">
<!--                            <div class="filter-section-title">业务领域</div>-->
                            <div class="filter-single">
                                <div th:replace="~{fragments/components :: form-select('businessDomain', 'businessDomain', '业务领域', ${businessDomains}, '', '请选择业务领域', false)}"></div>
                            </div>
                        </div>

                        <!-- Platform and Vendor Groups -->
                        <div class="filter-groups">
                            <!-- Platform Group -->
                            <div class="filter-group platform-group">
                                <div class="filter-group-title">平台端信息</div>
                                <div class="filter-group-content">
                                    <div th:replace="~{fragments/components :: form-select('platformInterface', 'platformInterface', '平台端接口', ${platformInterfaces}, '', '请选择平台端接口', false)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('platformVersion', 'platformVersion', '平台端版本', ${platformVersions}, '', '请选择平台端版本', false)}"></div>
                                </div>
                            </div>

                            <!-- Vendor Group -->
                            <div class="filter-group vendor-group">
                                <div class="filter-group-title">供应商端信息</div>
                                <div class="filter-group-content">
                                    <div th:replace="~{fragments/components :: form-select('vendor', 'vendor', '供应商', ${vendors}, '', '请选择供应商', false)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('vendorInterface', 'vendorInterface', '供应商接口', ${vendorInterfaces}, '', '请选择供应商端接口', false)}"></div>
                                    <div th:replace="~{fragments/components :: form-select('vendorVersion', 'vendorVersion', '供应商版本', ${vendorVersions}, '', '请选择供应商端版本', false)}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-actions">
                            <button type="button" class="btn btn-primary" onclick="searchFieldMapping()">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                查询
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Field Mapping Content -->
                <div id="fieldMappingContent" style="display: none;">
                    <!-- General Logic Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3 class="card-title">通用逻辑</h3>
                        </div>
                        <div class="card-body">
                            <div th:replace="~{fragments/components :: form-textarea('generalLogic', 'generalLogic', '', '', '请输入通用逻辑...', false, false, 6, null)}"></div>
                        </div>
                    </div>
                    
                    <!-- Mapping Rules Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">映射规则</h3>
                            <div class="card-actions">
                                <button type="button" class="btn btn-success" onclick="generateMapping()">
                                    生成映射
                                </button>
                                <button type="button" class="btn btn-info" onclick="generateCodePrompt()">
                                    生成 CodePrompt
                                </button>
                                <button type="button" class="btn btn-warning" onclick="saveStage()">
                                    暂存
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="data-table-container">
                                <table class="data-table" id="mappingRulesTable">
                                    <thead>
                                        <tr>
                                            <th style="width: 20%;">IFS 字段</th>
                                            <th style="width: 30%;">航空公司响应结果</th>
                                            <th style="width: 25%;">补充逻辑</th>
                                            <th style="width: 25%;">映射规则</th>
                                        </tr>
                                    </thead>
                                    <tbody id="mappingRulesTableBody">
                                        <!-- Mapping rules will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Empty state for no selection -->
                <div id="noSelectionState" class="empty-state">
                    <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                    <h3 class="empty-state-title">请选择映射关联</h3>
                    <p class="empty-state-description">请在上方选择业务领域、平台端和供应商端信息，然后点击查询按钮</p>
                </div>
            </main>
        </div>
    </div>
    
    <!-- JSON Tree Selector Modal -->
    <div id="jsonTreeSelector" class="json-tree-selector">
        <div class="json-tree-panel">
            <div class="json-tree-header">
                <h3>JSON 路径选择器</h3>
                <button type="button" class="btn btn-ghost" onclick="closeJsonTreeSelector()">
                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="json-tree-search">
                <input type="text" class="form-control" id="jsonTreeSearchInput" placeholder="搜索节点..." onkeyup="searchJsonNodes()">
            </div>
            <div class="json-tree-content">
                <div id="jsonTreeContainer" class="json-tree">
                    <!-- JSON tree will be rendered here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeJsonTreeSelector()">
                    取消
                </button>
                <button type="button" class="btn btn-primary" onclick="confirmJsonSelection()">
                    确认选择
                </button>
            </div>
        </div>
    </div>
    
    <div th:replace="~{fragments/layout :: scripts}"></div>
    <script th:src="@{/js/field-mapping.js}"></script>
</body>
</html>
